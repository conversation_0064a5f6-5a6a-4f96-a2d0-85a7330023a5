require 'rails_helper'

RSpec.describe GetContactWithPicklistValues do
  describe "Get Contact Data" do
    before do
      stub_entity_labels_api
      @contact_id = 11320
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      let(:contact_data) { file_fixture('contact-data.json').read }
      let(:standard_picklist) { file_fixture('standard-picklist.json').read }
      let(:user_data){ file_fixture('user-profile-response.json').read }
      let(:contact_fields){ file_fixture('contact-fields.json').read }
      let(:processed_contact_data){ JSON.parse(file_fixture('processed-contact-data.json').read) }

      before(:each) do
        stub_request(:get, SERVICE_SALES + "/v1/contacts/#{@contact_id}").
        with(
          headers: {
          "Authorization" => "Bearer #{@token}"
          }).
        to_return(status: 200, body: contact_data, headers: {})

        stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
        with(
          headers: {
          "Authorization" => "Bearer #{@token}"
          }).
        to_return(status: 200, body: standard_picklist, headers: {})

        stub_request(:get, SERVICE_IAM + "/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }).
            to_return(status: 200, body: user_data, headers: {})

        stub_request(:get, SERVICE_CONFIG + "/v1/entities/contact/fields?entityType=contact&custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
            "Authorization" => "Bearer #{@token}"
            }).
          to_return(status: 200, body: contact_fields, headers: {})
      end

      it "returns processed contact data in output" do
        command = GetContactWithPicklistValues.call @contact_id
        expect(command.result.first).to eq(processed_contact_data)
        expect(command.result.last).to eq({ "createdBy" => 11, "updatedBy" => 11, "ownerId" => 11, "importedBy" => 11 })
      end
    end
  end
end
