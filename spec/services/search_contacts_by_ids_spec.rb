# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SearchContactsByIds do
  describe "#call" do
    let(:token) { build(:auth_token, user_id: 9, tenant_id: 10).token }

    context 'when contact id email map present' do
      let(:contact_response) {
        {
          content: [
            {
              firstName: '<PERSON>',
              lastName: 'Stark',
              emails: [{ type: 'OFFICE', value: '<EMAIL>', primary: true }],
              ownerId: 9,
              id: 1234,
              associatedDeals: [123],
              recordActions: {
                email: true
              }
            }
          ]
        }
      }

      before { Thread.current[:token] = token }

      it "returns array of contact details" do
        rules = [{ field: 'id', id: 'id', operator: 'in', relatedFieldIds: nil, type: 'double', value: '1234' }]
        payload = { fields: %w[id firstName lastName emails associatedDeals ownerId], jsonRule: { rules: rules, condition: 'OR', valid: true } }

        stub_request(:post, "#{SERVICE_SEARCH}/v1/search/contact?sort=updatedAt,desc&page=0&size=1000").
          with(
            headers: {
              Authorization: "Bearer #{token}"
            },
            body: payload.to_json
          ).
          to_return(status: 200, body: contact_response.to_json)

        command = described_class.call({ 1234 => '<EMAIL>' }, 10)

        expect(command.result).to match_array([{ entity: 'contact_1234', email: '<EMAIL>', name: 'Tony Stark', tenant_id: 10, owner_id: 9, associated_deals: [123] }])
      end
    end

    context 'when contact id email map is blank' do
      it 'returns blank array' do
        expect(described_class.call({}, 10).result).to eq([])
      end
    end
  end
end
