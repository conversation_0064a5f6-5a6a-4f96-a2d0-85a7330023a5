# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe ConnectedAccountDeactivatedPublisher do
  describe '#call' do
    before do
      BunnyMock.use_bunny_queue_pop_api = true
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic(EMAIL_EXCHANGE)
      params = {
        tenantId: 123,
        userId: 234,
        disconnectedBy: 'AUTO',
        userEmail: '<EMAIL>',
        tenantEmail: '<EMAIL>'
      }
      @event = Event::ConnectedAccountDeactivated.new(params)
    end

    context 'When publisher is called with Oauth account' do
      it 'raises event for account deactivated in oauth queue' do
        @queue = @channel.queue(EMAIL_OAUTH_DISCONNECTED)
        @queue.bind(@exchange, routing_key: EMAIL_OAUTH_DISCONNECTED)
        allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)

        ConnectedAccountDeactivatedPublisher.call(123, 234, 'AUTO', EMAIL_OAUTH, '<EMAIL>', '<EMAIL>', '<EMAIL>')
        expect(@queue.message_count).to eq(1)
        payload = @queue.pop
        expect(JSON.parse(payload.last)).to eq(
          {
            'tenantId' => 123,
            'userId' => 234,
            'disconnectedBy' => 'AUTO',
            'userEmail' => '<EMAIL>',
            'tenantEmail' => '<EMAIL>',
            'connectedEmail' => '<EMAIL>'
          }
        )
      end
    end

    context 'When publisher is called with email client account' do
      it 'raises event for account deactivated in email client queue' do
        @queue = @channel.queue(EMAIL_CLIENT_DISCONNECTED)
        @queue.bind(@exchange, routing_key: EMAIL_CLIENT_DISCONNECTED)
        allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)

        ConnectedAccountDeactivatedPublisher.call(123, 234, 'AUTO', EMAIL_CLIENT, '<EMAIL>', '<EMAIL>','<EMAIL>')
        expect(@queue.message_count).to eq(1)
        payload = @queue.pop
        expect(JSON.parse(payload.last)).to eq(
          {
            'tenantId' => 123,
            'userId' => 234,
            'disconnectedBy' => 'AUTO',
            'userEmail' => '<EMAIL>',
            'tenantEmail' => '<EMAIL>',
            'connectedEmail' => '<EMAIL>'
          }
        )
      end
    end
  end
end
