require 'rails_helper'

RSpec.describe GetDeal do
  describe "#call" do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @deal = build(:look_up, entity_type: LOOKUP_DEAL, entity_id: 9, tenant_id: 10)
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      it "returns deal in output" do
        stub_request(:get, SERVICE_DEAL + "/v1/deals/#{@deal.entity_id}").
        with(
          headers: {
          "Authorization" => "Bearer #{ @token }"
          }).
        to_return(status: 200, body: { "id": @deal.entity_id, "ownerId": 1, "recordActions": { "read": true, "update": true, "delete": true, "email": true, "call": false, "sms": false, "task": true, "note": true, "meeting": true }}.to_json, headers: {})

        result = GetDeal.call(@deal.entity_id).result

        expect(result['id']).to eq(@deal.entity_id)
        expect(result.has_key?('recordActions')).to be true
      end
    end

    context "with invalid input - " do
      context "when deal is not available" do
        it "raises 404 not found error" do
          stub_request(:get, SERVICE_DEAL + "/v1/deals/#{@deal.entity_id}").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 404, body: "", headers: {})

          expect { GetDeal.call(@deal.entity_id).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      context "when something went wrong while API processing" do
        it "raises 500 error" do
          stub_request(:get, SERVICE_DEAL + "/v1/deals/#{@deal.entity_id}").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 500, body: "", headers: {})

          expect { GetDeal.call(@deal.entity_id).result }.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        end
      end

      context "when Api request invalid" do
        it "raises 400 error" do
          stub_request(:get, SERVICE_DEAL + "/v1/deals/#{@deal.entity_id}").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 400, body: "", headers: {})

          expect { GetDeal.call(@deal.entity_id).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end
    end
  end
end