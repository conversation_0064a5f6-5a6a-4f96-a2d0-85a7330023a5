require 'rails_helper'
require 'bunny-mock'

RSpec.describe LinkClickedEventPublisher do
  describe '#call' do
    before do
      BunnyMock.use_bunny_queue_pop_api = true
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic EMAIL_EXCHANGE

      @event = Event::LinkClickedAction.new({})

      @link = create(:link_mapping)
      @queue = @channel.queue "email.link.clicked.notify.user"
      @queue.bind @exchange, routing_key: "email.link.clicked.notify.user"
      allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)

      context "when link is clicked" do
        before { ProcessLinkMapping.call(@link.id) }

        it 'raises an event in the Email Exchange & routes it to queue email.link.clicked.notify.user' do
          expect(@queue.message_count).to eq(1)
        end
      end
    end
  end
end
