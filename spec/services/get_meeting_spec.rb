# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetMeeting do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    def stub_get_meeting_details_request(status: 200, body: file_fixture('meeting-details.json'), meeting_id: 'meeting-id')
      stub_request(:get, "#{SERVICE_MEETING}/v1/meetings/#{meeting_id}").
      with(
        headers: {
          'Authorization' => "Bearer #{@token}"
          }).
        to_return(status: status, body: body, headers: {}
      )
    end

    context 'with valid input' do
      it 'returns meeting details' do
        stub_get_meeting_details_request
        result = GetMeeting.call('meeting-id').result
        expect(result['id']).to be_present
      end
    end

    context 'with invalid input' do
      context 'when data not found' do
        it 'raises invalid data error and logs proper message' do
          allow(Rails.logger).to receive(:error).with(/Get Meeting - 404/)
          stub_get_meeting_details_request(status: 404)
          expect { GetMeeting.call('meeting-id') }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      context 'when internal server error' do
        it 'raises error and logs proper message' do
          allow(Rails.logger).to receive(:error).with(/Get Meeting - 500/)
          stub_get_meeting_details_request(status: 500)
          expect { GetMeeting.call('meeting-id') }.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        end
      end
    end
  end
end
