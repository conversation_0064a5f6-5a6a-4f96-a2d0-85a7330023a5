require 'rails_helper'
require 'bunny-mock'

RSpec.describe BuildDataForEmailUnrelatedPublisher do
  describe '#call' do
    before do
      allow(EmailMetadataPublisher).to receive(:call).and_return nil
      @user = create(:user)
      user_look_up = create(:look_up, entity_type: LOOKUP_USER, entity_id: @user.id)
      @another_user = create(:user, tenant_id: @user.tenant_id)
      thread = create(:email_thread, owner: @user, tenant_id: @user.tenant_id)
      @email = create(:email, owner_id: @user.id, sender: user_look_up, email_thread_id: thread.id)
      @contact = create(:look_up, entity_type: LOOKUP_CONTACT)
      @related_to = create(:look_up, entity_type: <PERSON><PERSON><PERSON><PERSON>_DEAL)
      @email.to_recipients << @contact
      @email.related_to << @related_to
      @auth_data = build(:auth_data, :contact_with_read_email_access, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

      @token = JWT.encode @auth_data, nil, 'none'
      thread = Thread.current
      thread[:token] = @token

      stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?page=0&size=30").
        with(
          body: { "fields"=>["id"], "jsonRule"=>{"condition"=>"AND", "rules"=>[{"operator"=>"equal", "id"=>"associatedDeals", "field"=>"associatedDeals", "type"=>"long", "value"=> @related_to.entity_id }], "valid"=>true}}.to_json,
          headers: {
            :Authorization => "Bearer "+ @token,
            :content_type => "application/json"
          }).
          to_return(status: 200, body: { "content": [{ "id": @contact.entity_id, "ownerId": @another_user.id } ]}.to_json, headers: {})
    end

    context "when Email related to Deal" do
      it 'returns correct data' do
        result = described_class.call(@email.email_thread.emails).result
        expect(result).not_to be_blank
        expected = [{:owner=>{:id=>@another_user.id, :tenant_id=>@email.tenant_id}, :user_id=>@email.sender.id, :related_to_id=>@contact.entity_id, :email_id=>@email.id}]
        expect(result).to eq expected
      end
    end

    context "when contact is not associated with deal" do
      before do
        stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?page=0&size=30").
          with(
            body: { "fields"=>["id"], "jsonRule"=>{"condition"=>"AND", "rules"=>[{"operator"=>"equal", "id"=>"associatedDeals", "field"=>"associatedDeals", "type"=>"long", "value"=> @related_to.entity_id }], "valid"=> true }}.to_json,
            headers: {
              :Authorization => "Bearer "+ @token,
              :content_type => "application/json"
            }).
            to_return(status: 200, body: { "content": []}.to_json, headers: {})

      end

      it 'returns empty array' do
        result = described_class.call(@email.email_thread.emails).result
        expect(result).to eq []
      end
    end

    context 'when multiple users in recipients' do
      let(:entity) { LOOKUP_DEAL }
      before do
        @email.cc_recipients << build(:look_up, entity: 'user_33',tenant_id: @email.tenant_id)
        @email.save
        @result = described_class.call(@email.email_thread.emails).result
      end
      it 'should return data for both users' do
        expect(@result.count).to eq(2)
      end
    end
  end
end
