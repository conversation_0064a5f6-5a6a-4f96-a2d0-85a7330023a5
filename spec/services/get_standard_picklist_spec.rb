require 'rails_helper'

RSpec.describe GetStandardPicklist do
  describe '#call' do
    before do
      token = build(:auth_token)
      Thread.current[:token] = token
      described_class.standard_picklist = nil

      stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
        with(
          headers: {
            "Authorization" => "Bearer #{token}"
          }).
        to_return(status: 200, body: file_fixture('standard-picklist.json'), headers: {})
    end

    context "within same request if #{described_class} is called multiple times" do
      it 'should call fetch data from API only once' do
        expect(Rails.logger).to receive(:info).with('GetStandardPicklist called').once

        described_class.call
        described_class.call
        described_class.call
        described_class.call
      end

      it 'should not fetch data from API again' do
        described_class.call
        described_class.call
        described_class.call
        described_class.call
      end
    end
  end
end
