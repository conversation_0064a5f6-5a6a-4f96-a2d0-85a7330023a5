require 'rails_helper'

RSpec.describe GetDealWithPicklistValues do
  describe "Get Deal Data" do
    before do
      stub_entity_labels_api
      @deal_id = 1815
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      let(:deal_data) { file_fixture('deal-data.json').read }
      let(:standard_picklist) { file_fixture('standard-picklist.json').read }
      let(:user_data){ file_fixture('user-profile-response.json').read }
      let(:deal_fields){ file_fixture('deal-fields.json').read }
      let(:processed_deal_data){ JSON.parse(file_fixture('processed-deal-data.json').read) }

      before(:each) do
        stub_request(:get, SERVICE_DEAL + "/v1/deals/#{@deal_id}").
        with(
          headers: {
          "Authorization" => "Bearer #{@token}"
          }).
        to_return(status: 200, body: deal_data, headers: {})

        stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
        with(
          headers: {
          "Authorization" => "Bearer #{@token}"
          }).
        to_return(status: 200, body: standard_picklist, headers: {})

        stub_request(:get, SERVICE_IAM + "/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }).
            to_return(status: 200, body: user_data, headers: {})

        stub_request(:get, SERVICE_DEAL + "/v1/deals/fields?custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
            "Authorization" => "Bearer #{@token}"
            }).
          to_return(status: 200, body: deal_fields, headers: {})
      end

      it "returns processed deal data in output" do
        command = GetDealWithPicklistValues.call @deal_id
        expect(command.result.first).to eq(processed_deal_data)
        expect(command.result.last).to eq({ "createdBy" => 11, "updatedBy" => 11, "ownedBy" => 11 })
      end
    end
  end
end
