require 'rails_helper'
require 'bunny-mock'

RSpec.describe ProcessLinkMapping do
  describe '#call' do

    before do
      connection = BunnyMock.new
      channel = connection.start.channel
      exchange = channel.topic EMAIL_EXCHANGE

      queue = channel.queue "email.link.clicked.notify.user"
      queue.bind exchange, routing_key: "email.link.clicked.notify.user"
      allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(queue)
    end


    context 'when sent link is tracked' do
      let!(:link_mapping) { create(:link_mapping) }

      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::LinkClickedAction)).exactly(1).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailUpdatedV2)).exactly(1).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailClicked)).exactly(1).times

        ProcessLinkMapping.call(link_mapping.id)
      end

      it 'create email_link_log' do
        expect(EmailLinkLog.count).to eq 1
        expect(EmailLinkLog.last.email).to eq link_mapping.email
        expect(EmailLinkLog.last.tenant).to eq link_mapping.tenant
      end
    end

    context 'when tracked email is deleted (i.e link mapping does not exist)' do
      it 'returns nil' do
        res = ProcessLinkMapping.call(123).result
        expect(res).to eq nil
        expect(EmailLinkLog.count).to eq 0
      end
    end
  end
end

