require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForUserUpdateV2 do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic USER_EXCHANGE
    end

    context 'valid input' do
      before do

        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(USER_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(USER_EXCHANGE, USER_UPDATED_V2_EVENT, USER_UPDATED_V2_QUEUE)
          .and_yield(payload.to_s)
        ListenForUserUpdateV2.call()
      end

      context 'user name updated event' do
        let(:routing_key) { USER_NAME_UPDATED_EVENT }
        let(:payload)     {
          {
            "entity": {
              "id": @user.id,
              "profile": {
                "id": 2,
                "name": "Admin"
              }
            }
          }.to_json
        }

        it 'updates the User profile id' do
          user = User.find(@user.id)
          expect(user.reload.profile_id).to eql 2
        end
      end
    end
  end
end
