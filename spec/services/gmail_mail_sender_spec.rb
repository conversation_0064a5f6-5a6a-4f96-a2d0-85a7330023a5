require 'rails_helper'

RSpec.describe GmailMailSender do
  describe '#call' do
    before do
      user = create(:user)
      thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
      @connected_account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i)
      @email = create(:email, owner: user, connected_account: @connected_account, email_thread_id: thread.id)

      setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', @connected_account.email).and_return(setting)
    end

    context 'Send Email' do
      before do
        return_auth_object = Signet::OAuth2::Client.new(access_token: 'NEWTOK', expires_at: DateTime.now + 1.hour)
        allow_any_instance_of(Signet::OAuth2::Client).to receive(:fetch_access_token!).and_return(return_auth_object)
      end

      context 'Success' do
        before do
          header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
          payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
          return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
          allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
          allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
          @res = SendEmail.call({ email: @email }).result
        end

        it 'Returns thread id and source id' do
          expect(@res.thread_id).to eq 'THREAD_ID'
          expect(@res.id).to eq 'SOURCE_ID'
        end
      end

      context 'success with attachment' do
        before do
          file = File.new("#{Rails.root}/tmp/sampleFile.png", 'wb')
          file.write("someValue")
          file.pos = 0
          attachments = [HashWithIndifferentAccess.new({
            type: 'inline',
            content_id: '12345',
            fileName: 'sampleFile.png',
            data: file
          })]
          header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
          payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
          return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
          allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
          expect(StringIO).to receive(:new).with(/Content-Type: multipart\/related.*X-Attachment-Id: 12345/m).and_call_original
          expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).with('me', { thread_id: nil }, upload_source: instance_of(StringIO), content_type: 'message/rfc822').and_return(return_message)
          @res = SendEmail.call({ email: @email, attachments: attachments }).result
        end

        after do
          File.delete("#{Rails.root}/tmp/sampleFile.png")
        end

        it 'returns thread id and source id' do
          expect(@res.thread_id).to eq('THREAD_ID')
          expect(@res.id).to eq('SOURCE_ID')
        end
      end
    end

    context 'failure' do
      it 'raises third party api auth error' do
        expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_raise(Google::Apis::AuthorizationError.new("error"))
        expect{ SendEmail.call({ email: @email }) }.to raise_error(ExceptionHandler::ThirdPartyAPIAuthError, ErrorCode.unauthorized)
      end

      it 'raises third party api error for google server error' do
        expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_raise(Google::Apis::ServerError.new("error"))
        expect{ SendEmail.call({ email: @email }) }.to raise_error(ExceptionHandler::ThirdPartyAPIError)
      end

      it 'raises third party api error for google client error' do
        expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_raise(Google::Apis::ClientError.new("error"))
        expect{ SendEmail.call({ email: @email }) }.to raise_error(ExceptionHandler::ThirdPartyAPIError)
      end

      it 'raises rate limit error' do
        expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_raise(Google::Apis::RateLimitError.new("userRateLimitExceeded"))
        expect{ SendEmail.call({ email: @email }) }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.rate_limit_error)
      end
    end
  end
end
