# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetMeetingWithPicklistValues do
  describe "Get Meeting Data" do
    before do
      stub_entity_labels_api
      @meeting_id = 5884
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'with valid input' do
      before(:each) do
        stub_request(:get, "#{SERVICE_IAM}/v1/users/me").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}",
            'Accept'=>'application/json',
            'Content-Type'=>'application/json'
          }).
          to_return(status: 200, body: file_fixture('user-profile-response.json'), headers: {})

        stub_request(:get, "#{SERVICE_MEETING}/v1/meetings/fields").
          with(
            headers: {
              'Authorization' => "Bearer #{@token}"
              }).
            to_return(status: 200, body: file_fixture('meeting-fields.json'), headers: {}
          )

        stub_request(:get, "#{SERVICE_MEETING}/v1/meetings/#{@meeting_id}").
          with(
            headers: {
              'Authorization' => "Bearer #{@token}"
              }).
            to_return(status: 200, body: file_fixture('meeting-details.json'), headers: {}
          )

        stub_request(:get, "#{SERVICE_CONFIG}/v1/picklists/standard").
          with(
            headers: {
            "Authorization" => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('standard-picklist.json'), headers: {})
      end

      it 'returns processed meeting data in output' do
        command = GetMeetingWithPicklistValues.call(@meeting_id)
        expect(command.result.first).to eq(JSON.parse(file_fixture('processed-meeting-data.json').read))
        expect(command.result.last).to eq({ "createdBy" => 6781, "updatedBy" => 6781, "owner" => 6781, "cancelledBy" => nil, "conductedBy" => nil })
      end
    end
  end
end
