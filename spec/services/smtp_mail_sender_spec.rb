require 'rails_helper'

RSpec.describe SmtpMailSender do
  describe '#call' do
    before do
      user = create(:user)
      @connected_account = create(:connected_account, access_token: 'test', provider_name: CUSTOM_PROVIDER)
      @email = create(:email, owner: user, connected_account: @connected_account, cc: [], bcc:[], to: ['<EMAIL>'])
    end

    context 'Send Email' do
      before do
        @response = {
          "response": "Queued for delivery",
          "messageId": "<<EMAIL>>",
          "sendAt": "2021-10-06T03:11:19.272Z",
          "queueId": "f425bf8c1307d655"
        }

        @payload = {
          "from": {
            "address": @email.from
          },
          "to": [
            {
              "address": @email.to.first
            }
          ],
          "cc": [],
          "bcc": [],
          "subject": @email.subject,
          "html": @email.body,
          "attachments": []
        }
      end

      context 'Success' do
        before do
          stub_request(:post, "#{SERVICE_EMAIL_ENGINE}/v1/account/#{@connected_account.access_token}/submit").
            with(
              body: @payload.to_json
            ).
            to_return(status: 200, body: @response.to_json, headers: {})

            @res = SmtpMailSender.call({ email: @email }).result
        end

        it 'returns source id' do
          expect(@res.id).to eq @response[:messageId]
        end
      end

      context 'Failure' do
        def stub_invalid_request(payload, token, status)
          stub_request(:post, "#{SERVICE_EMAIL_ENGINE}/v1/account/#{token}/submit").
            with(
              body: payload.to_json
            ).
            to_return(status: status, body: '', headers: {})
        end

        context 'when unauthorized request' do
          before { stub_invalid_request(@payload, @connected_account.access_token, 401) }

          it 'raises authentication error' do
            expect(Rails.logger).to receive(:error).with("EmailEngine: send 401 : ")
            expect { SmtpMailSender.call({ email: @email }).result }.to raise_error(ExceptionHandler::AuthenticationError, ErrorCode.smtp_unauthorized)
          end
        end

        context 'when unsupported media type' do
          before { stub_invalid_request(@payload, @connected_account.access_token, 415) }

          it 'raises unsupported media type error' do
            expect(Rails.logger).to receive(:error).with("EmailEngine: send 415 : ")
            expect { SmtpMailSender.call({ email: @email }).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.unsupported_media_type)
          end
        end

        context 'when email thread does not exist' do
          before { stub_invalid_request(@payload, @connected_account.access_token, 404) }

          it 'raises unsupported media type error' do
            expect(Rails.logger).to receive(:error).with("EmailEngine: send 404 :  |  | 404 Not Found").exactly(2).times
            expect { SmtpMailSender.call({ email: @email }).result }.to raise_error(ExceptionHandler::EmailThreadNotFoundError, ErrorCode.email_thread_not_found)
          end
        end

        context 'when too many requests' do
          before { stub_invalid_request(@payload, @connected_account.access_token, 429) }

          it 'raises invalid error with rate limit error code' do
            expect(Rails.logger).to receive(:error).with("EmailEngine: send 429")
            expect { SmtpMailSender.call({ email: @email }).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.rate_limit_error)
          end
        end

        context 'when internal server error' do
          before { stub_invalid_request(@payload, @connected_account.access_token, 500) }

          it 'raises internal server error' do
            expect(Rails.logger).to receive(:error).with("EmailEngine: send 500 | 500 Internal Server Error").exactly(2).times
            expect { SmtpMailSender.call({ email: @email }).result }.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
          end
        end

        context 'when http method not implemented' do
          before { stub_invalid_request(@payload, @connected_account.access_token, 501) }

          it 'raises unsupported media type error' do
            expect(Rails.logger).to receive(:error).with("EmailEngine: send 501")
            expect { SmtpMailSender.call({ email: @email }).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.not_implemented)
          end
        end

        context 'when bad gateway' do
          before { stub_invalid_request(@payload, @connected_account.access_token, 502) }

          it 'raises unsupported media type error' do
            expect(Rails.logger).to receive(:error).with("EmailEngine: send 502").exactly(2).times
            expect { SmtpMailSender.call({ email: @email }).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.bad_gateway)
          end
        end

        context 'when service unavailable' do
          before { stub_invalid_request(@payload, @connected_account.access_token, 503) }

          it 'raises unsupported media type error' do
            expect(Rails.logger).to receive(:error).with("EmailEngine: send 503").exactly(2).times
            expect { SmtpMailSender.call({ email: @email }).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.service_unavailable)
          end
        end

        context 'when gateway timeout' do
          before { stub_invalid_request(@payload, @connected_account.access_token, 504) }

          it 'raises unsupported media type error' do
            expect(Rails.logger).to receive(:error).with("EmailEngine: send 504").exactly(2).times
            expect { SmtpMailSender.call({ email: @email }).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.gateway_timeout)
          end
        end
      end
    end

    context 'success with inline attachments' do
      before do
        @response = {
          "response": "Queued for delivery",
          "messageId": "<<EMAIL>>",
          "sendAt": "2021-10-06T03:11:19.272Z",
          "queueId": "f425bf8c1307d655"
        }

        attachment_file = File.new("#{Rails.root}/tmp/sampleFile.png", 'wb')

        def attachment_file.content_type
          'image/png'
        end

        attachments = [HashWithIndifferentAccess.new({
          type: 'inline',
          content_id: '12345',
          fileName: 'sampleFile.png',
          data: attachment_file
        })];

        payload = {
          "from": {
            "address": @email.from
          },
          "to": [{
            "address": @email.to.first
          }],
          "cc": [],
          "bcc": [],
          "subject": @email.subject,
          "html": @email.body,
          "attachments": [{
            contentType: 'image/png',
            contentDisposition: 'inline',
            encoding: 'base64',
            content: '',
            filename: 'sampleFile.png',
            cid: '12345'
          }]
        }

        stub_request(:post, "#{SERVICE_EMAIL_ENGINE}/v1/account/#{@connected_account.access_token}/submit").
          with(
            body: payload.to_json).
            to_return(status: 200, body: @response.to_json, headers: {})

        @res = SmtpMailSender.call({ email: @email, attachments: attachments }).result
      end

      after do
        File.delete("#{Rails.root}/tmp/sampleFile.png")
      end
      it 'returns source id' do
        expect(@res.id).to eq(@response[:messageId])
      end
    end
  end
end
