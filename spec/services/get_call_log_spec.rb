# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetCallLog do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    def stub_get_call_log_details_request(status: 200, body: file_fixture('call-details.json'), call_log_id: 'call-log-id')
      stub_request(:get, "#{SERVICE_CALL}/v1/call-logs/#{call_log_id}").
      with(
        headers: {
          'Authorization' => "Bearer #{@token}"
          }).
        to_return(status: status, body: body, headers: {}
      )
    end

    context 'with valid input' do
      it 'returns call log details' do
        stub_get_call_log_details_request
        result = GetCallLog.call('call-log-id').result
        expect(result['id']).to be_present
      end
    end

    context 'with invalid input' do
      context 'when data not found' do
        it 'raises invalid data error and logs proper message' do
          allow(Rails.logger).to receive(:error).with(/Get CallLog - 404/)
          stub_get_call_log_details_request(status: 404)
          expect { GetCallLog.call('call-log-id') }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      context 'when internal server error' do
        it 'raises error and logs proper message' do
          allow(Rails.logger).to receive(:error).with(/Get CallLog - 500/)
          stub_get_call_log_details_request(status: 500)
          expect { GetCallLog.call('call-log-id') }.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        end
      end
    end
  end
end
