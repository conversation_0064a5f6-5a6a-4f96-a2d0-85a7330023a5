require 'rails_helper'

RSpec.describe UpdateEmailTemplate do
  describe '#call' do
    before do
      allow(TenantEmailTemplateUsagePublisher).to receive(:publish).with(instance_of(Integer))
      @user = create(:user, id: 12, tenant: create(:tenant, id: 99))
      auth_data =  build(:auth_data, permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

      thread = Thread.current
      thread[:auth] = auth_data

      token = FactoryBot.build(:auth_token, user_id: 12, tenant_id: 99).token
      thread[:token] = @token

      @email_template = create(:email_template, name: 't_1', body: "This is test body", tenant_id: @user.tenant_id, created_by: creator_user)

      @file_1_name = 'tenant_99/user_11/22_old_file_1.jpg'
      @file_2_name = 'tenant_99/user_11/22_old_file_2.jpg'
      old_attachment1 = create(:template_attachment, id: 77, email_template: @email_template, file_name: @file_1_name)
      old_attachment2 = create(:template_attachment, id: 78, email_template: @email_template, file_name: @file_2_name)
      @new_attachment = File.new("#{Rails.root}/spec/fixtures/files/test_photo.jpg")

      @params = {
        id: @email_template.id,
        name: 'test template',
        category: 'lead',
        subject: "test {{My Lead - First Name}}",
        body: "some {{My Lead - First Name}} text {{My Lead - Last Name}}",
        attachments: [{id: 77, fileName: '22_old_file.jpg' }]
      }

      allow(UploadFileToS3).to receive(:call).with(@new_attachment.path, /tenant_99\/user_12\/[0-9]+_test_[^>]*.jpg/, S3_EMAIL_TEMPLATE_BUCKET).and_return(nil)

      allow(DeleteFileFromS3).to receive(:call).with([@file_2_name], S3_EMAIL_TEMPLATE_BUCKET).and_return(nil)
      allow(V2::GetVariables).to receive_message_chain(:call, :result)
                        .and_return(JSON([{"id":1,"displayName":"My Lead - First Name","internalName":"firstName","standard":true, "entity": 'lead'},{"id":2,"displayName":"My Lead - Last Name","internalName":"lastName","standard":true, "entity": 'lead'}].to_json))
      @params = ActionController::Parameters.new(@params)
      @params.permit!
    end

    context 'when update permission is present on the email template' do
      let(:permission) { :email_template_with_update_access }

      context "and tried to update email template created by other user in same tenant" do
        let(:creator_user) { create(:user, id: 13, tenant_id: @user.tenant_id) }

        it 'throws error for insufficient permission for update' do
          expect { UpdateEmailTemplate.call(@params) }.to raise_error(ExceptionHandler::UpdateEmailTemplateNotAllowedError).with_message(ErrorCode.update_email_template_not_allowed)
        end
      end

      context "and tried to update email template created by same user" do
        let(:creator_user) { @user }

        include_examples 'an update action'
      end
    end

    context 'when update all permission is present on the email template' do
      let(:permission) { :email_template_with_update_all_access }

      context "when tried to update email template created by other user in same tenant" do
        let(:creator_user) { create(:user, id: 13, tenant_id: @user.tenant_id) }

        include_examples 'an update action'
      end

      context "and tried to update email template created by same user" do
        let(:creator_user) { @user }

        include_examples 'an update action'
      end
    end

    context 'when update/update all permissions are not present on the email template' do
      let(:permission) { :email_template_without_update_access }

      context "and tried to update email template" do
        let(:creator_user) { @user }

        it 'throws error for insufficient permission for update' do
          expect { UpdateEmailTemplate.call(@params) }.to raise_error(ExceptionHandler::UpdateEmailTemplateNotAllowedError).with_message(ErrorCode.update_email_template_not_allowed)
        end
      end
    end
  end
end
