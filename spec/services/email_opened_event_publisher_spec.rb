require 'rails_helper'
require 'bunny-mock'

RSpec.describe EmailOpenedEventPublisher do
  describe '#call' do
    before do
      BunnyMock.use_bunny_queue_pop_api = true
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic EMAIL_EXCHANGE

      @email = create(:email)

      @event = Event::EmailAction.new(@email.id)

      @queue = @channel.queue "email.opened.notify.user"
      @queue.bind @exchange, routing_key: "email.opened.notify.user"
      allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)

      context "when email is opened" do
        before { EmailRelatedToContactEventPublisher.call(@email.id) }

        it 'raises an event in the Email Exchange & routes it to queue email.opened.notify.user' do
          expect(@queue.message_count).to eq(1)
        end
      end
    end
  end
end
