# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetFields::Task do
  describe "#call" do
    before do
      @entity_label = 'Task'
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      let(:task_fields) { file_fixture('task-fields.json') }

      before(:each) do
        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/task/fields?custom-only=false&entityType=task&page=0&size=100&sort=createdAt,asc").
        with(
          headers: {
          "Authorization" => "Bearer #{ @token }"
          }).
        to_return(status: 200, body: task_fields, headers: {})
      end

      it "returns fields in output" do
        result = described_class.call.result.first

        expect(result['id']).to eq(135844)
        expect(result['displayName']).to eq('Task Name')
        expect(result['internalName']).to eq('name')
        expect(result['entity']).to eq('task')
      end
    end

    context "with invalid input - " do
      context "when fields are not available" do
        it "raises 404 not found error" do
          stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/task/fields?custom-only=false&entityType=task&page=0&size=100&sort=createdAt,asc").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 404, body: "", headers: {})

          expect { described_class.call.result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      context "when something went wrong while API processing" do
        it "raises 500 error" do
          stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/task/fields?custom-only=false&entityType=task&page=0&size=100&sort=createdAt,asc").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 500, body: "", headers: {})

          expect { described_class.call.result }.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        end
      end

      context "when Api request invalid" do
        it "raises 400 error" do
          stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/task/fields?custom-only=false&entityType=task&page=0&size=100&sort=createdAt,asc").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 400, body: "", headers: {})

          expect { described_class.call.result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end
    end
  end
end
