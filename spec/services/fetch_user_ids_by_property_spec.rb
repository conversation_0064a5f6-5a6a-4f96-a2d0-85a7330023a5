# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FetchUserIdsByProperty do
  describe '#call' do
    let(:token) { build(:auth_token, user_id: 9, tenant_id: 10).token }
    let(:rule) do
      {
        "operator": 'equal',
        "id": 'userFields',
        "field": 'userFields',
        "type": 'long',
        "value": 123,
        "primaryField": 'user',
        "property": 'teams'
      }.with_indifferent_access
    end

    let(:payload) do
      {
        "fields": [],
        "jsonRule": {
          "id": 'teams',
          "field": 'teams',
          "type": 'long',
          "input": nil,
          "operator": 'equal',
          "value": 123,
          "data": nil,
          "property": nil,
          "primaryField": nil,
          "condition": nil,
          "not": nil,
          "rules": nil,
          "group": false
        }
      }.with_indifferent_access
    end

    let(:stubbed_token_result) { double('GenerateToken', result: token) }

    context 'when json rule is invalid' do
      it 'returns array of user ids' do
        expect(GenerateToken).to receive(:call).with(9, 10).and_return(stubbed_token_result)
        updated_rule = rule
        updated_rule['property'] = 'invalid_field'

        updated_payload = payload
        updated_payload['jsonRule']['id'] = 'invalid_field'
        updated_payload['jsonRule']['field'] = 'invalid_field'

        stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id")
          .with(
            headers: { Authorization: "Bearer #{token}" },
            body: updated_payload.to_json
          )
          .to_return(status: 400)

        expect do
          described_class.call(10, 9, updated_rule)
        end.to raise_error(ExceptionHandler::InvalidDataError, '01603001')
      end
    end

    context 'when json rule is valid' do
      it 'returns array of user ids' do
        expect(GenerateToken).to receive(:call).with(9, 10).and_return(stubbed_token_result)

        stub_request(:post, "#{SERVICE_IAM}/v1/users/search-for-id")
          .with(
            headers: { Authorization: "Bearer #{token}" },
            body: payload.to_json
          )
          .to_return(status: 200, body: [89].to_json)

        command = described_class.call(10, 9, rule)
        expect(command.result).to eq([89])
      end
    end
  end
end
