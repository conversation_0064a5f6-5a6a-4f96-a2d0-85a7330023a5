require 'rails_helper'

RSpec.describe CreateLinkMapping do
  describe '#call' do
    before do
      @body = "<div>I am adding <a href=\"google.com\">google</a> link and <a target=\"_blank\" rel=\"noopener noreferrer\" href=\"https://github.com\">github</a> link and <a target=\"_blank\" rel=\"noopener noreferrer\" href=\"http://www.outlook.com?a=1\">outlook</a> link</div><div>--</div><div>&nbsp;</div> and again I am adding <a href=\"google.com\">google.com</a> link"
    end

    context 'create link mapping' do
      before do
        @email =  create(:email, body: @body)
        @result = CreateLinkMapping.call(@body, @email.id, @email.tenant_id).result
      end

      it 'returns updated body' do
        expect(@result).to include "<div>I am adding <a href=\"http://localhost:3000/v1/link_mappings"
      end

      it 'returns track_mapping record' do
        expect(LinkMapping.count).to eq 3
        expect(LinkMapping.last.email_id).to eq @email.id
        expect(LinkMapping.last.tenant_id).to eq(@email.tenant_id)
      end

      it 'does not change text' do
        expect(@result).to include ">google.com</a>"
      end
    end
  end
end
