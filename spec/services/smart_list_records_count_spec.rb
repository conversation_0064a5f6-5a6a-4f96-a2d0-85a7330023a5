# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SmartListRecordsCount do
  describe '#call' do
    let(:user) { create(:user, tenant: create(:tenant)) }

    before do
      @token = FactoryBot.build(:auth_token, user_id: user.id, tenant_id: user.tenant_id).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'when smart-lists ids are empty' do
      it 'should early returns empty output and not call GetSecurityContext service' do
        expect(GetSecurityContext).not_to receive(:call)

        response = described_class.call([])

        expect(response.result).to be_empty
      end
    end

    context 'when smart-lists ids are present' do
      context 'when input is invalid' do
        context 'when smart-list is not found' do
          it 'raises 404 not found error' do
            stub_request(:get, "#{SERVICE_SEARCH}/v1/search-lists/email?id=108998")
              .with(
                headers: { 'Authorization' => "Bearer #{@token}" }
              )
              .to_return(status: 404, body: '', headers: {})

            expect do
              described_class.call([108_998]).result
            end.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
          end
        end

        context 'when something went wrong while API processing' do
          it 'raises 500 error' do
            stub_request(:get, "#{SERVICE_SEARCH}/v1/search-lists/email?id=108998")
              .with(
                headers: { 'Authorization' => "Bearer #{@token}" }
              )
              .to_return(status: 500, body: '', headers: {})

            expect do
              described_class.call([108_998]).result
            end.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
          end
        end

        context 'when api request is invalid' do
          it 'raises 400 error' do
            stub_request(:get, "#{SERVICE_SEARCH}/v1/search-lists/email?id=108998")
              .with(
                headers: { 'Authorization' => "Bearer #{@token}" }
              )
              .to_return(status: 400, body: '', headers: {})

            expect do
              described_class.call([108_998]).result
            end.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
          end
        end
      end

      context 'when input is valid' do
        let(:expected_smart_list_details_response) { file_fixture('smart-list-details-response.json').read }

        before(:each) do
          connection = BunnyMock.new
          channel = connection.start.channel
          exchange = channel.topic EMAIL_EXCHANGE

          queue = channel.queue "email.update.lead.metaInfo"
          queue.bind exchange, routing_key: "email.update.lead.metaInfo"

          queue = channel.queue "email.update.deal.metaInfo"
          queue.bind exchange, routing_key: "email.update.deal.metaInfo"
          allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(queue)

          @emails = create_list(:email, 3, owner: user, tenant_id: user.tenant_id) do |email|
            email.created_at = (Time.zone.now.in_time_zone('Etc/GMT+12').beginning_of_day + 1.hours).utc
            email.read_by << user.id
            email.save!
          end
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: user.id)
          @cc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)
          @bcc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)

          @emails.each do |e|
            e.to_recipients << @to_lookup
            e.cc_recipients << @cc_lookup
            e.bcc_recipients << @bcc_lookup
          end
        end

        it 'returns expected output' do
          allow_any_instance_of(SharedAccess).to receive(:fetch)
            .and_return(
              {
                accessByOwners: { '3779' => { email: true } },
                accessByRecords: { '3779' => { read: true, email: true } }
              }.with_indifferent_access
            )

          stub_request(:get, "#{SERVICE_SEARCH}/v1/search-lists/email?id=108998")
            .with(headers: { 'Authorization' => "Bearer #{@token}" })
            .to_return(status: 200, body: expected_smart_list_details_response, headers: {})

          result = described_class.call([108_998]).result
          expect(result).to match_array(
            [{ 'id' => 108_998, 'name' => "Today's Emails", 'count' => 3 }]
          )
        end
      end
    end
  end
end
