# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FetchSmartListsDetails do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'when input is invalid' do
      context 'when smart-list is not found' do
        it 'raises 404 not found error' do
          stub_request(:get, "#{SERVICE_SEARCH}/v1/search-lists/email?id=108998")
            .with(
              headers: { 'Authorization' => "Bearer #{@token}" }
            )
            .to_return(status: 404, body: '', headers: {})

          expect { described_class.call([108998]).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      context 'when something went wrong while API processing' do
        it 'raises 500 error' do
          stub_request(:get, "#{SERVICE_SEARCH}/v1/search-lists/email?id=108998")
            .with(
              headers: { 'Authorization' => "Bearer #{@token}" }
            )
            .to_return(status: 500, body: '', headers: {})

          expect do
            described_class.call([108998]).result
          end.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        end
      end

      context 'when api request is invalid' do
        it 'raises 400 error' do
          stub_request(:get, "#{SERVICE_SEARCH}/v1/search-lists/email?id=108998")
            .with(
              headers: { 'Authorization' => "Bearer #{@token}" }
            )
            .to_return(status: 400, body: '', headers: {})

          expect { described_class.call([108998]).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end
    end

    context 'when input is valid' do
      let(:expected_smart_list_details_response) { file_fixture('smart-list-details-response.json').read }

      before(:each) do
        stub_request(:get, "#{SERVICE_SEARCH}/v1/search-lists/email?id=108998")
          .with(headers: { 'Authorization' => "Bearer #{@token}" })
          .to_return(status: 200, body: expected_smart_list_details_response, headers: {})
      end

      it 'returns correct output' do
        result = described_class.call([108998]).result.first
        expect(result.keys).to match_array(
          %w[
            createdAt updatedAt createdBy updatedBy recordActions metaData id
            entityType name description searchRequest systemDefault sort size
          ]
        )
        expect(result['searchRequest'].keys).to match_array(%w[fields jsonRule])
      end
    end
  end
end
