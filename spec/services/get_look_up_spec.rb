require 'rails_helper'

RSpec.describe GetLookUp do
  describe '#call' do
    context 'valid input' do
      context 'for custom mail id' do
       before do
          @look_up = build(:look_up)
          @input = {
            tenant_id: @look_up.tenant_id,
            entity: 'email',
            email: '<EMAIL>'
          }
        end

        it 'returns a look_up object' do
          command = GetLookUp.call(@input)
          expect(
            command.result
          ).to be_truthy
          expect(
            command.result.class
          ) == LookUp
        end
      end

      context 'for existing entity' do
        before do
          @look_up = create(:look_up)
          @input = {
            id: @look_up.entity_id,
            tenant_id: @look_up.tenant_id,
            entity: @look_up.entity_type,
            name: @look_up.name,
            email: @look_up.email
          }
        end

        it 'returns the existing look_up' do
          expect(
            GetLookUp.call(@input).result
          ).to be_eql(@look_up)
        end

        it 'does not create a new look_up' do
          expect {
            GetLookUp.call(@input)
          }.not_to change { LookUp.count }
        end
      end

      context 'for new entity' do
        before do
          @look_up = build(:look_up)
          @input = {
            id: @look_up.entity_id,
            tenant_id: @look_up.tenant_id,
            entity: @look_up.entity_type,
            name: @look_up.name
          }
        end

        it 'returns a look_up object' do
          command = GetLookUp.call(@input)
          expect(
            command.result
          ).to be_truthy
          expect(
            command.result.class
          ) == LookUp
        end

        it 'does not create a new look_up object' do
          expect {
            GetLookUp.call(@input)
          }.not_to change { LookUp.count }
        end
      end
    end

    context 'invalid input' do
      it 'raises error for any missing id' do
        expect{
          GetLookUp.call(
            {
              id:"",
              tenant_id: 1,
              entity:"lead",
              "name":"test"
            }
          )
        }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid
        )
      end
      it 'raises error for any missing tenant_id' do
        expect{
          GetLookUp.call(
            {
              id:"1",
              entity:"",
              "name":"test"
            }
          )
        }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid
        )
      end

      it 'raises error for any missing entity' do
        expect{
          GetLookUp.call(
            {
              id:"1",
              entity:"",
              "name":"test"
            }
          )
        }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid
        )
      end
      it 'raises error for any missing name' do
        expect{
          GetLookUp.call(
            {
              id:"1",
              entity:"lead",
              "name":""
            }
          )
        }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid
        )
      end
    end
  end
end
