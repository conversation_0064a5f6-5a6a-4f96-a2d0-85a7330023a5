# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GmailBounceDetector, type: :service do
  let(:gmail_bounce_message) do
    json = File.read(Rails.root.join("spec/fixtures/files/gmail_delivery_status_email.json"))
    data = JSON.parse(json)
    data = data.deep_transform_keys { |k| k.to_s.underscore.to_sym }

    RecursiveOpenStruct.new(data)
  end

  let(:regular_gmail_message) do
    RecursiveOpenStruct.new({
      id: 'regular123',
      payload: {
        headers: [
          { name: 'Subject', value: 'Regular email' },
          { name: 'From', value: '<EMAIL>' }
        ],
        body: {
          data: Base64.encode64('This is a regular email message.')
        }
      }
    })
  end

  describe '.call' do
    context 'with a Gmail bounce message' do
      subject { GmailBounceDetector.call(gmail_bounce_message) }

      it 'detects the message as a bounce' do
        expect(subject[:is_bounced]).to be true
      end

      it 'determines the correct bounce type' do
        expect(subject[:bounce_type]).to eq('hard')
      end

      it 'extracts the failed reason' do
        expect(subject[:failed_reason]).to include('550')
      end
    end

    context 'with a regular Gmail message' do
      subject { GmailBounceDetector.call(regular_gmail_message) }

      it 'does not detect the message as a bounce' do
        expect(subject[:is_bounced]).to be false
      end
    end

    context 'with soft bounce indicators' do
      let(:soft_bounce_message) do
        RecursiveOpenStruct.new({
          id: 'soft123',
          payload: {
            headers: [
              { name: 'Subject', value: 'Delivery Status Notification (Delay)' },
              { name: 'From', value: '<EMAIL>' }
            ],
            body: {
              data: Base64.encode64('421 4.2.1 Mailbox temporarily unavailable. Try again later.')
            }
          }
        })
      end

      subject { GmailBounceDetector.call(soft_bounce_message) }

      it 'detects as soft bounce' do
        expect(subject[:bounce_type]).to eq('soft')
      end
    end
  end

  describe '#bounce_by_sender?' do
    let(:detector) { GmailBounceDetector.new(gmail_bounce_message) }

    it 'detects bounce by sender patterns' do
      expect(detector.send(:bounce_by_sender?)).to be true
    end
  end

  describe '#determine_bounce_type' do
    let(:detector) { GmailBounceDetector.new(gmail_bounce_message) }

    it 'correctly identifies hard bounces' do
      expect(detector.send(:determine_bounce_type)).to eq('hard')
    end

    context 'with soft bounce patterns' do
      let(:soft_bounce_message) do
        RecursiveOpenStruct.new({
          payload: {
            headers: [
              { name: 'Subject', value: 'Delivery Status Notification (Delay)' }
            ],
            body: {
              data: Base64.encode64('450 Requested action not taken: mailbox unavailable')
            }
          }
        })
      end
      let(:detector) { GmailBounceDetector.new(soft_bounce_message) }

      it 'correctly identifies soft bounces' do
        expect(detector.send(:determine_bounce_type)).to eq('soft')
      end
    end
  end

  describe '#extract_failed_reason' do
    let(:detector) { GmailBounceDetector.new(gmail_bounce_message) }

    it 'extracts the failure reason' do
      reason = detector.send(:extract_failed_reason)
      expect(reason).to include('550')
    end
  end
end
