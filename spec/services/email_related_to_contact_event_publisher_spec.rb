require 'rails_helper'
require 'bunny-mock'

RSpec.describe EmailRelatedToContactEventPublisher do
  describe '#call' do
    before do
      BunnyMock.use_bunny_queue_pop_api = true
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic EMAIL_EXCHANGE

      @user = create(:user)
      user_look_up = create(:look_up, entity_type: LOOKUP_USER, entity_id: @user.id)
      another_user = create(:user, tenant_id: @user.tenant_id)

      thread = create(:email_thread, owner: @user, tenant_id: @user.tenant_id)
      @email = create(:email, owner_id: @user.id, sender: user_look_up, email_thread_id: thread.id)
      contact = build(:look_up, entity_type: LOOKUP_CONTACT)
      @related_to = build(:look_up, entity_type: entity)

      @params = ActionController::Parameters.new({ to: [{ entity: 'contact', id: contact.entity_id.to_s, name: contact.name, email: contact.email}],
                                                  relatedTo: { entity: @related_to.entity_type, id: @related_to.entity_id } })
      @auth_data = build(:auth_data, :contact_with_read_email_access, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

      @token = JWT.encode @auth_data, nil, 'none'
      thread = Thread.current
      thread[:token] = @token
      @event = Event::EmailRelatedToContact.new(another_user, user_look_up.entity_id, contact.entity_id.to_s, @email.id)

      @queue = @channel.queue "email.relatedto.contact"
      @queue.bind @exchange, routing_key: "email.relatedto.contact"
      allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)

      stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?page=0&size=30").
        with(
          body: { "fields"=>["id"], "jsonRule"=>{"condition"=>"AND", "rules"=>[{"operator"=>"equal", "id"=>"associatedDeals", "field"=>"associatedDeals", "type"=>"long", "value"=> @related_to.entity_id }], "valid"=>true}}.to_json,
          headers: {
            :Authorization => "Bearer "+ @token,
            :content_type => "application/json"
          }).
        to_return(status: 200, body: { "content": [{ "id": contact.entity_id, "ownerId": another_user.id } ]}.to_json, headers: {})
    end

    context " - Email related to Deal" do
      let(:entity) { LOOKUP_DEAL }

      context "when email is associated with deal and has read permssion and email permission on contacts and does not have special email permission" do
        before { EmailRelatedToContactEventPublisher.call(@params, @auth_data, @email) }

        it 'raises an event in the Email Exchange & routes it to queue email.relatedTo.contact' do
          expect(@queue.message_count).to eq(1)
        end

        it 'publishes the correct payload' do
          payload = @queue.pop
          expect(payload.last).to eq(@event.to_json)
        end
      end

      context "when contact is not associated with deal" do
        before do
          stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?page=0&size=30").
            with(
              body: { "fields"=>["id"], "jsonRule"=>{"condition"=>"AND", "rules"=>[{"operator"=>"equal", "id"=>"associatedDeals", "field"=>"associatedDeals", "type"=>"long", "value"=> @related_to.entity_id }], "valid"=> true }}.to_json,
              headers: {
                :Authorization => "Bearer "+ @token,
                :content_type => "application/json"
              }).
            to_return(status: 200, body: { "content": []}.to_json, headers: {})

          EmailRelatedToContactEventPublisher.call(@params, @auth_data, @email)
        end

        it 'does not raise an event in the Email Exchange' do
          expect(@queue.message_count).to eq(0)
        end
      end

      context "when user does not have read permission on contact" do
        before do
          @auth_data.permissions.first.action.read = false
          @auth_data.permissions.first.action.read_all = false

          @token = JWT.encode @auth_data, nil, 'none'
          thread = Thread.current
          thread[:token] = @token

          EmailRelatedToContactEventPublisher.call(@params, @auth_data, @email)
        end

        it 'does not raise an event in the Email Exchange' do
          expect(@queue.message_count).to eq(0)
        end
      end

      context "when user has no email permission on contact" do
        before do
          @auth_data.permissions.first.action.email = false

          token = JWT.encode @auth_data, nil, 'none'
          thread = Thread.current
          thread[:token] = token

          EmailRelatedToContactEventPublisher.call(@params, @auth_data, @email)
        end

        it 'does not raise an event in the Email Exchange' do
          expect(@queue.message_count).to eq(0)
        end
      end
    end

    context " - Email related to Lead" do
      let(:entity) { LOOKUP_LEAD }

      before do
        EmailRelatedToContactEventPublisher.call(@params, @auth_data, @email)
      end

      it 'does not raise an event in the Email Exchange' do
        expect(@queue.message_count).to eq(0)
      end
    end

    context 'when multiple users in recipients' do
      let(:entity) { LOOKUP_DEAL }
      before do
        @email.related_to << build(:look_up, entity: 'user_33',tenant_id: @email.tenant_id)
        @email.save
        EmailRelatedToContactEventPublisher.call(@params, @auth_data, @email)
      end
      it 'should publish event for each and every user recipient for given contact' do
        expect(@queue.message_count).to eq(2)
        messages =  @queue.all.map{|c| c[:message]}
        expect(JSON.parse(messages.first)['toId']).to eq(33)
        expect(JSON.parse(messages.last)['toId']).to eq(@user.id)
      end
    end
  end
end
