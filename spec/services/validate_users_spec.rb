require 'rails_helper'

RSpec.describe ValidateUsers do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @user = build(:look_up, entity_type: LOOKUP_USER, entity_id: 9, tenant_id: 10)
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end
    context 'valid input' do
      before do
        stub_request(:get, "http://localhost:8081/v1/users/summary?id=9").
         with(
           headers: {
          'Authorization'=>'Bearer '+ @token
           }).
         to_return(status: 200, body: [{"id": @user.entity_id,"name": "<PERSON>","email": {"primary": true,"value": @user.email}} ].to_json, headers: {})
      end
      it ' returns if no users are passed' do
        expect(ValidateUsers.call(nil).success?).to be true
      end

      it 'returns array of updated user look ups' do
        command = ValidateUsers.call([@user])
        expect(command.result.class).to be Array
        expect(command.result.count).to be == 1
        expect(command.result[0].class).to be == LookUp
      end

      it 'returns lookups with updated names' do
        command = ValidateUsers.call([@user])
      end


    end
    context 'invalid entry from user' do
      before do
        @invalid_user = build(:look_up, entity_type: LOOKUP_USER, entity_id: 10, tenant_id: 9)
        stub_request(:get, "http://localhost:8081/v1/users/summary?id=9,10").
         with(
           headers: {
          'Authorization'=>'Bearer '+ @token
           }).to_return(body: '', status: 404)
      end
      it 'invalid InvalidDataError for non-existent user' do
        expect {ValidateUsers.call([@user, @invalid_user])}.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid
        )
      end
    end
  end
end
