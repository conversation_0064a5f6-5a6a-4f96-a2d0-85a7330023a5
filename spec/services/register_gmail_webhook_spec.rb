require 'rails_helper'

RSpec.describe RegisterGmailWebhook do
  describe '#call' do
    before do
      @user = create(:user)
      @expires_at = (Time.now + 3.days).to_i
      @acct = create(:connected_account, provider_name: GOOGLE_PROVIDER, expires_at: @expires_at, user: @user)
      stub_request(:post, "https://gmail.googleapis.com/gmail/v1/users/me/watch").
        to_return(status: 200, body: { 'id' => '123', 'expirationDateTime': '2021-05-01T18:59:45.9356913Z', 'historyId': '1' }.to_json, headers: {'content-type' => 'application/json'})

      thread = Thread.current
      thread[:token] = GenerateToken.call(@acct.user.id, @acct.user.tenant_id).result
    end

    context 'when subscription is successful' do
      it 'stores subscription id and expiry in the database' do
        acct = RegisterGmailWebhook.call(@acct).result
        expect(@acct.last_history_id).to eq("1")
      end
    end
  end
end
