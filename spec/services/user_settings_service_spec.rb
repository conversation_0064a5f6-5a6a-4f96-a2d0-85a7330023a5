require 'rails_helper'

RSpec.describe  UserSettingsService do
  describe "#fetch" do

    context "Success" do
      let(:user_data){ file_fixture('user-profile-response.json').read }

      before(:each) do
        @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
        @auth_data = ParseToken.call(@token).result
        thread = Thread.current
        thread[:auth] = @auth_data
        thread[:token] = @token

        stub_request(:get, SERVICE_IAM + "/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }).
            to_return(status: 200, body: user_data, headers: {})
      end

      it 'should return timezone and preffered timezone' do
        response = UserSettingsService.new.fetch
        data = JSON.parse(user_data)
        expect(response[:timezone]).to eq data["timezone"]
        expect(response[:preffered_date_format]).to eq data["dateFormat"]
        expect(response[:profile_id]).to eq data["profileId"]
       end
    end
  end
end
