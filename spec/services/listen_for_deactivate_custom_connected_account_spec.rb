require 'bunny-mock'
require 'rails_helper'

RSpec.describe ListenForDeactivateCustomConnectedAccount do
  describe '#call' do
    before do
      @user = create(:user)
      create(:connected_account, active: true, user: @user, provider_name: CUSTOM_PROVIDER)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic USER_EXCHANGE
      @payload = {
        'userId' => @user.id,
        'tenantId'=> @user.tenant_id,
        'provider'=> CUSTOM_PROVIDER,
        'disconnectedBy' => 'USER',
        'userEmail' => '<EMAIL>',
        'tenantEmail' => '<EMAIL>'
      }.to_json
    end

    context 'deactivates account' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: DEACTIVATE_CUSTOM_CONNECTED_ACCOUNT_EVENT
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(USER_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(USER_EXCHANGE, DEACTIVATE_CUSTOM_CONNECTED_ACCOUNT_EVENT, DEACTIVATE_CUSTOM_CONNECTED_ACCOUNT_QUEUE)
          .and_yield(@payload.to_s)

        expect(PublishEvent).not_to receive(:call).with(instance_of(Event::ConnectedAccountDeactivated))
        ListenForDeactivateCustomConnectedAccount.call()
      end

      it 'deactivates connected account' do
        expect(@user.connected_accounts.where(active: false).count).to eq 1
        expect(@user.connected_accounts.where(active: true).count).to eq 0
      end
    end

    context 'when account is auto disconnected' do
      before do
        @payload = {
          'userId' => @user.id,
          'tenantId'=> @user.tenant_id,
          'provider'=> CUSTOM_PROVIDER,
          'disconnectedBy' => 'AUTO',
          'userEmail' => '<EMAIL>',
          'tenantEmail' => '<EMAIL>'
        }.to_json
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: DEACTIVATE_CUSTOM_CONNECTED_ACCOUNT_EVENT
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(USER_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(USER_EXCHANGE, DEACTIVATE_CUSTOM_CONNECTED_ACCOUNT_EVENT, DEACTIVATE_CUSTOM_CONNECTED_ACCOUNT_QUEUE)
          .and_yield(@payload.to_s)

        expect(PublishEvent).to receive(:call).with(instance_of(Event::ConnectedAccountDeactivated)).exactly(1).times
        ListenForDeactivateCustomConnectedAccount.call()
      end

      it 'deactivates connected account and publishes event for account deactivated' do
        expect(@user.connected_accounts.where(active: false).count).to eq 1
        expect(@user.connected_accounts.where(active: true).count).to eq 0
      end
    end
  end
end
