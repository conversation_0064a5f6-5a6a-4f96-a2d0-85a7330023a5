require 'rails_helper'

RSpec.describe FetchDisplayName do
  describe '#call' do
    before do
      user = create(:user)
      thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
      @connected_account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i)
    end

    context 'Fetch display name from google' do
      context 'Success' do
        before do
          setting = Google::Apis::GmailV1::SendAs.new(display_name: 'Ajit')
          allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', @connected_account.email).and_return(setting)
        end

        it 'saves name on connected account' do
          FetchDisplayName.call(@connected_account)
          expect(@connected_account.reload.display_name).to eq 'Ajit'
        end
      end

      context 'failure' do
        before do
          allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', @connected_account.email).and_return(nil)
        end

        it 'saves does not overide name on connected account' do
          @connected_account.update(display_name: 'old name')
          FetchDisplayName.call(@connected_account)
          expect(@connected_account.reload.display_name).to eq 'old name'
        end
      end
    end

    context 'Fetch display name from outlook' do
      context 'Success' do
        before do
          @connected_account.update(provider_name: 'MICROSOFT')
          stub_request(:get, "https://graph.microsoft.com/v1.0/me").
            with(
              headers: {
                "Content-type" => "application/json",
                Authorization: "Bearer #{GetConnectedAccountAccessToken.call(@connected_account, skip_auth: true).call.result}"
              }
            ).
            to_return(status: 200, body: {displayName: 'Ajit'}.to_json, headers: {})
        end

        it 'saves name on connected account' do
          FetchDisplayName.call(@connected_account)
          expect(@connected_account.reload.display_name).to eq 'Ajit'
        end
      end

      context 'failure' do
        before do
          @connected_account.update(provider_name: 'MICROSOFT')
          stub_request(:get, "https://graph.microsoft.com/v1.0/me").
            with(
              headers: {
                "Content-type" => "application/json",
                Authorization: "Bearer #{GetConnectedAccountAccessToken.call(@connected_account, skip_auth: true).call.result}"
              }
            ).
            to_return(status: 403, headers: {})
        end

        it 'saves does not overide name on connected account' do
          @connected_account.update(display_name: 'old name')
          FetchDisplayName.call(@connected_account)
          expect(@connected_account.reload.display_name).to eq 'old name'
        end
      end
    end
  end
end
