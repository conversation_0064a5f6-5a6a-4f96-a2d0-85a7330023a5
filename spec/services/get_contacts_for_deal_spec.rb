require 'rails_helper'

RSpec.describe GetContactsForDeal do
  before do
    @user = create(:user)
    @deal_id = 99
  end
  context 'when user context is present' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end
    it 'should fetch contacts associated with deal'do
      stub_request(:post, SERVICE_SEARCH + '/v1/search/contact?page=0&size=30').
        with(
          body: { 'fields'=>['id'], 'jsonRule'=>{'condition'=>'AND', 'rules'=>[{'operator'=>'equal', 'id'=>'associatedDeals', 'field'=>'associatedDeals', 'type'=>'long', 'value'=> @deal_id }], 'valid'=>true}}.to_json,
          headers: {
            :Authorization => 'Bearer '+ @token,
            :content_type => 'application/json'
          }).
        to_return(status: 200, body: { "content": [{ "id": 11, "ownerId": @user.id } ]}.to_json, headers: {})
      command = GetContactsForDeal.call(@deal_id)
      expect(command.success?).to be_truthy
      expect(command.result).to eq([{ 'id'=> 11, 'ownerId'=> @user.id } ])
    end

    context 'when search service is down' do
      it 'should throw ' do
        stub_request(:post, SERVICE_SEARCH + '/v1/search/contact?page=0&size=30').
          with(
            body: { 'fields'=>['id'], 'jsonRule'=>{'condition'=>'AND', 'rules'=>[{'operator'=>'equal', 'id'=>'associatedDeals', 'field'=>'associatedDeals', 'type'=>'long', 'value'=> @deal_id }], 'valid'=>true}}.to_json,
            headers: {
              :Authorization => 'Bearer '+ @token,
              :content_type => 'application/json'
            }).to_return(status: 500)
        expect{ GetContactsForDeal.call(@deal_id) }.to raise_error(ExceptionHandler::InternalServerError).with_message(ErrorCode.internal_error)
      end
    end
  end

  context 'when user context is NOT present' do
    before do
      thread = Thread.current
      thread[:token] = nil
    end
    it 'should throw AuthenticationError'do
      expect{ GetContactsForDeal.call(@deal_id) }.to raise_error(ExceptionHandler::AuthenticationError).with_message(ErrorCode.unauthorized)
    end
  end
end
