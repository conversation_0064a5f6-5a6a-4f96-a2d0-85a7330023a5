require 'rails_helper'
require 'bunny-mock'
require 'google/apis/gmail_v1'

RSpec.describe ListenForSendWorkflowEmailEvent do
  describe '#call' do
    let!(:user)              { create(:user, tenant: create(:tenant, id: 99))}
    let!(:connected_account) { create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: user, active: true) }
    let(:user_data){ file_fixture('user-profile-response.json').read }
    let(:lead_fields){ file_fixture('lead-fields.json').read }
    let(:standard_picklist) { file_fixture('standard-picklist.json').read }

    let(:user_data){ file_fixture('user-profile-response.json').read }

    before do
      stub_entity_labels_api
      user_lookup = build(:look_up, entity_type: LOOKUP_USER, entity_id: user.id, tenant_id: user.tenant_id, name: user.name)
      allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
        user_lookup
      ])
      header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
      payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
      return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
      return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID')
      Google::Apis::GmailV1::GmailService.any_instance.stub(:send_user_message).and_return(return_message)

      setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', connected_account.email).and_return(setting)

    end

    context 'valid input' do
      before do
        connection = BunnyMock.new
        @channel = connection.start.channel
        @exchange = @channel.topic USER_EXCHANGE
        allow(TenantEmailTemplateUsagePublisher).to receive(:publish).with(instance_of(Integer))
        @email_template = create(:email_template, tenant_id: user.tenant_id)
        @payload = {
          "senderId" => user.id,
          "to" => [{
            "entity" => "email",
            "name" => "test",
            "email" => "<EMAIL>"
          }],
          "relatedTo" => {
            "entity" => "lead",
            "name" => "test",
            "email" => "<EMAIL>",
            "id" => 1
          },
          "userId" => user.id,
          "tenantId" => user.tenant_id,
          "emailTemplateId" => @email_template.id,
          "metadata" => { "executedWorkflows": ['WF_123', 'WF_456'] }
        }.to_json
        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: WORKFLOW_SEND_EMAIL_EVENT
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(WORKFLOW_EXCHANGE)
          .with(EMAIL_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(WORKFLOW_EXCHANGE, WORKFLOW_SEND_EMAIL_EVENT, WORKFLOW_SEND_EMAIL_QUEUE)
          .and_yield(@payload.to_s)
        @token = GenerateToken.call(connected_account.user.id, connected_account.user.tenant_id).result

         stub_request(:get, SERVICE_IAM + "/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }).
            to_return(status: 200, body: user_data, headers: {})


        @token = GenerateToken.call(connected_account.user.id, connected_account.user.tenant_id, 2).result
        stub_request(:get, SERVICE_SALES + "/v1/leads/1").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}"
            }).
            to_return(status: 200, body: { "id": 1, "ownerId": user.id, "recordActions": { "read": true, "update": true, "delete": true, "email": true, "call": false, "sms": false, "task": true, "note": true, "meeting": true }}.to_json, headers: {})

            stub_request(:get, SERVICE_IAM + "/v1/users/me").
              with(
                headers: {
                  "Authorization" => "Bearer #{@token}",
                  'Accept'=>'application/json',
                  'Content-Type'=>'application/json'
                }).
                to_return(status: 200, body: user_data, headers: {})

                stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?entityType=lead&custom-only=false&sort=createdAt,asc&page=0&size=100").
                  with(
                    headers: {
                      "Authorization" => "Bearer #{@token}"
                    }).
                    to_return(status: 200, body: lead_fields, headers: {})

                    stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
                      with(
                        headers: {
                          "Authorization" => "Bearer #{@token}"
                        }).
                        to_return(status: 200, body: standard_picklist, headers: {})

                        stub_request(:get, "http://localhost:8083/v1/summaries/leads?includeConverted=true&id=1").
                          with(
                            headers: {
                              'Authorization'=>"Bearer #{@token}"
                            }).
                            to_return(status: 200, body: [{"id": 1,"name": "Jane Test","emails": [{"primary": true,"value": '<EMAIL>'}]}].to_json, headers: {})

      end

      context 'when email template is active' do
        before do
          ListenForSendWorkflowEmailEvent.call()
        end

        it 'creates & sends email' do
          parsed_token = ParseToken.call(@token).result.permissions
          %w[lead deal user contact email_template call].each do |entity|
            entity_permission = parsed_token.find { |permission| permission.name == entity }
            expect(entity_permission.action.read).to eq(true)
            expect(entity_permission.action.read_all).to eq(true)
          end
          expect(@token)
          expect(Email.count).to eq(1)
          expect(Email.last.subject).to eq(@email_template.subject)
          expect(Email.last.body).to eq(@email_template.body)
          expect(Email.last.metadata).to eq({ "executedWorkflows" => ['WF_123', 'WF_456'] })
        end

        it 'stores profile id on user' do
          expect(user.reload.profile_id).to eq 2
        end
      end

      context 'when email template is active and template body contains figure tag' do
        it 'creates & sends email with replacing figure tag with div tag' do
          parsed_token = ParseToken.call(@token).result.permissions
          %w[lead deal user contact email_template call].each do |entity|
            entity_permission = parsed_token.find { |permission| permission.name == entity }
            expect(entity_permission.action.read).to eq(true)
            expect(entity_permission.action.read_all).to eq(true)
          end

          email_body_with_figure_tag = '<figure class=\"image image_resized image-style-align-left\" style=\"margin-block-start: 0em; margin-block-end: 0em; margin-inline-start: 0px; margin-inline-end: 0px; float: left; width: fit-content;\">This email is not intended to be a solicitation.</figure>'

          email_body_with_div_tag = '<div class=\"image image_resized image-style-align-left\" style=\"margin-block-start: 0em; margin-block-end: 0em; margin-inline-start: 0px; margin-inline-end: 0px; float: left; width: fit-content;\">This email is not intended to be a solicitation.</div>'

          @email_template.update(body: email_body_with_figure_tag)

          ListenForSendWorkflowEmailEvent.call

          expect(Email.count).to eq(1)
          expect(Email.last.subject).to eq(@email_template.subject)
          expect(Email.last.body).to eq(email_body_with_div_tag)
        end
      end

      context 'when email template is inactive' do
        before do
          @email_template.update!(active: false)
          ListenForSendWorkflowEmailEvent.call()
        end

        it 'does not create email' do
          parsed_token = ParseToken.call(@token).result.permissions
          %w[lead deal user contact email_template call].each do |entity|
            entity_permission = parsed_token.find { |permission| permission.name == entity }
            expect(entity_permission.action.read).to eq(true)
            expect(entity_permission.action.read_all).to eq(true)
          end
          expect(@token)
          expect(Email.count).to eq(0)
        end
      end
    end
  end
end
