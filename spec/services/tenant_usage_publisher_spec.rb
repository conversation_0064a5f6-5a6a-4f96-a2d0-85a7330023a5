require 'rails_helper'
require 'bunny-mock'

RSpec.describe TenantUsagePublisher do
  describe '#call' do
    before do
      BunnyMock.use_bunny_queue_pop_api = true
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic EMAIL_EXCHANGE

      @event = Event::TenantUsage.new([{:tenantId=>15, :count=>1, :usageEntity=>"EMAIL"},
                                       {:tenantId=>14, :count=>1, :usageEntity=>"EMAIL"},
                                       {:tenantId=>15, :count=>1, :usageEntity=>"EMAIL_TEMPLATE"},
                                       {:tenantId=>14, :count=>1, :usageEntity=>"EMAIL_TEMPLATE"},
                                       {:tenantId=>15, :count=>180, :usageEntity=>"STORAGE_EMAIL_ATTACHMENT"},
                                       {:tenantId=>14, :count=>100, :usageEntity=>"STORAGE_EMAIL_ATTACHMENT"}])

      @queue = @channel.queue "tenant.usage.collected"
      @queue.bind @exchange, routing_key: "tenant.usage.collected"
      allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)
      allow(TenantEmailTemplateUsagePublisher).to receive(:publish).with(instance_of(Integer))
      @tenant1 = create(:tenant, id: 14)
      @tenant2 = create(:tenant, id: 15)
    end

    context "When tenant usage publisher is called" do
      before do
        # 2 emails and 3 attachments
        email_1 = create(:email, tenant: @tenant1)
        email_2 = create(:email, tenant: @tenant2)
        create_list(:attachment, 5, email: email_1, file_size: 10)
        create_list(:attachment, 3, email: email_2, file_size: 20)
        create_list(:attachment, 1, email: email_2, file_size: 30)

        # 2 templates and 3 template_attachments
        template_1 = create(:email_template, tenant_id: @tenant1.id)
        template_2 = create(:email_template, tenant_id: @tenant2.id)
        create_list(:template_attachment, 5, email_template: template_1, file_size: 10)
        create_list(:template_attachment, 3, email_template: template_2, file_size: 20)
        create_list(:template_attachment, 1, email_template: template_2, file_size: 30)

      end

      context 'when tenant id is not passed' do
        before do
          TenantUsagePublisher.call(nil, true)
        end

        it 'publishes correct number of events' do
          expect(@queue.message_count).to eq(1)
        end

        it 'publishes the correct payload' do
          payload = @queue.pop
          expect(JSON(payload.last)['usageRecords']).to match_array(JSON(@event.to_json)['usageRecords'])
        end
      end

      context 'when tenant id is passed' do
        before do
          @tenant_14_data = Event::TenantUsage.new([{:tenantId=>14, :count=>1, :usageEntity=>"EMAIL"},
                                                    {:tenantId=>14, :count=>1, :usageEntity=>"EMAIL_TEMPLATE"},
                                                    {:tenantId=>14, :count=>100, :usageEntity=>"STORAGE_EMAIL_ATTACHMENT"}])
          TenantUsagePublisher.call(14, true)
        end
        it 'publishes correct number of events' do
          expect(@queue.message_count).to eq(1)
        end

        it 'publishes the correct payload' do
          payload = @queue.pop
          expect(JSON(payload.last)['usageRecords']).to match_array(JSON(@tenant_14_data.to_json)['usageRecords'])
        end
      end

      context 'when publish attachment usage flag is false' do
        before do
          @tenant_14_data = Event::TenantUsage.new([{:tenantId=>14, :count=>1, :usageEntity=>"EMAIL"},
                                                    {:tenantId=>14, :count=>1, :usageEntity=>"EMAIL_TEMPLATE"}])
          TenantUsagePublisher.call(14, false)
        end
        it 'publishes correct number of events' do
          expect(@queue.message_count).to eq(1)
        end

        it 'publishes the correct payload' do
          payload = @queue.pop
          expect(JSON(payload.last)['usageRecords']).to match_array(JSON(@tenant_14_data.to_json)['usageRecords'])
        end
      end
    end
  end
end
