# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetEmailFields do
  describe "#call" do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      it "returns email fields in output" do
        result = described_class.call.result.first

        expect(result).to eq (
          {
            "active" => true,
            "displayName" => "User",
            "filterable" => true,
            "id" => "user",
            "internal" => false,
            "lookup" => {
              "entity" => "USER",
              "lookupUrl" => "/users/lookup?q=name:"
            },
            "name" => "user",
            "picklist" => nil,
            "required" => true,
            "sortable" => false,
            "standard" => true,
            "type" => "LOOK_UP",
          }
        )
      end
    end

    context "with invalid input - " do
      context "when unauthorized" do
        before { Thread.current[:auth] = nil }

        it 'should raise authentication error' do
          expect(Rails.logger).to receive(:error).with("Unauthorised: User context missing in GetEmailFields")
          expect { GetEmailFields.call() }.to raise_error(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
        end
      end
    end
  end
end
