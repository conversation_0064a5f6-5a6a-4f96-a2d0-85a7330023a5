require 'rails_helper'

RSpec.describe GetUserDetails do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'for existing user' do
      before do
        @user = create(:user)
        @command = GetUserDetails.call(@user.id, @user.tenant_id)
      end

      it 'returns the same user' do
        expect(@command.result).to eq(@user)
      end
    end

    context 'for new user' do
      before do
        @user = build(:user, id: 10, tenant: create(:tenant, id: 1))
        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{@user.id}")
          .with(headers: { 'Authorization' => "Bearer #{@token}" })
          .to_return(
            status: 200,
            body: [
              {
                "id": @user.id, "name": '<PERSON>',
                "email": { "primary": true, "value": 'jane<PERSON><EMAIL>' }
              }
            ].to_json,
            headers: {}
          )
        @command = GetUserDetails.call(@user.id, @user.tenant_id)
      end

      it 'returns the new user' do
        user = @command.result
        expect(Tenant.count).to eq 1
        expect(user.id).to eq(@user.id)
        expect(user.tenant_id).to eq(@user.tenant_id)
        expect(user.name).to eq('Jane Doe')
      end
    end
  end
end
