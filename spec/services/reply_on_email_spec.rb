# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ReplyOnEmail do
  describe '#call' do
    let(:user)              { create(:user)}
    let(:valid_auth_token)  { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let(:connected_account) { create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: user, tenant: user.tenant) }
    let(:email)             { build(:email, connected_account: connected_account) }
    let(:auth_data)         { ParseToken.call(valid_auth_token.token).result }

    before do
      @email_thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
      user_look_up = build(:look_up, entity_type: LOOKUP_USER, entity_id: user.id, tenant_id: user.tenant_id)
      new_email = create(:email, owner: user, to: [connected_account.email], tenant_id: user.tenant_id, email_thread_id: @email_thread.id, connected_account: connected_account, sender: user_look_up)
      expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata))
      new_email.related_to << build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1000, owner_id: user.id, tenant_id: user.tenant_id)

      lead_search_response = JSON.parse(file_fixture('lead-search-response.json').read)
      lead_search_response['content'] << { id: 1, firstName: 'lead', lastName: nil, ownerId: 4167, emails: [{ value: '<EMAIL>' }], recordActions: { email: true } }
      stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=1000&sort=updatedAt,desc").
        with(
          headers: {
            Authorization: "Bearer #{valid_auth_token.token}"
          },
          body: {
            fields: %w[id firstName lastName emails ownerId],
            jsonRule:{
              condition: 'AND',
              rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: '1,9' }],
              valid: true
            }
          }
        ).
        to_return(status: 200, body: lead_search_response.to_json, headers: {})

      stub_request(:post, "http://localhost:8083/v1/search/contact?page=0&size=1000&sort=updatedAt,desc").
        with(
          headers: {
            Authorization: "Bearer #{valid_auth_token.token}"
          },
          body: {
            fields: %w[id firstName lastName emails ownerId],
            jsonRule:{
              condition: 'AND',
              rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: '9' }],
              valid: true
            }
          }
        ).
        to_return(status: 200, body: file_fixture('contact-search-response.json').read, headers: {})

      header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
      payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
      return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
      setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', connected_account.email).and_return(setting)
    end

    let(:params) {
      {
        id: @email_thread.id,
        replyTo: Email.where(email_thread_id: @email_thread.id).first.id,
        subject: 'Re:test',
        body: 'some text',
        relatedTo: { entity: 'lead', id: 9, name: 'lead', email: '<EMAIL>' },
        to: [{ entity: 'lead', id: 1, name: 'lead', email: '<EMAIL>' }],
        cc: [{ entity: 'contact', id: 9, name: 'contact_cc', email: '<EMAIL>' }],
        bcc: [],
      }
    }

    context 'when user is participant in conversation' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token

        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata)).thrice
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailReceivedRelatedToEntity))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailCreatedWorkflowV2)).once
      end

      it 'should allow to reply on thread' do
        command = described_class.call(params)

        expect(command.success?).to be_truthy
        expect(Email.where(email_thread_id: @email_thread.id, subject: 'Re:test').first.id).to eq(command.result.id)
        expect(Email.where(email_thread_id: @email_thread.id, subject: 'Re:test').exists?).to be_truthy
      end
    end

    context 'when reply to email is not present' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        params[:id] = 10000
      end

      it 'raises & logs error' do
        expect(Rails.logger).to receive(:error).with('Email Not found while replying')
        expect do
          described_class.call(params).result
        end.to raise_error(ExceptionHandler::NotFound, '01602001')
      end
    end

    context 'when user is not a participant in conversation' do
      before do
        @another_user = create(:user, tenant: user.tenant)
        auth_token = build(:auth_token, user_id: @another_user.id, tenant_id: @another_user.tenant_id, username: @another_user.name )
        Thread.current[:auth] = ParseToken.call(auth_token.token).result
        Thread.current[:token] = auth_token.token
        allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => {} })
        allow_any_instance_of(Auth::Data).to receive(:can_read_all_emails?).and_return(true)
      end

      it 'raises & logs error' do
        expect(Rails.logger).to receive(:error).with("Unauthorised: User cannot reply to email. User id #{@another_user.id} Email id #{params[:replyTo]}")
        expect do
          described_class.call(params)
        end.to raise_error(ExceptionHandler::Forbidden, '01601005')
      end
    end

    context 'when missing user context' do
      before do
        Thread.current[:auth] = nil
        Thread.current[:token] = nil
      end

      it 'raises & logs error' do
        expect(Rails.logger).to receive(:error).with('Unauthorised: Missing user context in ReplyOnEmail')
        expect do
          described_class.call(params).result
        end.to raise_error(ExceptionHandler::AuthenticationError, '01601005')
      end
    end
  end
end
