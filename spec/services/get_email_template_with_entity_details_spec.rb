require 'rails_helper'

RSpec.describe GetEmailTemplateWithEntityDetails do
  describe '#call' do
    let(:user_data){ file_fixture('user-profile-response.json').read }
    let(:standard_picklist) { file_fixture('standard-picklist.json').read }
    let(:subject_without_variables) { "This is subject without variables" }
    let(:body_without_variables) do
      "This is body without any variables and hence it is expected that entity values need not be evaluated"
    end

    RSpec.shared_examples 'when no variables present on email template' do
      context 'when email template does not have any variables' do
        before { @email_template.update!(subject: subject_without_variables, body: body_without_variables) }

        it 'should not call entity variables & entity details API' do
          expect("Get#{@email_template.category.camelize}".constantize).not_to receive(:call)
          expect(GetStandardPicklist).not_to receive(:call)

          command = GetEmailTemplateWithEntityDetails.call(@email_template.id, @entity_id)
          result = command.result
          expect(result[:subject]).to eq(subject_without_variables)
          expect(result[:body]).to eq(body_without_variables)
        end
      end
    end

    before do
      stub_entity_labels_api
      allow(TenantEmailTemplateUsagePublisher).to receive(:publish).with(instance_of(Integer))
      @user = create(:user, id: 12, tenant: create(:tenant, id: 99))
      auth_data =  build(:auth_data, :email_template_with_read_access, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

      thread = Thread.current
      thread[:auth] = auth_data

      @token = FactoryBot.build(:auth_token, :with_email_template_read_all_permission, user_id: 12, tenant_id: 99).token

      thread[:token] = @token

      stub_request(:get, SERVICE_IAM + "/v1/users/me").
        with(
          headers: {
            "Authorization" => "Bearer #{@token}",
            'Accept'=>'application/json',
            'Content-Type'=>'application/json'
          }).
          to_return(status: 200, body: user_data, headers: {})

      stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
        with(
          headers: {
            "Authorization" => "Bearer #{@token}"
          }).
        to_return(status: 200, body: standard_picklist, headers: {})

      stub_request(:get, "#{SERVICE_IAM}/v1/tenants")
        .with(
          headers: {
            Authorization: "Bearer #{@token}"
          }
        )
        .to_return( status: 200, body: file_fixture('tenant-account.json').read, headers: {})

      stub_request(:post, SERVICE_IAM + "/v1/users/search?sort=updatedAt,desc&page=0&size=100")
        .to_return(status: 200, body: file_fixture('user-response.json'), headers: {})
    end

    context 'Template for lead' do
      let(:lead_fields){ file_fixture('lead-fields.json').read }

      before do
        @entity_id = 1
        body = "This is body with {{salutation}} {{firstName}} and {{lastName}, {if missing: <free text>}} is present but data is missing for {{missingData}, {if missing: free text}}. Tenant Account Name - {{tenant.accountName}} Pipeline Stage Reason {{lead.pipelineStageReason}}. User fields {{createdBy.firstName}} {{convertedBy.email}}"
        subject = "This is subject with {{firstName}} and {{lastName}, {if missing: <free text>}} is present but data is missing for {{missingData}, {if missing: free text}} {{date}}"
        @email_template = create(:email_template, category: LOOKUP_LEAD, body: body, subject: subject, tenant_id: 99, created_by: @user )

        response_body =  { "id": @entity_id,
                            "firstName": 'name',
                            'lastName': 'data',
                            "salutation": 794,
                            "pipelineStageReason": "Wrong number",
                            "createdBy": 4167,
                            "convertedBy": 6781,
                            "customFieldValues": {
                              "date": "2022-10-17T18:30:00.000Z"
                            },
                            "metaData": {
                              "idNameStore": { "salutation": { "794": "Mr"}, "createdBy": { "4167" => "Tony Stark" }, "updatedBy": { "6781" => "Low Ewa" }}
                            }
        }
        stub_request(:get, SERVICE_SALES + "/v1/leads/#{@entity_id}").
          with(
            headers: {
              "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 200, body: response_body.to_json, headers: {})

        stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?entityType=lead&custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
            "Authorization" => "Bearer #{@token}"
            }).
          to_return(status: 200, body: lead_fields, headers: {})
      end

      it 'returns subject and data' do
        command = GetEmailTemplateWithEntityDetails.call(@email_template.id, @entity_id)
        result = command.result
        expect(result[:body]).to eq("This is body with Mr name and data is present but data is missing for free text. Tenant Account Name - Tony QA Account Pipeline Stage Reason Wrong number. User <NAME_EMAIL>")
        expect(result[:subject]).to eq("This is subject with name and data is present but data is missing for free text Oct 18,2022")
        expect(result[:email_template].id).to eq @email_template.id
      end

      include_examples 'when no variables present on email template'
    end

    context 'Template for deal' do
      let(:deal_fields){ file_fixture('deal-fields.json').read }

      before do
        @entity_id = 1
        body = "This is body with {{firstName}}, estimated {{estimatedValue}}, owned by {{ownedBy}}, company {{company}}  and agin {{firstName}} and {{lastName}, {if missing: <free text>}} is present but data is missing for {{missingData}, {if missing: free text}}. Tenant Company Name - {{tenant.companyName}, {if missing: <free text>}} Pipeline Stage Reason {{deal.pipelineStageReason}} User fields {{updatedBy.lastName}} {{createdBy.designation}}"
        subject = "This is subject with {{firstName}} and {{lastName}, {if missing: <free text>}} is present but data is missing for {{missingData}, {if missing: free text}}"
        @email_template = create(:email_template, category: LOOKUP_DEAL, body: body, subject: subject, tenant_id: 99, created_by: @user )

        body =  {
          "id": @entity_id,
          "firstName": 'name',
          'lastName': 'data',
          "estimatedValue": { "currencyId": 400, "value": 1111111 },
          "ownedBy": {"id": 11, "name": "Mr"},
          "company": {"id": 27, "name": "test"},
          "createdBy": { "id": 6781, "name": "Low Ewa" },
          "updatedBy": { "id": 4167, "name": "Tony Stark" },
          "pipelineStageReason": "Booked with competitor"
        }
        stub_request(:get, SERVICE_DEAL + "/v1/deals/#{@entity_id}").
          with(
            headers: {
              "Authorization" => "Bearer #{ @token }"
            }).
            to_return(status: 200, body: body.to_json, headers: {})

        stub_request(:get, SERVICE_DEAL + "/v1/deals/fields?custom-only=false&sort=createdAt,asc&page=0&size=100").
        with(
          headers: {
          "Authorization" => "Bearer #{@token}"
          }).
        to_return(status: 200, body: deal_fields, headers: {})
      end

      it 'returns subject and data' do
        command = GetEmailTemplateWithEntityDetails.call(@email_template.id, @entity_id)
        result = command.result
        expect(result[:body]).to eq "This is body with name, estimated USD 1111111, owned by Mr, company test  and agin name and data is present but data is missing for free text. Tenant Company Name - Tony's QA Company Pipeline Stage Reason Booked with competitor User fields Stark New Designation"
        expect(result[:subject]).to eq "This is subject with name and data is present but data is missing for free text"
        expect(result[:email_template].id).to eq @email_template.id
      end

      include_examples 'when no variables present on email template'
    end

    context 'Template for contact' do
      let(:contact_fields){ file_fixture('contact-fields.json').read }

      before do
        @entity_id = 1
        body = "This is body with {{salutation}} {{firstName}} works for {{company}} and {{lastName}, {if missing: <free text>}} is present but data is missing for {{missingData}, {if missing: free text}}. Tenant timezone - {{tenant.timezone}}. User fields {{contact.phoneNumbers}, {if missing: Phone Number Not Available}}"
        subject = "This is subject with {{firstName}} and {{lastName}, {if missing: <free text>}} is present but data is missing for {{missingData}, {if missing: free text}}"
        @email_template = create(:email_template, category: LOOKUP_CONTACT, body: body, subject: subject, tenant_id: 99, created_by: @user )

         response_body =  { "id": @entity_id,
                            "firstName": 'name',
                            'lastName': 'data',
                            "salutation": 794,
                            "company": 1,
                            "metaData": {
                              "idNameStore": { "salutation": { "794": "Mr"}, "company": {"1": "kylas"}}
                            }
        }

        stub_request(:get, SERVICE_SALES + "/v1/contacts/#{@entity_id}").
          with(
            headers: {
              "Authorization" => "Bearer #{ @token }"
            }).
            to_return(status: 200, body: response_body.to_json, headers: {})

        stub_request(:get, SERVICE_CONFIG + "/v1/entities/contact/fields?entityType=contact&custom-only=false&sort=createdAt,asc&page=0&size=100").
        with(
          headers: {
          "Authorization" => "Bearer #{@token}"
          }).
        to_return(status: 200, body: contact_fields, headers: {})
      end

      it 'returns subject and data' do
        command = GetEmailTemplateWithEntityDetails.call(@email_template.id, @entity_id)
        result = command.result
        expect(result[:body]).to eq "This is body with Mr name works for kylas and data is present but data is missing for free text. Tenant timezone - (GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi / Sri Jayawardenapura. User fields Phone Number Not Available"
        expect(result[:subject]).to eq "This is subject with name and data is present but data is missing for free text"
        expect(result[:email_template].id).to eq @email_template.id
      end

      include_examples 'when no variables present on email template'
    end

    context 'Template for Call Log' do
      before do
        @entity_id = 1
        body = '{{id}, {if missing: <free text>}} {{outcome}, {if missing: <free text>}} {{callType}, {if missing: <free text>}} {{phoneNumber}, {if missing: <free text>}} {{duration}, {if missing: <free text>}} {{createdBy}, {if missing: <free text>}} {{updatedBy}, {if missing: <free text>}} {{owner}, {if missing: <free text>}} {{createdAt}, {if missing: <free text>}} {{updatedAt}, {if missing: <free text>}} {{deviceId}, {if missing: <free text>}} {{associatedLeads}, {if missing: <free text>}} {{associatedDeals}, {if missing: <free text>}} {{associatedContacts}, {if missing: <free text>}} {{tenant.website}, {if missing: <free text>}} {{createdBy.salutation}} {{updatedBy.phoneNumbers}} {{owner.profileId}}'
        subject = 'test {{outcome}}'
        @email_template = create(:email_template, category: LOOKUP_CALL, body: body, subject: subject, tenant_id: 99, created_by: @user)

        stub_request(:get, "#{SERVICE_CALL}/v1/call-logs/fields").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('call-fields.json'), headers: {}
        )

        stub_request(:get, "#{SERVICE_CALL}/v1/call-logs/#{@entity_id}").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('call-details.json'), headers: {}
        )
      end

      it 'returns subject and data' do
        command = GetEmailTemplateWithEntityDetails.call(@email_template.id, @entity_id)
        result = command.result
        expect(result[:body]).to eq("39494 connected outgoing +919527890092 23  Shubham Katte  Shubham Katte New aircall User Oct 10,2022 at10:43 am IST Oct 10,2022 at10:43 am IST Aircall 09527890092 Blair Durham Conan Joyce <a href = 'https://www.kylas.io'> https://www.kylas.io </a> Mr +91 9190898887 Restricted User")
        expect(result[:subject]).to eq('test connected')
      end

      context 'when body contans recording url and recording url is present on call log' do
        context 'when recording url is fetched successfully' do
          it 'replaces variable with URL' do
            body = '{{callRecording}}'
            subject = 'test {{outcome}}'
            @email_template = create(:email_template, category: LOOKUP_CALL, body: body, subject: subject, tenant_id: 99, created_by: @user)
            stub_request(:get, "#{SERVICE_CALL}/v1/call-logs/#{@entity_id}/call-recordings/24044?longLivedUrl=true").
            with(
              headers: {
                'Authorization' => "Bearer #{@token}"
                }).
              to_return(status: 200, body: file_fixture('call-recording-url.json'), headers: {}
            )

            command = GetEmailTemplateWithEntityDetails.call(@email_template.id, @entity_id)
            result = command.result
            expect(result[:body]).to eq('https://qa-call-recording.sgp1.digitaloceanspaces.com/tenant_2174/user_4010/call_recordings/39972_file_example_MP3_700KB_1666003315.mp3?response-content-disposition=attachment%3B%20filename%3Dfile_example_MP3_700KB.mp3&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=Q3HPYXCYNPT55Y6NZF2M%2F20221017%2Fsgp1%2Fs3%2Faws4_request&X-Amz-Date=20221017T123342Z&X-Amz-Expires=300&X-Amz-SignedHeaders=host&X-Amz-Signature=4b5889104edd5504f4875b6ffd854d18b94b200c1388ba341547d668f0a4591f')
            expect(result[:subject]).to eq('test connected')
          end
        end

        context 'when recording url fetching is failed' do
          it 'replaces with error message' do
            body = '{{callRecording}}'
            subject = 'test {{outcome}}'
            @email_template = create(:email_template, category: LOOKUP_CALL, body: body, subject: subject, tenant_id: 99, created_by: @user)
            stub_request(:get, "#{SERVICE_CALL}/v1/call-logs/#{@entity_id}/call-recordings/24044?longLivedUrl=true").
            with(
              headers: {
                'Authorization' => "Bearer #{@token}"
                }).
              to_return(status: 400, body: file_fixture('call-recording-url.json'), headers: {}
            )

            command = GetEmailTemplateWithEntityDetails.call(@email_template.id, @entity_id)
            result = command.result
            expect(result[:body]).to eq('Unable to get recording URL')
            expect(result[:subject]).to eq('test connected')
          end
        end
      end

      context 'when body contains recording url and call log does not have recording' do
        it 'replaces with proper message' do
          body = '{{callRecording}}'
          subject = 'test {{outcome}}'
          @email_template = create(:email_template, category: LOOKUP_CALL, body: body, subject: subject, tenant_id: 99, created_by: @user)
          stub_request(:get, "#{SERVICE_CALL}/v1/call-logs/#{@entity_id}").
          with(
            headers: {
              'Authorization' => "Bearer #{@token}"
              }).
            to_return(status: 200, body: file_fixture('call-detail-without-recording.json'), headers: {}
          )

          command = GetEmailTemplateWithEntityDetails.call(@email_template.id, @entity_id)
          result = command.result
          expect(result[:body]).to eq('Recording URL not available')
          expect(result[:subject]).to eq('test connected')
        end
      end

      include_examples 'when no variables present on email template'
    end

    context 'Template for Meeting' do
      before do
        @entity_id = 1
        body = '{{id}, {if missing: <free text>}} {{location}, {if missing: <free text>}} {{medium}, {if missing: <free text>}} {{from}, {if missing: <free text>}} {{createdBy}, {if missing: <free text>}} {{updatedBy}, {if missing: <free text>}} {{owner}, {if missing: <free text>}} {{createdAt}, {if missing: <free text>}} {{updatedAt}, {if missing: <free text>}} {{relateTo}, {if missing: <free text>}} {{providerLink}, {if missing: <free text>}} {{cancelledBy}, {if missing: <free text>}} {{tenant.website}, {if missing: <free text>}} {{createdBy.salutation}} {{updatedBy.phoneNumbers}} {{owner.profileId}} {{checkedInAt}} {{cfCustompicklistfield}}'
        subject = 'test {{title}}'
        @email_template = create(:email_template, category: LOOKUP_MEETING, body: body, subject: subject, tenant_id: 99, created_by: @user)

        stub_request(:get, "#{SERVICE_MEETING}/v1/meetings/fields").
          with(
            headers: {
              'Authorization' => "Bearer #{@token}"
              }).
            to_return(status: 200, body: file_fixture('meeting-fields.json'), headers: {}
          )

        stub_request(:get, "#{SERVICE_MEETING}/v1/meetings/#{@entity_id}").
          with(
            headers: {
              'Authorization' => "Bearer #{@token}"
              }).
            to_return(status: 200, body: file_fixture('meeting-details.json'), headers: {}
          )
      end

      it 'returns subject and data' do
        command = GetEmailTemplateWithEntityDetails.call(@email_template.id, @entity_id)
        result = command.result

        expect(result[:body]).to eq("5884 office location GOOGLE Dec 18,2022 at12:00 pm IST Low Ewa Low Ewa Low Ewa Dec 14,2022 at11:32 am IST Dec 14,2022 at11:32 am IST <free text> <a href = 'https://meet.google.com/mjn-mvqf-pcv'> https://meet.google.com/mjn-mvqf-pcv </a> <free text> <a href = 'https://www.kylas.io'> https://www.kylas.io </a> Mr +91 9876543219 Restricted User Dec 18,2022 at12:00 pm IST first")
        expect(result[:subject]).to eq('test meetign title')
      end

      include_examples 'when no variables present on email template'
    end

    context 'Template for Task' do
      before do
        @entity_id = 1
        body = '{{task.id}, {if missing: <free text>}} <div> {{task.name}, {if missing: <free text>}}</div><div> {{task.description}, {if missing: <free text>}}</div><div> {{task.type}, {if missing: <free text>}}</div><div> {{task.dueDate}, {if missing: <free text>}}</div><div> {{task.status}, {if missing: <free text>}}</div><div> {{task.priority}, {if missing: <free text>}}</div><div> {{task.assignedTo}, {if missing: <free text>}}</div><div> {{task.createdBy}, {if missing: <free text>}}</div><div> {{task.relation}, {if missing: <free text>}}</div><div> {{task.updatedBy}, {if missing: <free text>}}</div><div> {{task.completedAt}, {if missing: <free text>}}</div><div> {{task.originalDueDate}, {if missing: <free text>}}</div><div> {{task.cancelledAt}, {if missing: <free text>}}</div><div> {{task.reminder}, {if missing: <free text>}}</div><div> {{task.ownerId}, {if missing: <free text>}}</div>'
        subject =  '{{task.name}, {if missing: <free text>}}'
        @email_template = create(:email_template, category: LOOKUP_TASK, body: body, subject: subject, tenant_id: 99, created_by: @user)

        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/task/fields?custom-only=false&entityType=task&page=0&size=100&sort=createdAt,asc").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('task-fields.json'), headers: {}
        )

        stub_request(:get, "#{SERVICE_PRODUCTIVITY}/v1/tasks/#{@entity_id}").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('task-details.json'), headers: {}
        )

        stub_request(:get, "#{SERVICE_SALES}/v1/contacts/120773").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('contact-data.json'), headers: {}
        )

        stub_request(:get, "#{SERVICE_SALES}/v1/leads/341109").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('lead-data.json'), headers: {}
        )

        stub_request(:get, "#{SERVICE_DEAL}/v1/deals/25596").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('deal-data.json'), headers: {}
        )

        stub_request(:get, SERVICE_SEARCH + "/v1/summaries/companies/idName?id=25597").
        with(
          headers: {
          "Authorization" => "Bearer #{ @token }"
          }).
        to_return(status: 200, body: [{ "id": 25597, "name": "Dr. Stone" }].to_json, headers: {})
      end

      it 'returns variable replaced subject and data' do
        command = GetEmailTemplateWithEntityDetails.call(@email_template.id, @entity_id)
        result = command.result
        expect(result[:body]).to eq(
          "24822 <div> Mara Woodss</div><div> Eum nostrum earum cu</div><div> Call</div><div> Jul 06,2022 at 6:00 pm IST</div><div> Completed</div><div> High</div><div> Shubham Shubham Katte</div><div> Shubham Shubham Katte</div><div> SK Company, shweta kylas, asd, Dr. Stone</div><div> Shubham Shubham Katte</div><div> Feb 21,2023 at 3:58 pm IST</div><div> Jul 06,2022 at 6:00 pm IST</div><div> <free text></div><div> 1 hour before the due date and time</div><div> Shubham Shubham Katte</div>"
        )
        expect(result[:subject]).to eq('Mara Woodss')
      end
    end
  end
end
