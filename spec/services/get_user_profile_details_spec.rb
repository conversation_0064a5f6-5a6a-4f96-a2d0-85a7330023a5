require 'rails_helper'

RSpec.describe GetUserProfileDetails do
  describe "#call" do
    let(:user_id)   { 1 }
    let(:tenant_id) { 1 }

    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'when user is not already present in DB' do
      before do
        stub_request(:get, "http://localhost:8081/v1/users/1").
          with(
            headers: {
              'Authorization' => "Bearer #{ @token }"
            }).
            to_return(status: 200, body:{ firstName: 'Test', lastName: 'User'}.to_json, headers: {})
      end

      it 'returns user profile correctly' do
        user = GetUserProfileDetails.call(user_id, tenant_id).result
        expect(user.id).to eq(1)
        expect(user.name).to eq('Test User')
      end
    end

    context 'when user is already present' do
      before { @user = create(:user, name: "Test User") }

      it 'returns user profile correctly' do
        @user = GetUserProfileDetails.call(@user.id, @user.tenant_id).result

        user_in_db = User.find(@user.id)

        expect(@user.id).to eq(user_in_db.id)
        expect(@user.name).to eq(user_in_db.name)
      end
    end
  end
end

