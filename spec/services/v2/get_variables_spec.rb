require 'rails_helper'

RSpec.describe V2::GetVariables do
  describe "#call" do
    before do
      stub_entity_labels_api
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    RSpec.shared_examples "returns tenant variables" do
      it 'returns tenant variables for entity' do
        tenant_variables = @result.select { |variable| variable['entity'] == 'tenant' }
        expect(tenant_variables.first['groupBy']).to eq('Tenant')
        expect(tenant_variables.count).to be(14)
        expect(tenant_variables.map { |v| v['internalName']}).to match_array(%w[companyName website accountName industry address city state country zip id taxIdentificationNumber language currency timezone])
        expect(tenant_variables.map { |v| v['displayName']}).to match_array(['Tenant - Company Name', 'Tenant - Website', 'Tenant - Account Name', 'Tenant - Industry', 'Tenant - Address', 'Tenant - City', 'Tenant - State', 'Tenant - Country', 'Tenant - Zipcode', 'Tenant - Tenant ID', 'Tenant - Tax identification number', 'Tenant - Language', 'Tenant - Currency', 'Tenant - Timezone'])
        expect(tenant_variables.map { |v| v['type']}).to match_array(%w[TEXT_FIELD URL TEXT_FIELD PICK_LIST TEXT_FIELD TEXT_FIELD TEXT_FIELD PICK_LIST TEXT_FIELD NUMBER TEXT_FIELD PICK_LIST PICK_LIST PICK_LIST])
      end
    end

    RSpec.shared_examples "v2:should not contain excluded & checkbox fields" do
      it 'should not have any fields of type CHECKBOX' do
        expect(@result.select { |field| field['type'] == 'CHECKBOX' }).to eq([])
      end

      it 'should not have any excluded fields' do
        entity_fields = @result.select { |field| field['entity'] == entity }
                                .map { |field| field['internalName'] }
        expect(
          entity_fields & "#{entity.upcase}_EXCLUDED_FIELDS".constantize
        ).to eq([])
      end
    end

    RSpec.shared_examples "should have user fields" do
      it 'should have user lookup fields' do
        grouped_fields = @result.group_by { |field| field['entity'] }
        expect(grouped_fields.keys).to include(*user_field_display_name.keys)
        user_field_display_name.keys.each do |user_field|
          expect(grouped_fields[user_field].count).to be(11)
          expect(grouped_fields[user_field].map { |field| field['internalName'] }).to match_array(%w[firstName lastName phoneNumbers email profileId salutation designation currency timezone signature id])
          expect(grouped_fields[user_field].map { |field| field['type'] }).to match_array(%w[TEXT_FIELD TEXT_FIELD PHONE TEXT_FIELD LOOK_UP PICK_LIST TEXT_FIELD PICK_LIST PICK_LIST RICH_TEXT NUMBER])
          grouped_fields[user_field].map { |field| field['displayName'] }.each do |display_name|
            expect(display_name).to start_with(user_field_display_name[user_field])
          end
          expect(grouped_fields[user_field].first['groupBy']).to eq(user_field_display_name[user_field])
        end
      end
    end

    context "with valid input for Lead " do
      let(:entity) { 'lead' }
      let(:user_field_display_name) { { createdBy: 'Created By', updatedBy: 'Updated By', ownerId: 'Owner', importedBy: 'Imported By', convertedBy: 'Converted By' }.with_indifferent_access }

      before do
        stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?entityType=lead&custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
              "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 200, body: file_fixture('lead-fields.json').read, headers: {})
        @result = described_class.call(entity).result
      end

      it 'returns fields for lead in output' do
        res = @result.select { |field| field['entity'] == 'lead' }.first
        expect(res['id']).to eq(509)
        expect(res['displayName']).to eq("My Lead - First Name1 ")
        expect(res['internalName']).to eq("firstName")
      end

      include_examples 'returns tenant variables'
      include_examples 'v2:should not contain excluded & checkbox fields'
      include_examples 'should have user fields'
    end

    context "with valid input for Contact " do
      let(:entity) { 'contact' }
      let(:user_field_display_name) { { createdBy: 'Created By', updatedBy: 'Updated By', ownerId: 'Owner', importedBy: 'Imported By' }.with_indifferent_access }

      before do
        stub_request(:get, SERVICE_CONFIG + "/v1/entities/contact/fields?entityType=contact&custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
              "Authorization" => "Bearer #{ @token }"
            }).
            to_return(status: 200, body: file_fixture('contact-fields.json'), headers: {})

        @result = described_class.call(entity).result
      end

      it "returns fields for contact in output" do
        res = @result.select { |field| field['entity'] == 'contact' }.first
        expect(res['id']).to eq(601)
        expect(res['displayName']).to eq("My Contact - Salutation")
        expect(res['internalName']).to eq("salutation")
      end

      include_examples 'returns tenant variables'
      include_examples 'v2:should not contain excluded & checkbox fields'
      include_examples 'should have user fields'
    end

    context "with valid input for Deal " do
      let(:entity) { 'deal' }
      let(:user_field_display_name) { { createdBy: 'Created By', updatedBy: 'Updated By', ownedBy: 'Owner' }.with_indifferent_access }

      before do
        stub_request(:get, SERVICE_DEAL + "/v1/deals/fields?custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
              "Authorization" => "Bearer #{ @token }"
            }).
            to_return(status: 200, body: file_fixture('deal-fields.json'), headers: {})

        @result = described_class.call(entity).result
      end

      it "returns fields for deal in output" do
        res = @result.select { |field| field['entity'] == 'deal' }.first
        expect(res['displayName']).to eq("Deal - Name")
        expect(res['internalName']).to eq("name")
      end

      include_examples 'returns tenant variables'
      include_examples 'v2:should not contain excluded & checkbox fields'
      include_examples 'should have user fields'
    end

    context 'with valid input for Call Log ' do
      let(:entity) { 'call_log' }
      let(:user_field_display_name) { { createdBy: 'Created By', updatedBy: 'Updated By', owner: 'Logged By' }.with_indifferent_access }

      before do
        stub_request(:get, "#{SERVICE_CALL}/v1/call-logs/fields").
          with(
            headers: {
              'Authorization' => "Bearer #{@token}"
              }).
            to_return(status: 200, body: file_fixture('call-fields.json'), headers: {}
          )

        @result = described_class.call(entity).result
      end

      it 'returns fields for call log in output' do
        first_call_field = @result.select { |field| field['entity'] == 'call_log' }
        expect(first_call_field.first['displayName']).to eq('Call Log - ID')
        expect(first_call_field.first['internalName']).to eq('id')
      end

      include_examples 'returns tenant variables'
      include_examples 'v2:should not contain excluded & checkbox fields'
      include_examples 'should have user fields'
    end

    context 'with valid input for Meeting' do
      let(:entity) { 'meeting' }
      let(:user_field_display_name) { { createdBy: 'Created By', updatedBy: 'Updated By', owner: 'Organiser', cancelledBy: 'Cancelled By', conductedBy: 'Conducted By' }.with_indifferent_access }

      before do
        stub_request(:get, "#{SERVICE_MEETING}/v1/meetings/fields").
          with(
            headers: {
              'Authorization' => "Bearer #{@token}"
              }).
            to_return(status: 200, body: file_fixture('meeting-fields.json'), headers: {}
          )

        @result = described_class.call(entity).result
      end

      it 'returns fields for meeting in output' do
        first_call_field = @result.select { |field| field['entity'] == 'meeting' }
        expect(first_call_field.first['displayName']).to eq('Meeting - ID')
        expect(first_call_field.first['internalName']).to eq('id')
      end

      include_examples 'returns tenant variables'
      include_examples 'v2:should not contain excluded & checkbox fields'
      include_examples 'should have user fields'
    end

    context 'with valid input for task' do
      let(:entity) { 'task' }
      let(:user_field_display_name) { { assignedTo: 'Assigned To', ownerId: 'Owner', createdBy: 'Created By', updatedBy: 'Updated By' }.with_indifferent_access }

      before do
        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/task/fields?custom-only=false&entityType=task&page=0&size=100&sort=createdAt,asc").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('task-fields.json'), headers: {})

        @result = described_class.call(entity).result
      end

      it 'returns fields for task' do
        task_fields = @result.select { |field| field['entity'] == 'task' }
        expect(task_fields.count).to eq(15)
        expect(task_fields.first['displayName']).to eq('Task - Task Name')
        expect(task_fields.first['internalName']).to eq('name')
        expect(task_fields.first['entity']).to eq('task')
      end

      include_examples 'returns tenant variables'
      include_examples 'v2:should not contain excluded & checkbox fields'
      include_examples 'should have user fields'
    end

    context 'with invalid category' do
      it 'should raise invalid data error' do
        expect do
          described_class.call('invalid').result
        end.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      end
    end
  end
end
