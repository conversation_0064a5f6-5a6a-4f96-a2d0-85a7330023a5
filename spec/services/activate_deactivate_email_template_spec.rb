# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ActivateDeactivateEmailTemplate do
  describe '#call' do
    let(:email_template) { create(:email_template) }
    let(:user) { email_template.created_by }

    before do
      allow(TenantEmailTemplateUsagePublisher).to receive(:publish)
      Thread.current[:auth] = build(:auth_data, :email_template_with_update_access, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
    end

    context 'valid' do
      context 'when user has update permission and is template creator' do
        context 'when email template is inactive' do
          before { email_template.update(active: false) }

          it 'activates email template' do
            expect { described_class.call({ id: email_template.id, active: true }) }.to change(EmailTemplate.active, :count).by(1)
          end
        end

        context 'when email template is active' do
          before { email_template.update(active: true) }

          it 'deactivates email template' do
            expect { described_class.call({ id: email_template.id, active: false }) }.to change(EmailTemplate.active, :count).by(-1)
          end
        end
      end

      context 'when user has update all' do
        before do
          email_template.update(created_by: create(:user, tenant_id: user.tenant_id))
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('email_template', 'update').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('email_template', 'update_all').and_return(true)
        end

        context 'when email template is inactive' do
          before { email_template.update(active: false) }

          it 'activates email template' do
            expect { described_class.call({ id: email_template.id, active: true }) }.to change(EmailTemplate.active, :count).by(1)
          end
        end

        context 'when email template is active' do
          before { email_template.update(active: true) }

          it 'deactivates email template' do
            expect { described_class.call({ id: email_template.id, active: false }) }.to change(EmailTemplate.active, :count).by(-1)
          end
        end
      end
    end

    context 'when user does not have update permission' do
      before { expect_any_instance_of(Auth::Data).to receive(:can_access?).with('email_template', 'update').and_return(false) }

      it 'raises forbidden error' do
        expect { described_class.call({ id: email_template.id, active: true }) }.to raise_error(ExceptionHandler::Forbidden, '01601005')
      end
    end

    context 'when invalid id' do
      it 'raises not found error' do
        expect { described_class.call({ id: email_template.id + 1, active: true }) }.to raise_error(ExceptionHandler::NotFound, '01602001')
      end
    end

    context 'when user context is not present' do
      before { Thread.current[:auth] = nil }

      it 'raises authentication error' do
        expect(Rails.logger).to receive(:error).with('Unauthorised: User context missing in ActivateDeactivateEmailTemplate')
        expect { described_class.call({ id: email_template.id + 1, active: true }) }.to raise_error(ExceptionHandler::AuthenticationError, '01601005')
      end
    end

    context 'when user does not have update all or is not template creator' do
      before { email_template.update(created_by: create(:user, tenant_id: user.tenant_id)) }

      it 'raises forbidden error' do
        expect { described_class.call({ id: email_template.id, active: true }) }.to raise_error(ExceptionHandler::Forbidden, '01601005')
      end
    end
  end
end
