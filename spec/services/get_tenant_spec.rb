require 'rails_helper'

RSpec.describe GetTenant do
  describe '#call' do
    context 'when tenant details are requested' do
      before do
        token = build(:auth_token)
        Thread.current[:token] = token

        stub_request(:get, "#{SERVICE_IAM}/v1/tenants")
          .with(
            headers: {
              Authorization: "Bearer #{token}"
            }
          )
          .to_return( status: 200, body: file_fixture('tenant-account.json').read, headers: {})
      end

      it 'should return tenant details' do
        command = described_class.call(nil)

        expect(command.success?).to be(true)
        expect(command.result.keys).to match_array(%w[createdAt updatedAt createdBy updatedBy recordActions metaData id accountName industry timezone dateFormat language currency companyName website logo address taxIdentificationNumber city state zip country active confirmed confirmedAt planName hasLogo])
      end
    end
  end
end
