require 'rails_helper'

RSpec.describe GetFields::Deal do
  describe "#call" do
    before do
      @entity_label = JSON.parse(file_fixture('entity-labels-response.json').read)['DEAL']['displayName']
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      let(:deal_fields) { file_fixture('deal-fields.json') }

      before(:each) do
        stub_request(:get, SERVICE_DEAL + "/v1/deals/fields?custom-only=false&sort=createdAt,asc&page=0&size=100").
        with(
          headers: {
          "Authorization" => "Bearer #{ @token }"
          }).
        to_return(status: 200, body: deal_fields, headers: {})
      end

      it "returns fields in output" do
        result = described_class.call.result.first

        expect(result['id']).to eq(530)
        expect(result['displayName']).to eq("Name")
        expect(result['internalName']).to eq("name")
        expect(result['entity']).to eq('deal')
      end
    end

    context "with invalid input - " do
      context "when fields are not available" do
        it "raises 404 not found error" do
          stub_request(:get, SERVICE_DEAL + "/v1/deals/fields?custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 404, body: "", headers: {})

          expect { described_class.call.result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      context "when something went wrong while API processing" do
        it "raises 500 error" do
          stub_request(:get, SERVICE_DEAL + "/v1/deals/fields?custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 500, body: "", headers: {})

          expect { described_class.call.result }.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        end
      end

      context "when Api request invalid" do
        it "raises 400 error" do
          stub_request(:get, SERVICE_DEAL + "/v1/deals/fields?custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 400, body: "", headers: {})

          expect { described_class.call.result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end
    end
  end
end
