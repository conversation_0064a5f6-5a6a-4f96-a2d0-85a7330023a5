require 'rails_helper'

RSpec.describe GetPresignedUrlFromS3 do

  context 'Given file name' do
    before do
      @resource = instance_double(Aws::S3::Resource)
      @bucket = instance_double(Aws::S3::Bucket)
      @obj = instance_double(Aws::S3::Object)
      @file_name = 'tenant_99/user_11/121_old_file_123123123.jpg'
      allow(Aws::S3::Resource).to receive(:new).and_return(@resource)
      @user = create(:user, id: 12, tenant: create(:tenant, id: 14 ))
      @connected_account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: @user)
      auth_data =  build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      thread = Thread.current
      thread[:auth] = auth_data
      email_thread = create(:email_thread, owner: @user, tenant_id: @user.tenant_id)
      @email = create(:email, owner: @user, tenant: create(:tenant, id: 99), connected_account: @connected_account, email_thread: email_thread)
      @attachment = create(:attachment, id: 77, email: @email, file_name: @file_name)
      @params = { email_id: @email.id, attachment_id: @attachment.id }
    end

    context 'Attachment exists' do
      it 'should get presigned url of file from s3'do
        allow(@resource).to receive(:bucket).with(S3_EMAIL_BUCKET).and_return(@bucket)
        allow(@bucket).to receive(:object).with(@file_name).and_return(@obj)
        allow(@obj).to receive(:presigned_url).and_return('https://www.aws.com/files/***********.jpg')

        result = GetPresignedUrlFromS3.call(@attachment, S3_EMAIL_BUCKET).result
        expect(result).to eq({ file_name: 'old_file.jpg', url: 'https://www.aws.com/files/***********.jpg' })
      end
    end
  end
end
