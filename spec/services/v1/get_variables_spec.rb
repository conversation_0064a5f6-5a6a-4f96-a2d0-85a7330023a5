require 'rails_helper'

RSpec.describe V1::GetVariables do
  describe "#call" do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    RSpec.shared_examples "v1:should not contain excluded & checkbox fields" do
      it 'should not have any fields of type CHECKBOX' do
        expect(@result.select { |field| field['type'] == 'CHECKBOX' }).to eq([])
      end

      it 'should not have any excluded fields' do
        entity_fields = @result.select { |field| field['entity'] == entity }
                                .map { |field| field['internalName'] }
        expect(
          entity_fields & "#{entity.upcase}_EXCLUDED_FIELDS".constantize
        ).to eq([])
      end
    end

    context "with valid input for Lead " do
      let(:entity) { 'lead' }

      before do
        stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?entityType=lead&custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
              "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 200, body: file_fixture('lead-fields.json').read, headers: {})
        @result = described_class.call(entity).result
      end

      it 'returns fields for lead in output' do
        res = @result.select { |field| field['entity'] == 'lead' }.first
        expect(res['id']).to eq(509)
        expect(res['displayName']).to eq("First Name1 ")
        expect(res['internalName']).to eq("firstName")
      end

      include_examples 'v1:should not contain excluded & checkbox fields'
    end

    context "with valid input for Contact " do
      let(:entity) { 'contact' }

      before do
        stub_request(:get, SERVICE_CONFIG + "/v1/entities/contact/fields?entityType=contact&custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
              "Authorization" => "Bearer #{ @token }"
            }).
            to_return(status: 200, body: file_fixture('contact-fields.json'), headers: {})

        @result = described_class.call(entity).result
      end

      it "returns fields for contact in output" do
        res = @result.select { |field| field['entity'] == 'contact' }.first
        expect(res['id']).to eq(601)
        expect(res['displayName']).to eq("Salutation")
        expect(res['internalName']).to eq("salutation")
      end

      include_examples 'v1:should not contain excluded & checkbox fields'
    end

    context "with valid input for Deal " do
      let(:entity) { 'deal' }

      before do
        stub_request(:get, SERVICE_DEAL + "/v1/deals/fields?custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
              "Authorization" => "Bearer #{ @token }"
            }).
            to_return(status: 200, body: file_fixture('deal-fields.json'), headers: {})

        @result = described_class.call(entity).result
      end

      it "returns fields for deal in output" do
        res = @result.select { |field| field['entity'] == 'deal' }.first
        expect(res['displayName']).to eq("Name")
        expect(res['internalName']).to eq("name")
      end

      include_examples 'v1:should not contain excluded & checkbox fields'
    end

    context 'with valid input for Call Log ' do
      let(:entity) { 'call_log' }

      before do
        stub_request(:get, "#{SERVICE_CALL}/v1/call-logs/fields").
          with(
            headers: {
              'Authorization' => "Bearer #{@token}"
              }).
            to_return(status: 200, body: file_fixture('call-fields.json'), headers: {}
          )

        @result = described_class.call(entity).result
      end

      it 'returns fields for call log in output' do
        first_call_field = @result.select { |field| field['entity'] == 'call_log' }
        expect(first_call_field.first['displayName']).to eq('ID')
        expect(first_call_field.first['internalName']).to eq('id')
      end

      include_examples 'v1:should not contain excluded & checkbox fields'
    end

    context 'with valid input for Meeting' do
      let(:entity) { 'meeting' }

      before do
        stub_request(:get, "#{SERVICE_MEETING}/v1/meetings/fields").
          with(
            headers: {
              'Authorization' => "Bearer #{@token}"
              }).
            to_return(status: 200, body: file_fixture('meeting-fields.json'), headers: {}
          )

        @result = described_class.call(entity).result
      end

      it 'returns fields for meeting in output' do
        first_meeting_field = @result.select { |field| field['entity'] == 'meeting' }
        expect(first_meeting_field.first['displayName']).to eq('ID')
        expect(first_meeting_field.first['internalName']).to eq('id')
      end

      include_examples 'v1:should not contain excluded & checkbox fields'
    end

    context 'with valid input for Task' do
      let(:entity) { 'task' }

      before do
        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/task/fields?custom-only=false&entityType=task&page=0&size=100&sort=createdAt,asc").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('task-fields.json'), headers: {})

        @result = described_class.call(entity).result
      end

      it 'returns field for task in output' do
        first_task_field = @result.first
        expect(first_task_field['displayName']).to eq('Task Name')
        expect(first_task_field['internalName']).to eq('name')
      end

      include_examples 'v1:should not contain excluded & checkbox fields'
    end

    context 'with invalid category' do
      it 'should raise invalid data error' do
        expect do
          described_class.call('invalid').result
        end.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      end
    end
  end
end
