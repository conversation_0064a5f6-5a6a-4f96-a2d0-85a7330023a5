require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForUsageLimitChanged do
  describe '#call' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic USER_EXCHANGE
    end

    context 'valid input' do
      before do

        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(USER_EXCHANGE)
          .with(EMAIL_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(USER_EXCHANGE,USAGE_LIMIT_CHANGED_EVENT, USAGE_LIMIT_CHANGED_QUEUE)
          .and_yield(payload.to_s)
      end

      context 'Usage limit changed event' do
        let(:routing_key) { USAGE_LIMIT_CHANGED_EVENT }
        let(:payload)     { { "userId": 1, "tenantId": 1, "planId": "embark", "usageEntityLimits": [ { "usageEntity": "EMAIL_TEMPLATES", "limit": 10.0 }, { "usageEntity": "EMAIL_TRACKING", "limit": 1.0 } ] }.to_json }

        context 'when more email templates are active than the limit' do
          before do
            @templates = create_list(:email_template, 15, active: true, tenant_id: 1) do |template, i|
              template.created_at = template.updated_at = i.minutes.ago
              template.save!
            end
            ListenForUsageLimitChanged.call()
          end

          it 'sets the active status correctly for the email templates of the tenant' do
            expect(EmailTemplate.where(tenant_id: 1, active: true).map(&:id)).to match_array(@templates.first(10).map(&:id))
            expect(EmailTemplate.where(tenant_id: 1, active: false).map(&:id)).to match_array(@templates.last(5).map(&:id))
          end
        end

        context 'when less email templates are active than the limit' do
          before do
            @templates = create_list(:email_template, 15, tenant_id: 1) do |template, i|
              template.active = i.even?
              template.created_at = template.updated_at = i.minutes.ago
              template.save!
            end
            ListenForUsageLimitChanged.call()
          end

          it 'sets the active status correctly for the email templates of the tenant' do
            expect(EmailTemplate.all.order(created_at: :desc).map(&:active)).to eq(@templates.map(&:active))
          end
        end

        context 'when other tenants template limit is changed' do
          before do
            @templates = []
            [1,2].each do |tenant_id|
              templates = create_list(:email_template, 15, active: true, tenant_id: tenant_id) do |template, i|
                template.created_at = template.updated_at = i.minutes.ago
                template.save!
                @templates << template
              end
            end

            ListenForUsageLimitChanged.call()
          end

          it 'only affects specified tenants email template limit' do
            expect(EmailTemplate.where(tenant_id: 1, active: true).map(&:id)).to match_array(@templates[0, 10].map(&:id))
            expect(EmailTemplate.where(tenant_id: 1, active: false).map(&:id)).to match_array(@templates[10, 5].map(&:id))
          end

          it "doesn't affect rest tenants email template limits" do
            expect(EmailTemplate.where(tenant_id: 2).map(&:active)).to eq(@templates[14, 15].map(&:active))
          end
        end

        context 'when email tracking is enabled' do
          context 'when tenant is present in database' do
            before do
              @tenant = create(:tenant, id: 1, tracking_enabled: false)
              ListenForUsageLimitChanged.call()
            end

            it 'sets tracking_enabled to true for tenant id 1' do
              expect(@tenant.reload.tracking_enabled).to eq true
              expect(Tenant.count).to eq 1
            end
          end

          context 'when tenant is not present in database' do

            before do
              Tenant.destroy_all
              ListenForUsageLimitChanged.call()
            end

            it 'creates tenant in database and sets tracking enabled to true' do
              expect(Tenant.count).to eq 1
              tenant = Tenant.last
              expect(tenant.id).to eq 1
              expect(tenant.reload.tracking_enabled).to eq true
            end
          end
        end
      end
    end
  end
end
