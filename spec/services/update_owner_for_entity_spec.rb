# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UpdateOwnerForEntity do
  describe "#call" do
    before do
      @entity_type = LOOKUP_LEAD
      @lookup = create(:look_up, entity_type: @entity_type, entity_id: 1, tenant_id: 1, owner_id: 12)
      @data = { entity_id: 1, new_owner_id: 5, tenant_id: 1 }
    end

    context 'valid' do
      it 'should update look up with new owner id' do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::LookUpUpdated)).once
        expect(Rails.logger).to receive(:info).with(/UpdateOwnerForEntity LookUp updated/)
        expect{ described_class.call(@entity_type, @data) }.to change { @lookup.reload.owner_id }
        expect(@lookup.reload.owner_id).to eq(5)
      end
    end

    context 'invalid' do
      context 'when lookup is not present' do
        it 'should not update lookup' do
          expect(PublishEvent).not_to receive(:call).with(instance_of(Event::LookUpUpdated))
          expect{ described_class.call(@entity_type, @data.merge(entity_id: 2)) }.not_to change { @lookup.reload.owner_id }
        end
      end

      context 'when new owner id is not present' do
        it 'should not update lookup' do
          expect(PublishEvent).not_to receive(:call).with(instance_of(Event::LookUpUpdated))
          expect{ described_class.call(@entity_type, @data.slice(:entity_id)) }.not_to change { @lookup.reload.owner_id }
        end
      end
    end
  end
end
