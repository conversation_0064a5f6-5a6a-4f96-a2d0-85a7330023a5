require 'rails_helper'

RSpec.describe GetUsersWithPicklistValues do
  describe "Get users Data" do
    before do
      stub_entity_labels_api
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      let(:user_data) { file_fixture('user-response.json').read }
      let(:standard_picklist) { file_fixture('standard-picklist.json').read }
      let(:processed_user_data){ JSON.parse(file_fixture('processed-user-data.json').read) }

      before(:each) do
        stub_request(:post, SERVICE_IAM + "/v1/users/search?sort=updatedAt,desc&page=0&size=100").
          with(
            headers: {
              Authorization: "Bearer #{@token}"
            },
            body: "{\"fields\":[\"salutation\",\"firstName\",\"lastName\",\"phoneNumbers\",\"email\",\"designation\",\"currency\",\"profileId\",\"signature\",\"timezone\",\"id\"],\"jsonRule\":{\"rules\":[{\"field\":\"id\",\"id\":\"id\",\"operator\":\"in\",\"type\":\"double\",\"value\":\"4167,6781\"}],\"condition\":\"AND\",\"valid\":true}}").
          to_return(status: 200, body: user_data, headers: {})

        stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}"
            }).
          to_return(status: 200, body: standard_picklist, headers: {})
      end

      it "returns processed tenant data in output" do
        command = described_class.call({ createdBy: 4167, updatedBy: 4167, owner: 6781 })
        expect(command.success?).to be(true)
        expect(command.result).to eq(processed_user_data)
      end
    end
  end
end
