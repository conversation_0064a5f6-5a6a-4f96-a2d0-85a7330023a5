# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DeleteEmail do
  describe '#call' do
    let(:user) { create(:user) }
    let(:auth_data) { build(:auth_data, :email_with_delete_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id).token }
    let(:email_thread) { create(:email_thread, owner: user, tenant_id: user.tenant_id) }
    let(:email) { create(:email, owner: user, tenant_id: user.tenant_id, email_thread: email_thread) }

    context 'success' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = token

        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailDeletedWorkflowV2))
        
        expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id)
      end

      it 'deletes email' do
        described_class.call(email.id).result
        expect(Email.find_by(id: email.id)).to be_nil
      end

      it 'deletes email thread if this was the last email of thread' do
        described_class.call(email.id).result
        expect(EmailThread.find_by(id: email_thread.id)).to be_nil
      end

      it 'does not delete email thread when other emails are present in thread' do
        create(:email, email_thread: email_thread)

        described_class.call(email.id).result
        expect(EmailThread.find_by(id: email_thread.id)).to eq(email_thread)
      end

      it 'deletes attachments' do
        attachment = create(:attachment, email: email)
        expect(DeleteFileFromS3).to receive(:call).with([attachment.file_name], S3_EMAIL_BUCKET)

        described_class.call(email.id).result
        expect(Attachment.find_by(id: attachment.id)).to be_nil
      end
    end

    context 'failure' do
      context 'when email not found' do
        before do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = token
        end

        it 'raises not found error' do
          expect do
            described_class.call(email.id + 1)
          end.to raise_error(ExceptionHandler::NotFound, '01602001')
        end
      end

      context 'when user does not have delete permission on email' do
        before do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = token

          another_user = create(:user, tenant_id: user.tenant_id)
          email.update(owner: another_user)
          email_thread.update(owner: another_user)
        end

        it 'raises forbidden error' do
          expect do
            described_class.call(email.id)
          end.to raise_error(ExceptionHandler::Forbidden, '01601005')
        end
      end

      context 'when error while destroying email' do
        before do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = token

          allow_any_instance_of(Email).to receive(:destroy!).and_raise(StandardError, 'Unable to destroy email')
        end

        it 'raises invalid error' do
          expect(Rails.logger).to receive(:error).with('Error while deleting email. Message Unable to destroy email')
          expect do
            described_class.call(email.id)
          end.to raise_error(ExceptionHandler::InvalidDataError, '01603001')
        end
      end

      context 'when user context is not present' do
        before do
          Thread.current[:auth] = nil
          Thread.current[:token] = nil
        end

        it 'raises authentication error' do
          expect(Rails.logger).to receive(:error).with('Unauthorised: Missing user context in DeleteEmail')
          expect do
            described_class.call(email.id)
          end.to raise_error(ExceptionHandler::AuthenticationError, '01601005')
        end
      end
    end
  end
end
