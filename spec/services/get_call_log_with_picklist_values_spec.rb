# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetCallLogWithPicklistValues do
  describe "Get CallLog Data" do
    before do
      stub_entity_labels_api
      @call_log_id = 1815
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'with valid input' do
      before(:each) do
        stub_request(:get, "#{SERVICE_IAM}/v1/users/me").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}",
            'Accept'=>'application/json',
            'Content-Type'=>'application/json'
          }).
          to_return(status: 200, body: file_fixture('user-profile-response.json'), headers: {})

        stub_request(:get, "#{SERVICE_CALL}/v1/call-logs/fields").
          with(
            headers: {
              'Authorization' => "Bearer #{@token}"
              }).
            to_return(status: 200, body: file_fixture('call-fields.json'), headers: {}
          )

        stub_request(:get, "#{SERVICE_CALL}/v1/call-logs/#{@call_log_id}").
          with(
            headers: {
              'Authorization' => "Bearer #{@token}"
              }).
            to_return(status: 200, body: file_fixture('call-details.json'), headers: {}
          )

        stub_request(:get, "#{SERVICE_CONFIG}/v1/picklists/standard").
          with(
            headers: {
            "Authorization" => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('standard-picklist.json'), headers: {})
      end

      it 'returns processed call log data in output' do
        command = GetCallLogWithPicklistValues.call(@call_log_id)
        expect(command.result.first).to eq(JSON.parse(file_fixture('processed-call-log-data.json').read))
        expect(command.result.last).to eq({ "createdBy" => 4167, "updatedBy" => 4167, "owner" => 6781 })
      end
    end
  end
end
