require 'rails_helper'

RSpec.describe GetLeadWithPicklistValues do
  describe "Get Lead Data" do
    before do
      stub_entity_labels_api
      @lead_id = 217416
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      let(:lead_data) { file_fixture('lead-data.json').read }
      let(:standard_picklist) { file_fixture('standard-picklist.json').read }
      let(:user_data){ file_fixture('user-profile-response.json').read }
      let(:lead_fields){ file_fixture('lead-fields.json').read }
      let(:processed_lead_data){ JSON.parse(file_fixture('processed-lead-data.json').read) }

      before(:each) do
        stub_request(:get, SERVICE_SALES + "/v1/leads/#{@lead_id}").
          with(
            headers: {
            "Authorization" => "Bearer #{@token}"
            }).
          to_return(status: 200, body: lead_data, headers: {})

        stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
          with(
            headers: {
            "Authorization" => "Bearer #{@token}"
            }).
          to_return(status: 200, body: standard_picklist, headers: {})

        stub_request(:get, SERVICE_IAM + "/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }).
          to_return(status: 200, body: user_data, headers: {})

        stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?entityType=lead&custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
            "Authorization" => "Bearer #{@token}"
            }).
          to_return(status: 200, body: lead_fields, headers: {})
      end

      it "returns processed lead data in output" do
        command = GetLeadWithPicklistValues.call(@lead_id)
        expect(command.result.first).to eq(processed_lead_data)
        expect(command.result.last).to eq({ "createdBy" => 11, "updatedBy" => 11, "convertedBy" => 11, "importedBy" => 11, "ownerId" => 3397 })
      end
    end
  end
end
