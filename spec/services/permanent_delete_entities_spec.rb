# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PermanentDeleteEntities do
  describe "#call" do
    context 'when called' do
      before do
        allow(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata))
      end

      context 'when threads are marked for delete' do
        before do
          thread1 = create(:email_thread)
          thread2 = create(:email_thread)
          create_list(:email, 2, email_thread_id: thread1.id, deleted: true)
          create_list(:email, 2, email_thread_id: thread2.id)
          thread1.update!(deleted: true)
        end

        it 'permanently deletes all soft deleted entities' do
          described_class.call
          expect(EmailThread.unscoped.count).to eq 1
          expect(Email.unscoped.count).to eq 2
        end
      end

      context 'when specific emails are marked for delete' do
        before do
          email_marked_for_delete = create(:email)
          lead_lookup = create(:look_up, entity: 'lead_12')
          email_marked_for_delete.to_recipients << lead_lookup
          create(:email_track_log, email: email_marked_for_delete)
          create(:email_link_log, email: email_marked_for_delete)
          create(:track_mapping, email: email_marked_for_delete)
          create(:link_mapping, email: email_marked_for_delete)
          email_marked_for_delete.update_column(:deleted, true)
          lead_lookup.update_column(:deleted, true)
          EmailLookUp.unscoped.where(email_id: email_marked_for_delete.id).update_all(deleted: true)
          create(:email)
        end

        it 'should permanently delete all soft deleted entities' do
          expect { described_class.call }
            .to change(Email.unscoped, :count).by(-1)
            .and change(EmailLookUp.unscoped, :count).by(-1)
            .and change(LookUp.unscoped, :count).by(-1)
            .and change(TrackMapping, :count).by(-1)
            .and change(LinkMapping, :count).by(-1)
            .and change(EmailTrackLog, :count).by(-1)
            .and change(EmailLinkLog, :count).by(-1)
        end
      end
    end
  end
end
