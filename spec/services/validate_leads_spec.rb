require 'rails_helper'

RSpec.describe ValidateLeads do
  describe '#call' do
    let(:lead_search_response) { JSON.parse(file_fixture('lead-search-response.json').read) }

    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @lead = build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 9, tenant_id: 10)
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
      lead_search_response['content'].first['emails'].first['value'] = @lead.email
      stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=1000&sort=updatedAt,desc").
        with(
          headers: {
            Authorization: "Bearer #{@token}"
          },
          body: {
            fields: %w[id firstName lastName emails ownerId],
            jsonRule:{
              condition: 'AND',
              rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: '9' }],
              valid: true
            }
          }
        ).
        to_return(status: 200, body: lead_search_response.to_json, headers: {})
    end

    context 'valid input' do
      it ' returns if no users are passed' do
        expect(ValidateLeads.call(nil).success?).to be true
      end

      context 'when email matches with primary email' do
        it 'returns array of updated user look ups' do
          command = ValidateLeads.call([@lead])
          expect(command.result.class).to be Array
          expect(command.result.count).to be == 1
          expect(command.result[0].class).to be == LookUp
        end

        it 'returns lookups with updated names' do
          command = ValidateLeads.call([@lead])
          expect(command.result.first[:name]).to eq('Email Lead')
          expect(command.result.first.owner_id).to eq(4167)
        end
      end

      context 'when email matches with secondary email' do
        before do
          lead_search_response['content'].first['emails'].first['value'] = '<EMAIL>'
          lead_search_response['content'].first['emails'].second['value'] = @lead.email
          stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=1000&sort=updatedAt,desc").
            with(
              headers: {
                Authorization: "Bearer #{@token}"
              },
              body: {
                fields: %w[id firstName lastName emails ownerId],
                jsonRule:{
                  condition: 'AND',
                  rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: '9' }],
                  valid: true
                }
              }
            ).
            to_return(status: 200, body: lead_search_response.to_json, headers: {})
        end

        it 'returns array of updated user look ups' do
          command = ValidateLeads.call([@lead])
          expect(command.result.class).to be Array
          expect(command.result.count).to be == 1
          expect(command.result[0].class).to be == LookUp
        end

        it 'returns lookups with updated names' do
          command = ValidateLeads.call([@lead])
          expect(command.result.first[:name]).to eq('Email Lead')
          expect(command.result.first.owner_id).to eq(4167)
        end
      end
    end

    context 'invalid entry from user' do
      before do
        @invalid_lead = build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 10, tenant_id: 9)
        stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=1000&sort=updatedAt,desc").
            with(
              headers: {
                Authorization: "Bearer #{@token}"
              },
              body: {
                fields: %w[id firstName lastName emails ownerId],
                jsonRule:{
                  condition: 'AND',
                  rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: '9,10' }],
                  valid: true
                }
              }
            ).
            to_return(status: 200, body: lead_search_response.to_json, headers: {})
      end
      it 'invalid InvalidDataError for non-existent user' do
        expect {ValidateLeads.call([@lead, @invalid_lead])}.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid
        )
      end
    end

    context 'user email mismatch' do
      before do
        lead_search_response['content'].first['emails'].first['value'] = '<EMAIL>'
        lead_search_response['content'].first['emails'].second['value'] = '<EMAIL>'
        stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=1000&sort=updatedAt,desc").
          with(
            headers: {
              Authorization: "Bearer #{@token}"
            },
            body: {
              fields: %w[id firstName lastName emails ownerId],
              jsonRule:{
                condition: 'AND',
                rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: '9' }],
                valid: true
              }
            }
          ).
          to_return(status: 200, body: lead_search_response.to_json, headers: {})
      end
      it 'raises InvalidDataError for contact whos email doesnt match' do
        expect {ValidateLeads.call([@lead])}.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid
        )
      end
    end
  end
end
