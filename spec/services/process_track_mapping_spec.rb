require 'rails_helper'
require 'bunny-mock'

RSpec.describe ProcessTrackMapping do
  describe '#call' do

    context 'when sent email is tracked' do
      let!(:track_mapping) { create(:track_mapping) }
      
      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction)).exactly(1).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailUpdatedV2)).exactly(1).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailOpened)).exactly(1).times

        ProcessTrackMapping.call(track_mapping.id)
      end
      
      it 'create email_track_log' do
        expect(EmailTrackLog.count).to eq 1
        expect(EmailTrackLog.last.email).to eq track_mapping.email
        expect(EmailTrackLog.last.tenant).to eq track_mapping.tenant
      end

      it 'sets email status as opened' do
        expect(EmailTrackLog.last.email.status).to eq 'opened'
      end
    end

    context 'when tracked email is deleted (i.e track mapping does not exist)' do
      it 'returns nil' do
        res = ProcessTrackMapping.call(123).result
        expect(res).to eq nil
        expect(EmailTrackLog.count).to eq 0
      end
    end

    context 'when email is already opened' do
      let!(:track_mapping) { create(:track_mapping) }
      let!(:email) { track_mapping.email }

      before do
      email.update(status: Email.statuses['opened'])
      expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailUpdatedV2)).exactly(1).times
      expect(PublishEvent).not_to receive(:call).with(instance_of(Event::EmailOpened))
      expect(PublishEvent).not_to receive(:call).with(instance_of(Event::EmailAction))
      ProcessTrackMapping.call(track_mapping.id)
      end

      it 'does not change email status' do
      expect(email.reload.status).to eq 'opened'
      end

      it 'creates email_track_log' do
      expect(EmailTrackLog.count).to eq 1
      expect(EmailTrackLog.last.email).to eq track_mapping.email
      expect(EmailTrackLog.last.tenant).to eq track_mapping.tenant
      end
    end
  end
end
