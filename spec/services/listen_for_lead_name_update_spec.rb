require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForLeadNameUpdate do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic LEAD_EXCHANGE
      @lead = create(:look_up, entity_type: 'lead', tenant_id: @user.tenant_id)
    end

    context 'valid input' do
      before do

        expect(PublishEvent).to receive(:call).with(instance_of(Event::LookUpUpdated)).once
        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(LEAD_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(LEAD_EXCHANGE, LEAD_NAME_UPDATED_EVENT, LEAD_NAME_UPDATED_QUEUE)
          .and_yield(payload.to_s)
        ListenForLeadNameUpdate.call()
      end

      context 'lead name updated event' do
        let(:routing_key) { LEAD_NAME_UPDATED_EVENT }
        let(:payload)     { { "leadId" => @lead.entity_id, "tenantId" => @user.tenant_id, "firstName" => "John", "lastName" => "Doe" }.to_json }

        it 'updates the User with new name' do
          expect(@lead.reload.name).to be == "John Doe"
        end
      end
    end
  end
end
