require 'rails_helper'

RSpec.describe MarkAsReadUnread do
  before do
    @user = create(:user, id: 12, tenant: create(:tenant, id: 14) )
    user_lookup = build(:look_up, entity_type: LOOKUP_USER, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name)
    @connected_account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: @user, tenant_id: @user.tenant_id)
    thread = create(:email_thread, owner: @user, tenant_id: @user.tenant_id)
    @email = create(:email, owner: @user, tenant_id: @user.tenant_id, connected_account: @connected_account, email_thread_id: thread.id)
    @email.related_to << user_lookup
    @email.save!
  end

  context 'For Single Email' do
    context 'When given email is associated with current user' do
      before do
        auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data
      end

      it 'should mark email as read' do
        MarkAsR<PERSON>Unread.call({ email_id: @email.id, read: true })
        expect(@email.reload.read?).to be_truthy
      end

      it 'should return true when thread is completely read' do
        is_thread_read = described_class.call({ email_id: @email.id, read: true }).result
        expect(is_thread_read).to be_truthy
      end

      it 'should return false when at least one of the emails is unread' do
        create(:email, tenant_id: @user.tenant_id, email_thread_id: @email.email_thread_id)

        is_thread_read = described_class.call({ email_id: @email.id, read: true }).result
        expect(is_thread_read).to be_falsey
      end

      it 'should mark email as unread' do
        MarkAsReadUnread.call({ email_id: @email.id, read: false })
        expect(@email.reload.read?).to be_falsey
      end

      it 'should return true when thread is completely read' do
        is_thread_read = described_class.call({ email_id: @email.id, read: false }).result
        expect(is_thread_read).to be_falsey
      end
    end

    context 'When no user context available' do
      before do
        thread = Thread.current
        thread[:auth] = nil
      end

      it 'should throw AuthenticationError' do
        expect{ MarkAsReadUnread.call({ email_id: @email.id, read: true }) }.to raise_error(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
      end
    end

    context 'when user does not have access to email' do
      before do
        @another_user = create(:user, tenant: @user.tenant)
        auth_data = build(:auth_data, user_id: @another_user.id, tenant_id: @another_user.tenant_id, username: @another_user.name)
        Thread.current[:auth] = auth_data
        allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => {} })
      end

      it 'should raise & log unauthorised error' do
        expect(Rails.logger).to receive(:error).with("Unauthorised: User cannot mark email as read / unread. User id #{@another_user.id} Email id #{@email.id}")
        expect do
          MarkAsReadUnread.call({ email_id: @email.id, read: true })
        end.to raise_error(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
      end
    end

    context 'When sender try to mark email as read or unread' do
      before do
        another_user = create(:user, id: 199, tenant_id: 14)
        another_user_lookup = create(:look_up, entity_id: another_user.id, entity_type: LOOKUP_USER)
        auth_data = build(:auth_data, user_id: another_user.id, tenant_id: @user.tenant_id, username: another_user.name)
        thread = Thread.current
        thread[:auth] = auth_data
        @email.update(sender: another_user_lookup)
      end

      # TODO: Enable this once validation is fixed
      xit 'should throw InvalidDataError' do
        expect{ MarkAsReadUnread.call({ email_id: @email.id, read: true }) }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      end
    end

    context 'When given email not found' do
      before do
        auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
        thread = Thread.current
        thread[:auth] = auth_data
      end

      it 'should throw record not found exception' do
        expect{ MarkAsReadUnread.call({ email_id: 'random_id', read: true }) }.to raise_error(ActiveRecord::RecordNotFound, ErrorCode.not_found("email: #{@email.id}"))
      end
    end
  end

  context 'For Email thread' do
    before do
      @new_thread = create(:email_thread, owner: @user, tenant_id: @user.tenant_id)
      create(:email,owner: @user, tenant_id: @user.tenant_id, connected_account: @connected_account, email_thread_id: @new_thread.id)
      create(:email,owner: @user, tenant_id: @user.tenant_id, connected_account: @connected_account, email_thread_id: @new_thread.id, read_by: [@user.id, 344])
      auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      thread = Thread.current
      thread[:auth] = auth_data
      allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => {} })
    end

    context 'when user try to mark as read' do
      it 'should mark all the unread emails in thread as read' do
        MarkAsReadUnread.call({ thread_id: @new_thread.id, read: true })
        expect(Email.where(email_thread_id: @new_thread.id).all?(&:read?)).to be_truthy
        expect(Email.where.not(email_thread_id: @new_thread.id).all?(&:read?)).to be_falsey
      end
    end

    context 'when user try to mark as un read' do
      it 'should mark all the unread emails in thread as unread' do
        MarkAsReadUnread.call({ thread_id: @new_thread.id, read: false })
        expect(Email.where(email_thread_id: @new_thread.id).all?(&:read?)).to be_falsey
      end
    end

    context 'when thread does not exist or no emails on thread' do
      it 'raises not found error' do
        expect(Rails.logger).to receive(:error).with('Emails not found for thread while mark thread as read / unread')
        expect do
          MarkAsReadUnread.call({ thread_id: @new_thread.id + 1, read: false })
        end.to raise_error(ExceptionHandler::NotFound, ErrorCode.not_found("email_thread: #{@new_thread.id + 1}"))
      end
    end

    context 'when user does not have access to email thread' do
      before do
        @another_user = create(:user, tenant: @user.tenant)
        auth_data = build(:auth_data, user_id: @another_user.id, tenant_id: @another_user.tenant_id, username: @another_user.name)
        Thread.current[:auth] = auth_data
      end

      it 'should raise & log unauthorised error' do
        expect(Rails.logger).to receive(:error).with("Unauthorised: User cannot mark thread as read / unread. User id #{@another_user.id} Thread id #{@new_thread.id}")
        expect do
          MarkAsReadUnread.call({ thread_id: @new_thread.id, read: true })
        end.to raise_error(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
      end
    end
  end
end
