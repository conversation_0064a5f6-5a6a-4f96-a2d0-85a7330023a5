# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetEmailHistory do
  describe "#call" do
    let(:user) { create(:user) }
    let(:auth_data) { build(:auth_data, :email_with_delete_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id).token }
    let(:email_thread) { create(:email_thread, owner: user, tenant_id: user.tenant_id) }
    let(:emails) { create_list(:email, 10, owner: user, tenant_id: user.tenant_id, email_thread: email_thread) }

    before do
      thread = Thread.current
      thread[:auth] = auth_data
      thread[:token] = token
      allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => {} })
    end

    context "with valid input - " do
      it "returns email history for given email_id in output" do
        result = described_class.call({id: emails.second.id, page: 1, size:3}).result
        expect(result.count).to eq 2
      end
    end

    context "with invalid input - " do
      context "when unauthorized" do
        before { Thread.current[:auth] = nil }

        it 'should raise authentication error' do
          expect(Rails.logger).to receive(:error).with("Unauthorised: Missing user context in GetEmailHistory")

          expect do
            described_class.call({id: emails.first.id})
          end.to raise_error(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
        end
      end

      context "when email is not present" do
        before do
          thread = Thread.current
          thread[:auth] = auth_data
          thread[:token] = token
        end

        it 'should raise not found error' do
          expect do
            described_class.call({id: 324324})
          end.to raise_error(ExceptionHandler::NotFound, ErrorCode.not_found)
        end
      end
    end
  end
end
