require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForContactDelete do
  describe '#call' do
    before do
      @user = create(:user)
      @contact_lookup = create(:look_up, entity: 'contact_12')
      allow(RabbitmqConnection).to receive(:subscribe).with(
        CONTACT_EXCHANGE,
        CONTACT_DELETED_EVENT,
        CONTACT_DELETED_QUEUE
      ).and_yield(payload.to_s)
    end

    context 'valid input' do
      context 'for contact deleted event' do
        let(:payload) do
          {
            'id' => @contact_lookup.entity_id,
            'tenantId' => @contact_lookup.tenant_id,
            'userId' => @user.id,
            'publishUsage' => true
          }.to_json
        end

        it 'should call relevant job with parameters' do
          expected_options = { id: @contact_lookup.entity_id, tenant_id: @contact_lookup.tenant_id, user_id: @user.id, publish_usage: true, entity_type: LOOKUP_CONTACT }
          expect(UpdateEmailForEntityDeleteEventJob).to receive(:perform_later).with(expected_options)
          described_class.call
        end
      end
    end
  end
end
