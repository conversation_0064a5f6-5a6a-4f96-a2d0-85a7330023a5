# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetTaskWithPicklistValues do
  describe "Get Task Data" do
    before do
      stub_entity_labels_api
      @task_id = 5884
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'with valid input' do
      before(:each) do
        stub_request(:get, "#{SERVICE_IAM}/v1/users/me").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}",
            'Accept'=>'application/json',
            'Content-Type'=>'application/json'
          }).
          to_return(status: 200, body: file_fixture('user-profile-response.json'), headers: {})

        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/task/fields?custom-only=false&entityType=task&page=0&size=100&sort=createdAt,asc").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('task-fields.json'), headers: {}
        )

        stub_request(:get, "#{SERVICE_PRODUCTIVITY}/v1/tasks/#{@task_id}").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('task-details.json'), headers: {}
        )

        stub_request(:get, "#{SERVICE_SALES}/v1/contacts/120773").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('contact-data.json'), headers: {}
        )

        stub_request(:get, "#{SERVICE_SALES}/v1/leads/341109").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('lead-data.json'), headers: {}
        )

        stub_request(:get, "#{SERVICE_DEAL}/v1/deals/25596").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
            }).
          to_return(status: 200, body: file_fixture('deal-data.json'), headers: {}
        )

        stub_request(:get, SERVICE_SEARCH + "/v1/summaries/companies/idName?id=25597").
        with(
          headers: {
          "Authorization" => "Bearer #{ @token }"
          }).
        to_return(status: 200, body: [{ "id": 25597, "name": "Dr. Stone" }].to_json, headers: {})
      end

      it 'returns processed task data in output' do
        command = GetTaskWithPicklistValues.call(@task_id)
        expect(command.result.first).to eq(JSON.parse(file_fixture('processed-task-data.json').read))
        expect(command.result.last).to eq({ "assignedTo"=>4010, "createdBy"=>4010, "ownerId"=>4010, "updatedBy"=>4010 })
      end
    end
  end
end
