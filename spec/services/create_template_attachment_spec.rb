require 'rails_helper'

RSpec.describe CreateTemplateAttachment do
  describe '#call' do
    before do
      allow(TenantEmailTemplateUsagePublisher).to receive(:publish).with(instance_of(Integer))
      user = create(:user, id: 12, tenant: create(:tenant, id: 99))
      @attachment = File.new("#{Rails.root}/spec/fixtures/files/test_photo.jpg")
      auth_data =  build(:auth_data, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
      thread = Thread.current
      thread[:auth] = auth_data
      @email_template = create(:email_template, tenant_id: user.tenant_id, created_by: user)
      @files = [
        {
          'data' => @attachment,
          'fileName' => 'test.jpg'
      }]
    end

    it 'should associate attachment with email' do
      allow(UploadFileToS3).to receive(:call).with(@attachment.path, /tenant_99\/user_12\/[0-9]+_test_[^>]*.jpg/, S3_EMAIL_TEMPLATE_BUCKET).and_return(nil)
      CreateTemplateAttachment.call(@email_template, @files)
      expect(@email_template.template_attachments.count).to eq 1
    end
  end
end
