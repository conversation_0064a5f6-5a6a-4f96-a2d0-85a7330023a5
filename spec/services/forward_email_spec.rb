require 'google/apis/gmail_v1'
require 'rails_helper'
require 'bunny-mock'

RSpec.describe ForwardEmail do
  before do
    connection = BunnyMock.new
    channel = connection.start.channel
    exchange = channel.topic EMAIL_EXCHANGE

    queue = channel.queue "email.update.lead.metaInfo"
    queue.bind exchange, routing_key: "email.update.lead.metaInfo"

    queue = channel.queue "email.update.deal.metaInfo"
    queue.bind exchange, routing_key: "email.update.deal.metaInfo"
    allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(queue)
  end

  before do
    @user = create(:user, id: 12, tenant: create(:tenant, id: 14))
    user_lookup = build(:look_up, entity_type: LOOKUP_USER, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name)
    @connected_account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: @user, tenant: @user.tenant)
    thread = create(:email_thread, owner: @user, tenant_id: @user.tenant_id)
    @email = create(:email, owner: @user, tenant_id: @user.tenant_id, connected_account: @connected_account, email_thread_id: thread.id)
    @email.related_to << user_lookup
    @email.save!
    header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
    payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
    return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
    allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
    allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
    setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
    allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', @connected_account.email).and_return(setting)
  end

  context 'When sender is owner of current email_thread' do
    before do
      auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      thread = Thread.current
      thread[:auth] = auth_data
      @params = {
        id: @email.id,
        subject: 'FW: test',
        body: 'some text',
        relatedTo: {entity: 'lead', id: 1, name: 'lead', email: '<EMAIL>'},
        to: [{entity: 'lead', id: 1, name: 'lead', email: '<EMAIL>'}],
        cc: [],
        bcc: []
      }
      allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
        build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1, tenant_id: @user.tenant_id, name: 'lead 1', email: '<EMAIL>'),
      ])
    end

    context 'When given email is NOT present' do
      it 'should throw email not found error' do
        @params[:id] = 'random-id'
        expect{ForwardEmail.call(@params)}.to raise_error(ActiveRecord::RecordNotFound).with_message('01602001')
      end
    end

    context 'When given email is present' do
      it 'should forward email with same thread-id' do
        forwarded_email = ForwardEmail.call(@params).result
        expect(forwarded_email.email_thread_id).to eq(@email.email_thread_id)
      end
    end
  end

  context 'When sender is NOT owner of current email_thread' do
    before do
      another_user = create(:user, id: 33, tenant_id: 14)
      auth_data = build(:auth_data, user_id: another_user.id, tenant_id: another_user.tenant_id, username: another_user.name)
      @email.related_to << build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1000, tenant_id: @user.tenant_id, name: 'lead 1', email: '<EMAIL>', owner_id: @user.id)
      account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: another_user)
      thread = Thread.current
      thread[:auth] = auth_data
      @params = {
        id: @email.id,
        subject: 'FW: test',
        body: 'some text',
        relatedTo: {entity: 'lead', id: 1, name: 'lead', email: '<EMAIL>'},
        to: [{entity: 'lead', id: 1, name: 'lead', email: '<EMAIL>'}],
        cc: [],
        bcc: []
      }

      allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
        build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1, tenant_id: @user.tenant_id, name: 'lead 1', email: '<EMAIL>'),
      ])
      setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', account.email).and_return(setting)
      allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => { 1000 => {} } })
    end

    it 'should create new email thread' do
      forwarded_email = ForwardEmail.call(@params).result
      expect(forwarded_email.email_thread_id).to_not eq(@email.email_thread_id)
    end
  end

  context 'when user does not have permission to forward email' do
    before do
      another_user = create(:user, id: 33, tenant_id: 14)
      auth_data = build(:auth_data, user_id: another_user.id, tenant_id: another_user.tenant_id, username: another_user.name)
      create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: another_user)
      thread = Thread.current
      thread[:auth] = auth_data
      @params = {
        id: @email.id,
        subject: 'FW: test',
        body: 'some text',
        relatedTo: {entity: 'lead', id: 1, name: 'lead', email: '<EMAIL>'},
        to: [{entity: 'lead', id: 1, name: 'lead', email: '<EMAIL>'}],
        cc: [],
        bcc: []
      }

      allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
        build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1, tenant_id: @user.tenant_id, name: 'lead 1', email: '<EMAIL>'),
      ])
      allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => {} })
    end

    it 'raised & logs error' do
      expect(Rails.logger).to receive(:error).with("Unauthorised: User cannot forward email. User id 33 Email id #{@email.id}")
      expect do
        ForwardEmail.call(@params).result
      end.to raise_error(ExceptionHandler::AuthenticationError, '01601005')
    end
  end
end
