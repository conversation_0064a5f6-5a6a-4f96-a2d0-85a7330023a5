require 'google/apis/gmail_v1'
require 'bunny-mock'
require 'rails_helper'

RSpec.describe CreateConnectedAccount do
  describe '#call' do
    before do
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:watch_user).and_return(true)
      expect(RegisterGmailWebhook).to receive(:call).and_return true
      allow(FetchDisplayName).to receive(:call).and_return true
      @user = create(:user)
      connection = BunnyMock.new
      expires_at = (Time.now + 1.hour).to_s
      @data = {
        "provider" => GOOGLE_PROVIDER,
        "accessToken" => "AAAA",
        "expiresAt" => expires_at,
        "scopes" => ['https://www.googleapis.com/auth/gmail.send', 'https://www.googleapis.com/auth/gmail.readonly'],
        "email" => Faker::Internet.email
      }
    end

    context 'valid input' do
      context 'Existinng user' do
        before do
          CreateConnectedAccount.call(@user.id, @user.tenant_id, @user.name, @data)
        end

        context '#connected_account' do
          it 'adds new connected account' do
            expect(ConnectedAccount.count).to eq 1
          end

          it 'checks for data' do
            acct = ConnectedAccount.last
            expect(acct.tenant_id).to eq @user.tenant_id
            expect(acct.expires_at).not_to be nil
            expect(acct.access_token).to eq 'AAAA'
            expect(acct.scopes).to eq ["https://www.googleapis.com/auth/gmail.send", "https://www.googleapis.com/auth/gmail.readonly"]
          end

          it 'registers for webhook' do
            expect(RegisterGmailWebhook).to receive(:call).with(instance_of(ConnectedAccount)).exactly(1).times
            CreateConnectedAccount.call(@user.id, @user.tenant_id, @user.name, @data)
          end
        end
      end

      context 'New user' do
        before(:each) do
          User.destroy_all
          CreateConnectedAccount.call(@user.id, @user.tenant_id, @user.name, @data)
        end

        it 'adds new user' do
          expect(User.count).to eq 1
        end

        it 'verify new user"s data' do
          user = User.last
          expect(user.name).to eq @user.name
          expect(user.id).to eq @user.id
          expect(user.tenant_id).to eq @user.tenant_id
        end

        it 'registers for webhook' do
          expect(RegisterGmailWebhook).to receive(:call).with(instance_of(ConnectedAccount)).exactly(1).times
          CreateConnectedAccount.call(@user.id, @user.tenant_id, @user.name, @data)
        end
      end
    end
  end
end

