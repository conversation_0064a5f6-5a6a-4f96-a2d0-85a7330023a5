require 'rails_helper'

RSpec.describe GetUser do
  describe "#call" do
    let(:url) { SERVICE_IAM + "/v1/users/search?sort=updatedAt,desc&page=0&size=100" }
    let(:headers) { { Authorization: "Bearer #{ @token }" } }
    let(:user_ids) { '4167,6781' }

    before do
      @token = build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      it "returns users in output" do
        stub_request(:post, url).to_return(status: 200, body: file_fixture('user-response.json'), headers: {})

        result = described_class.call(user_ids).result

        expect(result.keys).to match_array(%w[last first totalPages size number numberOfElements totalElements sort content metaData])
        expect(result['content'].count).to be(2)
        expect(result['content'].map { |user| user['id'] }).to match_array(user_ids.split(',').map(&:to_i))
        expect(result['metaData'].present?).to be(true)
      end
    end

    context "with invalid input - " do
      context "when users are not available" do
        it "raises 404 not found error" do
          stub_request(:post, url).with(headers: headers)
            .to_return(status: 404, body: '', headers: {})

          expect { described_class.call(user_ids).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      context "when something went wrong while API processing" do
        it "raises 500 error" do
          stub_request(:post, url).with(headers: headers)
            .to_return(status: 500, body: '', headers: {})

          expect { described_class.call(user_ids).result }.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        end
      end

      context "when Api request invalid" do
        it "raises 400 error" do
          stub_request(:post, url).with(headers: headers)
            .to_return(status: 400, body: '', headers: {})

          expect { described_class.call(user_ids).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end
    end
  end
end
