require 'rails_helper'

RSpec.describe EntityLabels do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      thread = Thread.current
      thread[:token] = @token
      stub_entity_labels_api
    end

    context 'when entity labels are requested' do
      [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_CALL, LOOKUP_MEETING].each do |entity|
        it 'should return entity labels for all required entities' do
          result = described_class.call(entity).result

          expect(result.keys).to match_array(%w[displayName displayNamePlural])
        end
      end
    end
  end
end
