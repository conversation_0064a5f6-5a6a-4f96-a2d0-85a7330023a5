require 'rails_helper'
require 'bunny-mock'

RSpec.describe EmailMetadataPublisher do
  describe '#call' do
    before do
      BunnyMock.use_bunny_queue_pop_api = true
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic EMAIL_EXCHANGE

      @email = create(:email)
      @look_up = create(:look_up)

      @event = Event::EmailMetadata.new(another_user, user_look_up.entity_id, contact.entity_id.to_s, @email.id)

      @queue = @channel.queue "email.update.deal.metaInfo"
      @queue.bind @exchange, routing_key: "email.update.deal.metaInfo"
      allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)

      context " - Email related to Deal" do
        context "when email is created" do
          before { EmailRelatedToContactEventPublisher.call(@emaili, @look_up) }

          it 'raises an event in the Email Exchange & routes it to queue email.update.deal.metaInfo' do
            expect(@queue.message_count).to eq(1)
          end

          it 'publishes the correct payload' do
            payload = @queue.pop
            expect(payload.last).to eq(@event.to_json)
          end
        end
      end
    end
  end
end
