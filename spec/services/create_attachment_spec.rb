require 'rails_helper'

RSpec.describe CreateAttachment do
  describe '#call' do
    before do
      user = create(:user, id: 12, tenant: create(:tenant, id: 99))
      @attachment = File.new("#{Rails.root}/spec/fixtures/files/test_photo.jpg")
      @connected_account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i)
      auth_data =  build(:auth_data, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
      thread = Thread.current
      thread[:auth] = auth_data
      email_thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
      @email = create(:email, id: 22,owner: user, tenant_id: 99, connected_account: @connected_account, email_thread_id: email_thread.id)
      old_attachment = create(:attachment, id: 77, email: @email, file_name: 'tenant_99/user_11/22_old_file.jpg')
      @files = [
        {
          'data' => @attachment,
          'fileName' => 'test.jpg'
      }]
      @existing_files = [
        {
          'id' => 77,
          'fileName' => 'old_file.jpg',
          'oldAttachment' => old_attachment
        }
      ]
    end

    context 'Create Attachment' do
      it 'should associate attachment with email' do
        allow(UploadFileToS3).to receive(:call).with(@attachment.path, /tenant_99\/user_12\/[0-9]+_test_[^>]*.jpg/, S3_EMAIL_BUCKET).and_return(nil)
        CreateAttachment.call(@email, @files)
        expect(@email.attachments.count).to eq 2
      end

      context 'when attachment name is not present' do
        before do
          @files = [
            {
              :data => ActionDispatch::Http::UploadedFile.new({
                :filename => 'avatar.jpeg',
                :type => 'image/jpeg',
                :tempfile => @attachment
              })
          }]
        end

        it 'saves the filename correctly' do
          allow(UploadFileToS3).to receive(:call).with(@attachment.path, /tenant_99\/user_12\/[0-9]+_test_[^>]*.jpg/, S3_EMAIL_BUCKET).and_return(nil)
          CreateAttachment.call(@email, @files)
          expect(@email.attachments.count).to eq 2
          expect(@email.attachments.map(&:file_name).last).to match(%r{tenant_99/user_12/22_avatar_[^>]*})
        end
      end
    end

    context 'Copy Attachment' do
      it 'should associate attachment with email' do
        allow(File).to receive(:delete).with('old_file.jpg').and_return(true)
        allow(CopyAttachmentsOnS3).to receive(:call).with('tenant_99/user_11/22_old_file.jpg', %r{tenant_99/user_12/3_old_file_[^>]*}).and_return(nil)
        forwarded_email = create(:email, id: 3, tenant_id: 99, owner: @email.owner, connected_account: @connected_account, email_thread_id: @email.email_thread_id)
        CreateAttachment.call(forwarded_email, @existing_files, @email.id)
        expect(@email.attachments.count).to eq 1
        expect(forwarded_email.attachments.count).to eq 1
        expect(forwarded_email.attachments.first.file_name).to match(%r{tenant_99/user_12/3_old_file_[^>]*})
      end
    end
  end
end
