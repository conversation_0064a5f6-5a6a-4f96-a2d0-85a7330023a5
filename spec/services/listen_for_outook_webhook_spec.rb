require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForOutlookWebhook do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic EMAIL_EXCHANGE
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(EMAIL_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(EMAIL_EXCHANGE, OUTLOOK_WEBHOOK_EVENT, OUTLOOK_WEBHOOK_QUEUE)
          .and_yield(payload.to_s)
        allow(FetchOutlookMessages).to receive(:call).and_return(nil)
      end

      context 'outlook webhook event' do
        let(:routing_key) { OUTLOOK_WEBHOOK_EVENT }
        let(:payload)     { {'params': [{ "subscriptionId": "123", "resourceData": { "id": "xyz" } }]}.to_json }

        it 'call the fetch message service correctly' do
          expect(FetchOutlookMessages).to receive(:call).with(JSON.parse(payload)['params'])
          ListenForOutlookWebhook.call
        end
      end
    end
  end
end
