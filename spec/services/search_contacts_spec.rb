require 'rails_helper'

RSpec.describe SearchContacts do
  describe "#call" do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      it "returns correct output" do
        data = {"content":[{"emails":[{"type":"OFFICE","value":"<EMAIL>","primary":true}],
                            "lastName":"test","firstName":"Contact1","id":1, recordActions: { email: true }, ownerId: 123 },
        {"emails":[{"type":"OFFICE","value":"<EMAIL>","primary":true}],
         "lastName":"test","firstName":"Contact2 test","id":2, recordActions: { email: true } },
        {"emails":[{"type":"OFFICE","value":"<EMAIL>","primary":true}],
         "lastName":"test","firstName":"Contact3 test","id":3, recordActions: { email: true } }
        ]
        }

        emails = ['<EMAIL>', '<EMAIL>']

        rules = [
          {
            "operator": "in",
            "id": "emails",
            "field": "emails",
            "type": "string",
            "value": emails.join(',')
          }
        ]

        payload = { fields: ["id", "firstName", "lastName", "emails", "associatedDeals", "ownerId"], jsonRule: { rules: rules, "condition": "OR", "valid": true } }

        stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?sort=updatedAt,desc&page=0&size=100").
          with(
            body: payload.to_json,
            headers: {
              "Authorization" => "Bearer #{ @token }"
            }).
            to_return(status: 200, body: data.to_json, headers: {})

            command = SearchContacts.call(emails, 1)

            expect(command.result[:matched]).to eq([{:entity=>"contact_1", :email=>"<EMAIL>", :name=>"Contact1 test", :tenant_id=>1, :owner_id => 123, :associated_deals => []}])
            expect(command.result[:unmatched]).to eq([{:entity=>"customemail", :email=>"<EMAIL>", :tenant_id=>1}])
      end
    end

    # TODO: Need to add more test coverage
    context "with invalid input - " do
      context "when lead is not available" do
      end

      context "when something went wrong while API processing" do
      end

      context "when Api request invalid" do
      end
    end
  end
end
