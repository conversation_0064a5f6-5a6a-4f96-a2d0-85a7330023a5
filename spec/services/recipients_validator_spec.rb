require 'rails_helper'

RSpec.describe RecipientsValidator do
  describe "#call" do
    before do
      @user = create(:user)
      @params = { user: @user }
      recipients = []
      recipients << {id: 10, entity: LOOKUP_USER, name: "<PERSON>"}
      recipients << {id: 11, entity: LOOKUP_LEAD, name: "<PERSON>"}
      recipients << {id: 12, entity: LOOKUP_DEAL, name: "<PERSON> Deal"}
      recipients << {id: 13, entity: LOOKUP_CONTACT, name: "John Contact"}
      recipients << {email: "<EMAIL>", entity: 'email'}
      @params[:recipients] = recipients

      allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
        build(:look_up, entity_type: LOOKUP_USER, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name),
        build(:look_up, entity_type: LOOKUP_USER, entity_id: 10, tenant_id: @user.tenant_id, name: '<PERSON>')
      ])

      allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
        build(:look_up, entity_type: LO<PERSON>UP_LEAD, entity_id: 11, tenant_id: @user.tenant_id, name: "<PERSON>")
      ])

      allow(ValidateDeals).to receive_message_chain(:call, :result).and_return([
        build(:look_up, entity_type: LOOKUP_DEAL, entity_id: 12, tenant_id: @user.tenant_id, name: "John Deal")
      ])

      allow(ValidateContacts).to receive_message_chain(:call, :result).and_return([
        build(:look_up, entity_type: LOOKUP_CONTACT, entity_id: 12, tenant_id: @user.tenant_id, name: "John Contact")
      ])
    end

    context 'with uniq recipients' do

      it 'should return validated recipients' do
        response = RecipientsValidator.call(@params).result
        expect(response.size).to eq(6)
      end

      it 'should always add current user in recipients' do
        response = RecipientsValidator.call(@params).result
        expect(response.size).to eq(6)
        expect(response.first.entity_id).to eq(@user.id)
        expect(response.first.name).to eq(@user.name)
      end
    end

    context "with duplicate recipients" do
      it 'should remove duplicate' do
        @params[:recipients] << {id: 10, entity: LOOKUP_USER, name: "John Doe"}
        response = RecipientsValidator.call(@params).result
        expect(response.size).to eq(6)
        expect(response.select{|resp| resp.entity_id == 10}.count).to eq(1)
      end
    end
  end
end
