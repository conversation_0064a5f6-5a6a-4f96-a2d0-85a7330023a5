# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetCallRecordingUrl do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    def stub_get_call_recording_request(status: 200, body: file_fixture('call-recording-url.json'), call_log_id: 'call-log-id', call_recording_id: 'call-recording-id')
      stub_request(:get, "#{SERVICE_CALL}/v1/call-logs/#{call_log_id}/call-recordings/#{call_recording_id}?longLivedUrl=true").
      with(
        headers: {
          'Authorization' => "Bearer #{@token}"
          }).
        to_return(status: status, body: body, headers: {}
      )
    end

    context 'with valid input' do
      it 'returns call log details' do
        stub_get_call_recording_request
        result = GetCallRecordingUrl.call('call-log-id', 'call-recording-id').result
        expect(result['url']).to be_present
      end
    end

    context 'with invalid input' do
      context 'when data not found' do
        it 'raises invalid data error and logs proper message' do
          allow(Rails.logger).to receive(:error).with(/Get CallRecordingUrl - 404/)
          stub_get_call_recording_request(status: 404)
          expect { GetCallRecordingUrl.call('call-log-id', 'call-recording-id') }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      context 'when internal server error' do
        it 'raises error and logs proper message' do
          allow(Rails.logger).to receive(:error).with(/Get CallRecordingUrl - 500/)
          stub_get_call_recording_request(status: 500)
          expect { GetCallRecordingUrl.call('call-log-id', 'call-recording-id') }.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        end
      end
    end
  end
end
