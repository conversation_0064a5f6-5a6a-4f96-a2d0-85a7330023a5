require 'rails_helper'

RSpec.describe RecipientsBuilder do
  describe "#call" do
    before do
      @user = create(:user)
      @params = { user: @user }
      recipients = []
      recipients << {id: 10, entity: LOOKUP_USER, name: "<PERSON>"}
      recipients << {id: 11, entity: LOOKUP_LEAD, name: "<PERSON>"}
      recipients << {id: 12, entity: LOOKUP_DEAL, name: "John Deal"}
      recipients << {id: 13, entity: LOOKUP_CONTACT, name: "John Contact"}
      recipients << {email: "<EMAIL>", entity: 'email'}
      @params[:recipients] = recipients

      @token = FactoryBot.build(:auth_token, user_id: @user.id, tenant_id: @user.tenant_id).token
      Thread.current[:auth] = ParseToken.call(@token).result
      Thread.current[:token] = @token
    end

    before do
      stub_request(:get, SERVICE_SALES + "/v1/leads/11").
        with(
          headers: {
            Authorization: "Bearer #{@token}"
          }).
        to_return(status: 200, body: file_fixture('lead-data.json'))

      stub_request(:get, SERVICE_SALES + "/v1/contacts/13").
        with(
          headers: {
            Authorization: "Bearer #{@token}"
          }).
        to_return(status: 200, body: file_fixture('contact-data.json'))

      stub_request(:get, SERVICE_DEAL + "/v1/deals/12").
        with(
          headers: {
            Authorization: "Bearer #{@token}"
          }).
        to_return(status: 200, body: file_fixture('deal-data.json'))
    end

    context 'with uniq recipients' do
      it 'should return recipients' do
        response = described_class.call(@params).result
        expect(response.size).to eq 5
      end

      it 'should return lead lookup with owner id' do
        response = described_class.call(@params).result.find { |lookup| lookup.entity_type == LOOKUP_LEAD }

        expect(response.owner_id).to eq(3397)
      end

      it 'should return contact lookup with owner id' do
        response = described_class.call(@params).result.find { |lookup| lookup.entity_type == LOOKUP_CONTACT }

        expect(response.owner_id).to eq(11)
      end

      it 'should return deal lookup with owner id' do
        response = described_class.call(@params).result.find { |lookup| lookup.entity_type == LOOKUP_DEAL }

        expect(response.owner_id).to eq(11)
      end
    end

    context "with duplicate recipients" do
      it 'should remove duplicate' do
        @params[:recipients] << {id: 5, entity: LOOKUP_USER, name: "John Doe"}
        response = RecipientsBuilder.call(@params).result
        expect(response.size).to eq 6
        expect(response.select{|resp| resp.entity_id == 10}.count).to eq(1)
      end
    end
  end
end
