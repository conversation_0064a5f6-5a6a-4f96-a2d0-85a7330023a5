require 'rails_helper'
require 'bunny-mock'

RSpec.describe TenantEmailTemplateUsagePublisher do
  describe '#publish' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic EMAIL_EXCHANGE
      @queue = @channel.queue 'tenant.usage.collected'
      @queue.bind @exchange, routing_key: 'tenant.usage.collected'
      allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)
    end

    context 'When tenant email template usage publisher is called' do
      before do
        create(:email_template, tenant_id: 15)
        template_2 = create(:email_template, tenant_id: 15)
        create_list(:template_attachment, 3, email_template: template_2, file_size: 20)
        create_list(:template_attachment, 1, email_template: template_2, file_size: 30)
        email_2 = create(:email, tenant: create(:tenant, id: 15))
        create_list(:attachment, 5, email: email_2, file_size: 10)

        TenantEmailTemplateUsagePublisher.publish(15)
      end

      it 'publishes the correct payload' do
        # Note: Here first messages will be due to after_create callback so we need to pick next messages
        payload = @queue.all[2]
        expect(JSON(payload[:message])['usageRecords']).to match_array([
          { 'count' => 2, 'tenantId' => 15, 'usageEntity' => 'EMAIL_TEMPLATE' }
          #{ 'count' => 140, 'tenantId' => 15, 'usageEntity' => 'STORAGE_EMAIL_ATTACHMENT' }
          ])
      end
    end
  end
end
