require 'rails_helper'

RSpec.describe CreateEmailTemplate do
  describe '#call' do
    before do
      stub_entity_labels_api
      allow(TenantEmailTemplateUsagePublisher).to receive(:publish).with(instance_of(Integer))
      @user = create(:user, id: 12, tenant: create(:tenant, id: 99))
      auth_data =  build(:auth_data, :email_template_with_write_access, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

      thread = Thread.current
      thread[:auth] = auth_data

      @token = FactoryBot.build(:auth_token, user_id: 12, tenant_id: 99).token
      thread[:token] = @token
    end

    [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |category|
      context "when tried to create email template for #{category}" do
        context 'Success' do
          before do
            label = EntityLabels.call(category).result[:displayName]
            @params = {
              name: 'test template',
              category: category,
              subject: "test {{Created By - First Name}, {if missing: User}} and special {{Tenant - First / Name}, {if missing: <free text>}} {{Tenant - Company + Name}, {if missing: <free text>}}",
              body: "some {{#{label} - First Name}} text {{#{label} - Last Name}} tenant {{Tenant - Account Name}, {if missing: <free text>}} and special {{Tenant - First / Name}}, {{Tenant - Company + Name}}"
            }
            @attachment = File.new("#{Rails.root}/spec/fixtures/files/test_photo.jpg")
            allow(UploadFileToS3).to receive(:call).with(@attachment.path, /tenant_99\/user_12\/email_templates\/[0-9]+_test_[^>]*.jpg/, S3_EMAIL_TEMPLATE_BUCKET).and_return(nil)
            allow(V2::GetVariables).to receive_message_chain(:call, :result)
                               .and_return(JSON([
                                 {"id":1,"displayName":"#{label} - First Name","internalName":"firstName","standard":true, "entity": category},
                                 {"id":2,"displayName":"#{label} - Last Name","internalName":"lastName","standard":true, "entity": category},
                                 {"id": 1, "displayName": "Tenant - Account Name", "internalName": "accountName", "standard": true, "entity": 'tenant'},
                                 {"id": 4, "displayName": "Created By - First Name", "internalName": "firstName", "standard": true, "entity": 'createdBy'},
                                 {"id": 5, "displayName": "Tenant - First / Name", "internalName": "firstSlashName", "standard": true, "entity": 'tenant'},
                                 {"id": 5, "displayName": "Tenant - Company + Name", "internalName": "companyPlusName", "standard": true, "entity": 'tenant'}
                               ].to_json))

            @res = CreateEmailTemplate.call(@params.dup).result
          end

          it 'creates email template in db' do
            expect(EmailTemplate.count).to eq 1
          end

          it 'stores correct data for email template' do
            expect(@res.subject).to eq('test {{createdBy.firstName}, {if missing: User}} and special {{tenant.firstSlashName}, {if missing: <free text>}} {{tenant.companyPlusName}, {if missing: <free text>}}')
            expect(@res.body).to eq("some {{#{category}.firstName}} text {{#{category}.lastName}} tenant {{tenant.accountName}, {if missing: <free text>}} and special {{tenant.firstSlashName}}, {{tenant.companyPlusName}}")
            expect(@res.name).to eq(@params[:name])
            expect(@res.category).to eq(@params[:category])
          end

          context "when create permissions are not present on email template for user" do
            before do
              auth_data =  build(:auth_data, :email_template_without_write_access, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

              thread = Thread.current
              thread[:auth] = auth_data
            end

            it "throws forbidden error" do
              expect { CreateEmailTemplate.call(@params) }.to raise_error(ExceptionHandler::CreateEmailTemplateNotAllowedError).with_message(ErrorCode.create_email_template_not_allowed)
            end
          end
        end

        context 'Failure - with invalid input' do
          before do
            @params = {
              name: nil,
              subject: "test",
              body: "some text",
              category: category
            }
            allow(V2::GetVariables).to receive_message_chain(:call, :result)
                               .and_return(JSON([{"id":1,"displayName":"First Name","internalName":"firstName","standard":true, "entity": category},{"id":2,"displayName":"Last Name","internalName":"lastName","standard":true, "entity": category}].to_json))
          end

          it 'throws error' do
            expect { CreateEmailTemplate.call(@params) }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
            expect(EmailTemplate.count).to eq 0
            expect(TemplateAttachment.count).to eq 0
          end
        end

        context 'Failure - with unauthorised access' do
          before do
            @params = {
              name: 'test template',
              subject: "test",
              body: "some text",
              category: category
            }
            thread = Thread.current
            thread[:auth] = nil
          end

          it 'throws error' do
            expect { CreateEmailTemplate.call(@params) }.to raise_error(ExceptionHandler::AuthenticationError).with_message(ErrorCode.unauthorized)
            expect(EmailTemplate.count).to eq 0
            expect(TemplateAttachment.count).to eq 0
          end
        end
      end
    end

    context 'when tried to create email template for call-log' do
      context 'Success' do
        before do
          @params = {
            name: 'test template for call-log',
            category: 'call_log',
            subject: 'test {{Logged By - First Name}}',
            body: "{{Call Log - ID}, {if missing: <free text>}} {{Call Log - Outcome}, {if missing: <free text>}} {{Call Log - Type}, {if missing: <free text>}} {{Call Log - Phone Number}, {if missing: <free text>}} {{Call Log - Duration}, {if missing: <free text>}} {{Call Log - Created By}, {if missing: <free text>}} {{Call Log - Updated By}, {if missing: <free text>}} {{Call Log - Logged By}, {if missing: <free text>}} {{Call Log - Logged At}, {if missing: <free text>}} {{Call Log - Updated At}, {if missing: <free text>}} {{Call Log - Device Id}, {if missing: <free text>}} {{Call Log - Associated Lead}, {if missing: <free text>}} {{Call Log - Associated Deal}, {if missing: <free text>}} {{Call Log - Associated Contact}, {if missing: <free text>}}"
          }
          @attachment = File.new("#{Rails.root}/spec/fixtures/files/test_photo.jpg")
          allow(UploadFileToS3).to receive(:call).with(@attachment.path, /tenant_99\/user_12\/email_templates\/[0-9]+_test_[^>]*.jpg/, S3_EMAIL_TEMPLATE_BUCKET).and_return(nil)
          allow(V2::GetVariables).to receive_message_chain(:call, :result)
                             .and_return(JSON([{"id"=>19927, "displayName"=>"Call Log - ID", "internalName"=>"id", "standard"=>true, "type"=>"NUMBER", "entity" => LOOKUP_CALL},
                              {"id"=>19928, "displayName"=>"Call Log - Outcome", "internalName"=>"outcome", "standard"=>true, "type"=>"ENTITY_PICKLIST", "entity" => LOOKUP_CALL},
                              {"id"=>19929, "displayName"=>"Call Log - Type", "internalName"=>"callType", "standard"=>true, "type"=>"ENTITY_PICKLIST", "entity" => LOOKUP_CALL},
                              {"id"=>19930, "displayName"=>"Call Log - Phone Number", "internalName"=>"phoneNumber", "standard"=>true, "type"=>"PHONE", "entity" => LOOKUP_CALL},
                              {"id"=>19932, "displayName"=>"Call Log - Duration", "internalName"=>"duration", "standard"=>true, "type"=>"NUMBER", "entity" => LOOKUP_CALL},
                              {"id"=>19935, "displayName"=>"Call Log - Created By", "internalName"=>"createdBy", "standard"=>true, "type"=>"LOOK_UP", "entity" => LOOKUP_CALL},
                              {"id"=>19936, "displayName"=>"Call Log - Updated By", "internalName"=>"updatedBy", "standard"=>true, "type"=>"LOOK_UP", "entity" => LOOKUP_CALL},
                              {"id"=>19937, "displayName"=>"Call Log - Logged By", "internalName"=>"owner", "standard"=>true, "type"=>"LOOK_UP", "entity" => LOOKUP_CALL},
                              {"id"=>19938, "displayName"=>"Call Log - Logged At", "internalName"=>"createdAt", "standard"=>true, "type"=>"DATETIME_PICKER", "entity" => LOOKUP_CALL},
                              {"id"=>19939, "displayName"=>"Call Log - Updated At", "internalName"=>"updatedAt", "standard"=>true, "type"=>"DATETIME_PICKER", "entity" => LOOKUP_CALL},
                              {"id"=>19940, "displayName"=>"Call Log - Device Id", "internalName"=>"deviceId", "standard"=>true, "type"=>"TEXT_FIELD", "entity" => LOOKUP_CALL},
                              {"id"=>19942, "displayName"=>"Call Log - Associated Lead", "internalName"=>"associatedLeads", "standard"=>true, "type"=>"LOOK_UP", "entity" => LOOKUP_CALL},
                              {"id"=>19943, "displayName"=>"Call Log - Associated Deal", "internalName"=>"associatedDeals", "standard"=>true, "type"=>"LOOK_UP", "entity" => LOOKUP_CALL},
                              {"id"=>19944, "displayName"=>"Call Log - Associated Contact", "internalName"=>"associatedContacts", "standard"=>true, "type"=>"LOOK_UP", "entity" => LOOKUP_CALL},
                              {"id"=>19945, "displayName"=>"Logged By - First Name", "internalName"=>"firstName", "standard"=>true, "type"=>"TEXT_FIELD", "entity" => 'owner'}
                            ].to_json))

          @res = CreateEmailTemplate.call(@params.dup).result
        end

        it 'creates new record for email template with category in db' do
          expect(EmailTemplate.count).to eq(1)
        end

        it 'stores correct parsed data' do
          expect(@res.subject).to eq('test {{owner.firstName}}')
          expect(@res.body).to eq("{{call_log.id}, {if missing: <free text>}} {{call_log.outcome}, {if missing: <free text>}} {{call_log.callType}, {if missing: <free text>}} {{call_log.phoneNumber}, {if missing: <free text>}} {{call_log.duration}, {if missing: <free text>}} {{call_log.createdBy}, {if missing: <free text>}} {{call_log.updatedBy}, {if missing: <free text>}} {{call_log.owner}, {if missing: <free text>}} {{call_log.createdAt}, {if missing: <free text>}} {{call_log.updatedAt}, {if missing: <free text>}} {{call_log.deviceId}, {if missing: <free text>}} {{call_log.associatedLeads}, {if missing: <free text>}} {{call_log.associatedDeals}, {if missing: <free text>}} {{call_log.associatedContacts}, {if missing: <free text>}}")
          expect(@res.name).to eq(@params[:name])
          expect(@res.category).to eq(@params[:category])
        end

        context 'when create permissions are not present on email template for user' do
          before do
            auth_data =  build(:auth_data, :email_template_without_write_access, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

            thread = Thread.current
            thread[:auth] = auth_data
          end

          it 'throws forbidden error' do
            expect { CreateEmailTemplate.call(@params) }.to raise_error(ExceptionHandler::CreateEmailTemplateNotAllowedError).with_message(ErrorCode.create_email_template_not_allowed)
          end
        end
      end

      context 'Failure - with invalid input' do
        before do
          @params = {
            name: nil,
            subject: 'test',
            body: 'some text',
            category: 'call_log'
          }
          allow(V2::GetVariables).to receive_message_chain(:call, :result)
                             .and_return(JSON([
                              { 'id'=>19930, 'displayName'=>'Phone Number', 'internalName'=>'phoneNumber', 'standard'=>true, 'type'=>'PHONE', "entity" => LOOKUP_CALL },
                              { 'id'=>19928, 'displayName'=>'Outcome', 'internalName'=>'outcome', 'standard'=>true, 'type'=>'ENTITY_PICKLIST', "entity" => LOOKUP_CALL}
                            ].to_json))
        end

        it 'throws error' do
          expect { CreateEmailTemplate.call(@params) }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
          expect(EmailTemplate.count).to eq 0
          expect(TemplateAttachment.count).to eq 0
        end
      end

      context 'Failure - with unauthorised access' do
        before do
          @params = {
            name: 'test template',
            subject: 'test',
            body: 'some text',
            category: 'call_log'
          }
          thread = Thread.current
          thread[:auth] = nil
        end

        it 'throws error' do
          expect { CreateEmailTemplate.call(@params) }.to raise_error(ExceptionHandler::AuthenticationError).with_message(ErrorCode.unauthorized)
          expect(EmailTemplate.count).to eq 0
          expect(TemplateAttachment.count).to eq 0
        end
      end
    end
  end
end
