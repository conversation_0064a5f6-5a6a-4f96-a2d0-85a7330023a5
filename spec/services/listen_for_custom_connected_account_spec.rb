require 'google/apis/gmail_v1'
require 'bunny-mock'
require 'rails_helper'

RSpec.describe ListenForCustomConnectedAccount do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic USER_EXCHANGE
      expires_at = (Time.now + 1.hour).to_s
      @payload = {
        "userId" => @user.id,
        "tenantId" => @user.tenant_id,
        "provider" => CUSTOM_PROVIDER,
        "firstName" => @user.name,
        "accountId" => '123',
        "scopes" => ['SMTP', 'IMAP'],
        "email" => Faker::Internet.email
      }.to_json
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: CUSTOM_CONNECTED_ACCOUNT_SYNC_EVENT
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(USER_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(USER_EXCHANGE, CUSTOM_CONNECTED_ACCOUNT_SYNC_EVENT, CUSTOM_CONNECTED_ACCOUNT_SYNC_QUEUE)
          .and_yield(@payload.to_s)
      end

      context 'Existinng user' do
        before do
          ListenForCustomConnectedAccount.call()
        end

        it 'adds new connected account' do
          user = User.find(@user.id)
          expect(user.connected_accounts.count).to eq 1
        end

        context '#connected_account' do
          it 'adds new connected account' do
            expect(ConnectedAccount.count).to eq 1
          end

          it 'checks for data' do
            acct = ConnectedAccount.last
            expect(acct.tenant_id).to eq @user.tenant_id
            expect(acct.expires_at).not_to be nil
            expect(acct.expires_at).to be > Time.current.to_i
            expect(acct.access_token).to eq '123'
            expect(acct.scopes).to eq ["SMTP", "IMAP"]
          end
        end
      end

      context 'New user' do
        before(:each) do
          User.destroy_all
          ListenForCustomConnectedAccount.call()
        end

        it 'adds new user' do
          expect(User.count).to eq 1
        end

        it 'verify new user"s data' do
          user = User.last
          expect(user.name).to eq @user.name
          expect(user.id).to eq @user.id
          expect(user.tenant_id).to eq @user.tenant_id
        end
      end
    end
  end
end

