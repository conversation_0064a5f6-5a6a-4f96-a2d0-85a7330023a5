# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetCompanyIdName do
  describe "#call" do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
      @company_id = 123
    end

    context "with valid input - " do
      it "returns company id & name in output" do
        stub_request(:get, SERVICE_SEARCH + "/v1/summaries/companies/idName?id=123").
        with(
          headers: {
          "Authorization" => "Bearer #{ @token }"
          }).
        to_return(status: 200, body: [{ "id": 123, "name": "Dr. <PERSON>" }].to_json, headers: {})

        result = GetCompanyIdName.call(123).result

        expect(result.first['id']).to eq(123)
        expect(result.first['name']).to eq('Dr. Stone')
      end
    end

    context "with invalid input - " do
      context "when company is not available" do
        it "raises 404 not found error" do
          stub_request(:get, SERVICE_SEARCH + "/v1/summaries/companies/idName?id=123").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 404, body: "", headers: {})

          expect { GetCompanyIdName.call(123).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      context "when something went wrong while API processing" do
        it "raises 500 error" do
          stub_request(:get, SERVICE_SEARCH + "/v1/summaries/companies/idName?id=123").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 500, body: "", headers: {})

          expect { GetCompanyIdName.call(123).result }.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        end
      end

      context "when Api request invalid" do
        it "raises 400 error" do
          stub_request(:get, SERVICE_SEARCH + "/v1/summaries/companies/idName?id=123").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 400, body: "", headers: {})

          expect { GetCompanyIdName.call(123).result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end
    end
  end
end
