require 'rails_helper'

RSpec.describe AssociateEmailThreadWithDeals do
  describe '#call' do
    before do
      @user = create(:user, id: 12, tenant: create(:tenant, id: 99))
      auth_data =  build(:auth_data, :email_template_with_write_access, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

      thread = Thread.current
      thread[:auth] = auth_data

      @token = FactoryBot.build(:auth_token, user_id: 12, tenant_id: 99).token
      thread[:token] = @token

      @email_thread  = FactoryBot.create(:email_thread, tenant_id: 99)
      @emails = FactoryBot.create_list(:email, 2, tenant_id: 99, email_thread_id: @email_thread.id)
    end

    context 'success' do

      let(:deal_search_response) { file_fixture('deal-search-response.json').read }

      before do
        @params = {
          deals: [
            {
              id: 9,
              name: "test"
            }
          ],
          id: @email_thread.id
        }

        stub_request(:post, "http://localhost:8083/v1/search/deal?page=0&size=1000&sort=updatedAt,desc").
          with(
            headers: {
              Authorization: "Bearer #{@token}"
            },
            body: {
              fields: %w[id name ownedBy ownerId],
              jsonRule:{
                condition: 'AND',
                rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: "9" }],
                valid: true
              }
            }
          ).
          to_return(status: 200, body: deal_search_response, headers: {})

          connection = BunnyMock.new
          channel = connection.start.channel
          exchange = channel.topic EMAIL_EXCHANGE

          queue = channel.queue "email.updated.v2"
          queue.bind exchange, routing_key: "email.updated.v2"
          allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(queue)

      end

      it 'associate deal with emails present in thread' do
        AssociateEmailThreadWithDeals.call(@params)
        expect(@emails.first.related_to.where(entity_type: LOOKUP_DEAL, entity_id: 9)).not_to be_blank
        expect(@emails.last.related_to.where(entity_type: LOOKUP_DEAL, entity_id: 9)).not_to be_blank
        expect(@emails.last.email_thread.deal_ids).not_to be_blank
      end

      it 'removes deal from emails if its not present in params' do
        email = @emails.first
        email.related_to << LookUp.create(tenant_id: @user.tenant_id, entity_type: 'deal', entity_id: '12345', name: 'test')
        expect(email.reload.related_to.collect(&:entity)).to include 'deal_12345'
        AssociateEmailThreadWithDeals.call(@params)
        expect(email.reload.related_to.collect(&:entity)).not_to include 'deal_12345'
        expect(email.related_to.where(entity_type: LOOKUP_DEAL, entity_id: 9)).not_to be_blank
      end
    end

    context 'failure' do
      it 'raise error when email thread doesnt exist' do
        expect do
          AssociateEmailThreadWithDeals.call({id: 111})
        end.to raise_error(ExceptionHandler::NotFound, '01602001')
      end
    end
  end
end
