require 'rails_helper'
require 'bunny-mock'

RSpec.describe EmailReceivedRelatedToEntityPublisher do
  describe '#call' do
    before do
      allow(EmailCreateEventPublisher).to receive(:call)

      @user = create(:user)
      @another_user = create(:user, tenant_id: @user.tenant_id)

      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic EMAIL_EXCHANGE
      @queue = @channel.queue EMAIL_RECEIVED_RELATED_TO_ENTITY_EVENT
      @queue.bind @exchange, routing_key: EMAIL_RECEIVED_RELATED_TO_ENTITY_EVENT
      allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)
    end

    context 'when EmailReceivedRelatedToEntityPublisher is called' do
      before do
        @thread = create(:email_thread, owner: @another_user, tenant_id: @user.tenant_id)
        @email = create(:email, owner: @another_user, tenant_id: @user.tenant_id, email_thread_id: @thread.id)
        @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: @user.tenant_id)
        @cc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: @user.tenant_id)
        @bcc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: @user.tenant_id)

        @email.to_recipients << @to_lookup
        @email.cc_recipients << @cc_lookup
        @email.bcc_recipients << @bcc_lookup

        @email.related_to << @to_lookup
        @email.related_to << @cc_lookup
        @email.related_to << @bcc_lookup

        data = EmailDetailsSerializer.call(@email, false, add_owner_id: true).result.except('body')
        @event_data = Event::EmailReceivedRelatedToEntity.new(data).to_json

        EmailReceivedRelatedToEntityPublisher.call(@email, false)
      end

      it 'publishes correct number of events' do
        email_received_queue_message_count = @queue.instance_variable_get(:@messages).select do |message|
          message.dig(:options, :routing_key) == 'email.received.relatedTo.entity'
        end.count
        expect(email_received_queue_message_count).to eq(1)
      end

      it 'publishes the correct payload' do
        email_received_queue_payload = @queue.instance_variable_get(:@messages).find do |message|
          message.dig(:options, :routing_key) == 'email.received.relatedTo.entity'
        end
        expect(email_received_queue_payload[:message]).to eq(@event_data)
      end
    end
  end
end
