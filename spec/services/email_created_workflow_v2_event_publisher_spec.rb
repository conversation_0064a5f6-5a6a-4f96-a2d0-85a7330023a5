# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmailCreatedWorkflowV2EventPublisher do
  
  describe '#call' do
    context 'when email is created and event is not raised already for given global_message_id' do
      let(:email){ create(:email) }

      before do
        email.update(global_message_id: Faker::Alphanumeric.unique.alphanumeric(number: 15) )
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailCreatedWorkflowV2)).exactly(1).times
        expect(Rails.logger).to receive(:info).with("Email Created Workflow V2 event publisher called tenantId: #{email.tenant_id} ownerId: #{email.owner_id} email: #{email.id}")
      end
      
      it 'publishes email created workflow v2 event' do
        described_class.call(email)
      end
      
      it 'creates a record in global_messages' do
        expect { described_class.call(email) }.to change { GlobalMessage.count }.by(1)
        expect(GlobalMessage.last.message_id).to eq email.global_message_id
      end
    end
  
    context 'when email is created and event is already raised for given global_message_id' do
      let(:email){ create(:email) }
      let(:existing_global_message_entry) { create(:global_message) }

      before do
        email.update(global_message_id: existing_global_message_entry.message_id )
        expect(PublishEvent). to receive(:call).with(instance_of(Event::EmailCreatedWorkflowV2)).exactly(0).times
        expect(Rails.logger).to receive(:info).with("Skipped email created workflow v2 event for tenantId: #{email.tenant_id} ownerId: #{email.owner_id} email: #{email.id}")
      end

      it 'should not raise event' do
        described_class.call(email)
      end
    end

    context 'when email is created and global_message_id is not available' do
      let(:email){ create(:email) }

      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailCreatedWorkflowV2)).exactly(1).times
        expect(Rails.logger).to receive(:info).with("Email Created Workflow V2 event publisher called tenantId: #{email.tenant_id} ownerId: #{email.owner_id} email: #{email.id}")
      end
      
      it 'publishes email created workflow v2 event' do
        described_class.call(email)
      end
      
      it 'does not create a record in global_messages' do
        expect { described_class.call(email) }.to change { GlobalMessage.count }.by(0)
      end
    end
  end
end
