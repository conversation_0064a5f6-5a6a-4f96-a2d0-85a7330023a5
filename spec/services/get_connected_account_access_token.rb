require 'google/apis/gmail_v1'
require 'rails_helper'

RSpec.describe GetConnectedAccountAccessToken do
  describe '#call' do

    context 'expired access token' do
      before do
        user = create(:user)
        @connected_account = create(:connected_account, expires_at: (Time.now - 1.hour).to_i)
      end

      context 'Success' do
        before do
          return_auth_object = Signet::OAuth2::Client.new(access_token: 'NEWTOK', expires_at: DateTime.now + 1.hour)
          Signet::OAuth2::Client.any_instance.stub(:fetch_access_token!).and_return(return_auth_object)
          @result = GetConnectedAccountAccessToken.call(@connected_account).result
        end

        it 'updates access token and return it' do
          expect(@result).to eq 'NEWTOK'
          expect(@connected_account.access_token).to eq 'NEWTOK'
        end
      end

      context 'failure' do
        it 'raises third party api auth error' do
          Signet::OAuth2::Client.any_instance.stub(:fetch_access_token!).and_raise(Signet::AuthorizationError.new("error"))
          expect{ GetConnectedAccountAccessToken.call(@connected_account) }.to raise_error(ExceptionHandler::ThirdPartyAPIAuthError)
        end

        it 'raises third party api error for remote server error' do
          Signet::OAuth2::Client.any_instance.stub(:fetch_access_token!).and_raise(Signet::RemoteServerError.new("error"))
          expect{ GetConnectedAccountAccessToken.call(@connected_account) }.to raise_error(ExceptionHandler::ThirdPartyAPIError)
        end

        it 'raises third party api error for unexpected status' do
          Signet::OAuth2::Client.any_instance.stub(:fetch_access_token!).and_raise(Signet::UnexpectedStatusError.new("error"))
          expect{ GetConnectedAccountAccessToken.call(@connected_account) }.to raise_error(ExceptionHandler::ThirdPartyAPIError)
        end
      end
    end

    context 'live access token' do
      before do
        user = create(:user)
        @connected_account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i)
      end

      it 'returns access token' do
        expect(GetConnectedAccountAccessToken.call(@connected_account).result).to eq @connected_account.access_token
      end
    end
  end
end
