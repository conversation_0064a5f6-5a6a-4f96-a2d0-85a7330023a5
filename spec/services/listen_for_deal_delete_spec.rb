require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForDealDelete do
  describe '#call' do
    before do
      @user = create(:user)
      @deal_lookup = create(:look_up, entity: 'deal_12')
      allow(RabbitmqConnection).to receive(:subscribe).with(
        DEAL_EXCHANGE,
        DEAL_DELETED_EVENT,
        DEAL_DELETED_QUEUE
      ).and_yield(payload.to_s)
    end

    context 'valid input' do
      context 'for deal deleted event' do
        let(:payload) do
          {
            'id' => @deal_lookup.entity_id,
            'tenantId' => @deal_lookup.tenant_id,
            'userId' => @user.id,
            'publishUsage' => true
          }.to_json
        end

        it 'should call relevant job with parameters' do
          expected_options = { id: @deal_lookup.entity_id, tenant_id: @deal_lookup.tenant_id, user_id: @user.id, publish_usage: true, entity_type: LOOKUP_DEAL }
          expect(UpdateEmailForEntityDeleteEventJob).to receive(:perform_later).with(expected_options)
          described_class.call
        end
      end
    end
  end
end
