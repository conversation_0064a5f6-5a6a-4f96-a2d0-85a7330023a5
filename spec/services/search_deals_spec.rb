# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SearchDeals do
  describe "#call" do
    let(:deal_search_response) { file_fixture('deal-search-response.json').read }

    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @deal = build(:look_up, entity_type: LOOKUP_DEAL, entity_id: 9, tenant_id: 10)
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
      stub_request(:post, "http://localhost:8083/v1/search/deal?page=0&size=1000&sort=updatedAt,desc").
        with(
          headers: {
            Authorization: "Bearer #{@token}"
          },
          body: {
            fields: %w[id name ownedBy ownerId],
            jsonRule:{
              condition: 'AND',
              rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: "#{@deal.entity_id}" }],
              valid: true
            }
          }
        ).
        to_return(status: 200, body: deal_search_response, headers: {})
    end

    context 'valid input' do
      it 'returns blank if no deals are passed' do
        command = described_class.call([], @deal.tenant_id)

        expect(command.success?).to be_truthy
        expect(command.result).to eq([])
      end

      context 'when deal id matches' do
        it 'returns array of lookup hashes' do
          command = described_class.call([@deal.entity_id], @deal.tenant_id)

          expect(command.success?).to be_truthy
          expect(command.result).to match_array([{:entity=>"deal_#{@deal.entity_id}", :name=>'Email Deal', :owner_id=>4167, :tenant_id=>@deal.tenant_id}])
        end
      end
    end
  end
end
