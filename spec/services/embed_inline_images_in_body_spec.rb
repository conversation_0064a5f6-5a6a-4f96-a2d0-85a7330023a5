require 'rails_helper'
require 'ostruct'
require 'bunny-mock'

RSpec.describe EmbedInlineImagesInBody do

  before do
    connection = BunnyMock.new
    channel = connection.start.channel
    exchange = channel.topic EMAIL_EXCHANGE

    queue = channel.queue "email.update.lead.metaInfo"
    queue.bind exchange, routing_key: "email.update.lead.metaInfo"

    queue = channel.queue "email.update.deal.metaInfo"
    queue.bind exchange, routing_key: "email.update.deal.metaInfo"
    allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(queue)
  end

  context 'Gmail: Embed Inline Images' do
    let!(:user)             { create(:user)}
    let!(:another_user)     { create(:user, tenant_id: user.tenant_id)}
    let(:number_of_emails)  { 2 }

    before do
      user_look_up = create(:look_up, entity_type: LOOKUP_USER, entity_id: another_user.id)
      thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
      @emails = create_list(:email, number_of_emails, owner: another_user, sender: user_look_up, tenant_id: user.tenant_id, email_thread_id: thread.id) do |email, index|
        email.subject = index == 0 ? 'Original Subject' : 'Re: Original Subject'
        email.created_at = (10 - index).days.ago
        email.read_by << user.id
        email.body = "<div dir=\"ltr\"><div>FOR TEST ONLY</div><div><br></div><img src=\"cid:ii_kkoutiy612\" height=\"500\" style=\"width: 40%\" alt=\"cssW1258.jpg\" ><br><img src=\"cid:ii_kkoutiy612\" alt=\"cssW125811(1).jpg\" ></div><br><div class=\"gmail_quote\"><div dir=\"ltr\" class=\"gmail_attr\">On Tue, Feb 2, 2021 at 4:53 PM Swati Jadhav &lt;<a href=\"mailto:<EMAIL>\"><EMAIL></a>&gt; wrote:<br></div><blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\"><div dir=\"ltr\">ooooooooooooooooooooooooooooooooooooooooo</div><br><div class=\"gmail_quote\"><div dir=\"ltr\" class=\"gmail_attr\">On Tue, Feb 2, 2021 at 3:19 PM Swati Jadhav &lt;<a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a>&gt; wrote:<br></div><blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\"><div dir=\"ltr\">tttttttttttttttttttttttttttttttttttttttttttttttt</div><br><div class=\"gmail_quote\"><div dir=\"ltr\" class=\"gmail_attr\">On Tue, Feb 2, 2021 at 3:05 PM Swati Jadhav &lt;<a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a>&gt; wrote:<br></div><blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\"><div dir=\"ltr\">jeuwyeiquwiqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq</div><br><div class=\"gmail_quote\"><div dir=\"ltr\" class=\"gmail_attr\">On Tue, Feb 2, 2021 at 3:05 PM &lt;<a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a>&gt; wrote:<br></div><blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\"><p>\xC2\xA0</p><p>\xC2\xA0</p><p>\xC2\xA0</p><p>\xC2\xA0</p><p>--</p><p>\xC2\xA0</p>\r\n</blockquote></div>\r\n</blockquote></div>\r\n</blockquote></div>\r\n</blockquote></div>\r\n"
        email.save!
        email.attachments.create(file_name: 'tenant_14/user_12/121_cssW1258_123123123.jpg', file_size: 100, inline: true, content_id: '100d1a50-7def-4f33-bfe3-2b926b25e362')
        email.attachments.create(file_name: 'tenant_14/user_12/121_cssW125811_123123123.jpg', file_size: 100, inline: true, content_id: 'ii_kkoutiy612')
      end
      @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
      @cc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)
      @emails.each { |e| e.related_to << @to_lookup }

      allow(GetPresignedUrlFromS3).to receive(:call).and_return(OpenStruct.new({ result: { file_name: 'old_file.jpg', url: 'https://www.aws.com/files/78682793829.jpg' }}))
    end

    context 'Emails exists' do
      it 'should replace image tag with proper inline image url in email body'do
        result = EmbedInlineImagesInBody.call(@emails).result

        expect(result.first.body.to_s.scan("src=\"https://www.aws.com/files/78682793829.jpg\"").size).to eq(2)
        expect(result.first.body.to_s.scan("src=\"https://www.aws.com/files/78682793829.jpg\" height=\"500\" style=\"width: 40%\"").size).to eq(1)
        expect(result.second.body.to_s.scan("src=\"https://www.aws.com/files/78682793829.jpg\"").size).to eq(2)
      end
    end

    context 'Emails does not exists' do
      it 'should return blank array'do
        result = EmbedInlineImagesInBody.call([]).result
        expect(result).to eq([])
      end
    end
  end

  context 'Outlook: Embed Inline Images' do
    let!(:user)             { create(:user)}
    let!(:another_user)     { create(:user, tenant_id: user.tenant_id)}
    let(:number_of_emails)  { 2 }

    before do
      user_look_up = create(:look_up, entity_type: LOOKUP_USER, entity_id: another_user.id)
      thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
      @emails = create_list(:email, number_of_emails, owner: another_user, sender: user_look_up, tenant_id: user.tenant_id, email_thread_id: thread.id) do |email, index|
        email.subject = index == 0 ? 'Original Subject' : 'Re: Original Subject'
        email.created_at = (10 - index).days.ago
        email.read_by << user.id
        email.body = "<div dir=\"ltr\"><div>FOR TEST ONLY</div><div><br></div><img size=\"64404\" contenttype=\"image/png\" unselectable=\"on\" tabindex=\"-1\" style=\"max-width: 100%; user-select: none;\" data-outlook-trace=\"F:1|T:1\" src=\"cid:100d1a50-7def-4f33-bfe3-2b926b25e362\"><br><img src=\"cid:ii_kkoutiy612\" alt=\"cssW125811.jpg\" ></div><br><div class=\"gmail_quote\"><div dir=\"ltr\" class=\"gmail_attr\">On Tue, Feb 2, 2021 at 4:53 PM Swati Jadhav &lt;<a href=\"mailto:<EMAIL>\"><EMAIL></a>&gt; wrote:<br></div><blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\"><div dir=\"ltr\">ooooooooooooooooooooooooooooooooooooooooo</div><br><div class=\"gmail_quote\"><div dir=\"ltr\" class=\"gmail_attr\">On Tue, Feb 2, 2021 at 3:19 PM Swati Jadhav &lt;<a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a>&gt; wrote:<br></div><blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\"><div dir=\"ltr\">tttttttttttttttttttttttttttttttttttttttttttttttt</div><br><div class=\"gmail_quote\"><div dir=\"ltr\" class=\"gmail_attr\">On Tue, Feb 2, 2021 at 3:05 PM Swati Jadhav &lt;<a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a>&gt; wrote:<br></div><blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\"><div dir=\"ltr\">jeuwyeiquwiqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq</div><br><div class=\"gmail_quote\"><div dir=\"ltr\" class=\"gmail_attr\">On Tue, Feb 2, 2021 at 3:05 PM &lt;<a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a>&gt; wrote:<br></div><blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\"><p>\xC2\xA0</p><p>\xC2\xA0</p><p>\xC2\xA0</p><p>\xC2\xA0</p><p>--</p><p>\xC2\xA0</p>\r\n</blockquote></div>\r\n</blockquote></div>\r\n</blockquote></div>\r\n</blockquote></div>\r\n"
        email.save!
        email.attachments.create(file_name: 'tenant_14/user_12/121_cssW1258_123123123.jpg', file_size: 100, inline: true, content_id: '100d1a50-7def-4f33-bfe3-2b926b25e362')
        email.attachments.create(file_name: 'tenant_14/user_12/121_cssW125811_123123123.jpg', file_size: 100, inline: true, content_id: 'ii_kkoutiy612')
      end
      @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
      @cc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)
      @emails.each { |e| e.related_to << @to_lookup }

      allow(GetPresignedUrlFromS3).to receive(:call).and_return(OpenStruct.new({ result: { file_name: 'old_file.jpg', url: 'https://www.aws.com/files/78682793829.jpg' }}))
    end

    context 'Emails exists' do
      it 'should replace image tag with proper inline image url in email body'do
        result = EmbedInlineImagesInBody.call(@emails).result

        expect(result.first.body.to_s.scan("src=\"https://www.aws.com/files/78682793829.jpg\"").size).to eq(2)
        expect(result.second.body.to_s.scan("src=\"https://www.aws.com/files/78682793829.jpg\"").size).to eq(2)
      end

      context 'when email body is blank' do
        before { @emails.each { |email| email.update(body: nil) } }

        it 'should return email with blank body' do
          result = EmbedInlineImagesInBody.call(@emails).result

          expect(result.first.body).to be_nil
          expect(result.second.body).to be_nil
        end
      end
    end

    context 'Emails does not exists' do
      it 'should return blank array'do
        result = EmbedInlineImagesInBody.call([]).result
        expect(result).to eq([])
      end
    end
  end
end
