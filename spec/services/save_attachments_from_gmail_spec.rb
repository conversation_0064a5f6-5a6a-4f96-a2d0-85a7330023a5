require 'rails_helper'
require 'ostruct'

RSpec.describe SaveAttachmentsFromGmail do

  context 'Given attachments' do
    before do
      @user = create(:user, id: 12, tenant: create(:tenant, id: 99) )
      @connected_account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: @user)
      auth_data =  build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      thread = Thread.current
      thread[:auth] = auth_data
      email_thread = create(:email_thread, owner: @user, tenant_id: @user.tenant_id)

      @email = create(:email, owner: @user, tenant: @user.tenant, connected_account: @connected_account, email_thread: email_thread)
      @attachments = [{id: 'iwueyri38947928eiwuweiwue89e273quwe8927', name: 'test.txt'}]
      @gmail = Google::Apis::GmailV1::GmailService.new
      @gmail.authorization = Signet::OAuth2::Client.new(access_token: 'NEWTOK', expires_at: DateTime.now + 1.hour)
      @file = File.new("#{Rails.root}/tmp/#{DateTime.now.to_i}_#{@email.id}_test.txt", 'w')
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message_attachment).and_return(OpenStruct.new({ data: @file }))

      allow(UploadFileToS3).to receive(:call).with(@file.path, /tenant_99\/user_12\/[0-9]+_test_[^>]*.txt/, S3_EMAIL_BUCKET).and_return(nil)
    end

    context 'Attachment or email or gmail not exist' do
      it 'should return nil if attachments is blank'do
        params = { email: @email, attachments: [], gmail: @gmail }
        result = SaveAttachmentsFromGmail.call(params).result
        expect(result).to eq(nil)
      end

      it 'should return nil if email is blank'do
        params = { email: nil, attachments: @attachments, gmail: @gmail }
        result = SaveAttachmentsFromGmail.call(params).result
        expect(result).to eq(nil)
      end

      it 'should return nil if gmail is blank'do
        params = { email: @email, attachments: @attachments, gmail: nil }
        result = SaveAttachmentsFromGmail.call(params).result
        expect(result).to eq(nil)
      end
    end

    context 'All params exist' do
      it 'should call create attachment service with required params'do
        params = { email: @email, attachments: @attachments, gmail: @gmail }
        expect(@email.attachments.count).to eq(0)

        result = SaveAttachmentsFromGmail.call(params).result
        expect(@email.attachments.count).to eq(1)
        expect(@email.attachments.first.inline).to eq(false)
      end

      it 'should call create attachment of type inline  service with required params'do
        @attachments = [{id: 'iwueyri38947928eiwuweiwue89e273quwe8927', name: 'test.txt', type: 'inline', content_id: 'iuwey_98uyeww'}]
        params = { email: @email, attachments: @attachments, gmail: @gmail }
        expect(@email.attachments.count).to eq(0)

        result = SaveAttachmentsFromGmail.call(params).result
        expect(@email.attachments.count).to eq(1)
        expect(@email.attachments.first.inline).to eq(true)
        expect(@email.attachments.first.content_id).to eq('iuwey_98uyeww')
      end
    end
  end
end
