require 'google/apis/gmail_v1'
require 'rails_helper'

RSpec.describe GetEmailTemplateDetails do
  describe '#call' do

    before do
      stub_entity_labels_api
      allow(TenantEmailTemplateUsagePublisher).to receive(:publish).with(instance_of(Integer))
      @user = create(:user, id: 12, tenant: create(:tenant, id: 99))
      auth_data =  build(:auth_data, :email_template_with_read_access, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

      thread = Thread.current
      thread[:auth] = auth_data

      @token = FactoryBot.build(:auth_token, :with_email_template_read_all_permission, user_id: 12, tenant_id: 99).token

      thread[:token] = @token

      stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?custom-only=false&entityType=lead&page=0&size=100&sort=createdAt,asc").
        with(
          headers: {
            'Authorization' => "Bearer #{ @token }"
          }).
        to_return(status: 200, body: file_fixture('lead-fields.json'), headers: {})
    end

    context 'Get details' do
      context 'supports cross entity format' do
        before do
          @email_template = create(:email_template, created_by: @user, tenant_id: 99, body: '{{lead.firstName}} in body {{unknownVariable}} user {{convertedBy.lastName}}', subject: '{{lead.salutation}} and {{lead.firstName}} in subject, missing text {{lead.firstName}, {if missing: <free text>}}')
          @result = GetEmailTemplateDetails.call(@email_template.id).result
        end

        it 'returns email template object' do
          expect(@result[:body]).to eq "{{My Lead - First Name1 }} in body {{unknownVariable}} user {{Converted By - Last Name}}"
          expect(@result[:email_template].id).to eq @email_template.id
          expect(@result[:subject]).to eq "{{My Lead - Salutation=1}} and {{My Lead - First Name1 }} in subject, missing text {{My Lead - First Name1 }, {if missing: <free text>}}"
        end
      end

      context 'supports old format' do
        before do
          @email_template = create(:email_template, created_by: @user, tenant_id: 99, body: '{{firstName}} in body {{unknownVariable}}', subject: '{{salutation}} and {{firstName}} in subject')
        end

        it 'returns email template object' do
          result = GetEmailTemplateDetails.call(@email_template.id).result

          expect(result[:body]).to eq("{{My Lead - First Name1 }} in body {{unknownVariable}}")
          expect(result[:email_template].id).to eq(@email_template.id)
          expect(result[:subject]).to eq("{{My Lead - Salutation=1}} and {{My Lead - First Name1 }} in subject")
        end

        context 'when variables are in old format but cross entity variables are also available' do
          before do
            @email_template.update!(body: '{{firstName}} in body {{accountName}}', subject: '{{salutation}} and {{firstName}} in subject')
          end

          it 'returns display name for lead entity only' do
            result = GetEmailTemplateDetails.call(@email_template.id).result

            expect(result[:body]).to eq("{{My Lead - First Name1 }} in body {{accountName}}")
            expect(result[:email_template].id).to eq(@email_template.id)
            expect(result[:subject]).to eq("{{My Lead - Salutation=1}} and {{My Lead - First Name1 }} in subject")
          end
        end
      end
    end
  end
end
