# frozen_string_literal: true

require 'rails_helper'

RSpec.describe BounceProcessor, type: :service do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:connected_account) { create(:connected_account, user: user, tenant_id: tenant.id, email: '<EMAIL>') }
  let(:gmail_bounce_data) do
    data = JSON.parse(File.read(Rails.root.join('spec/fixtures/files/gmail_delivery_status_email.json')))
    data = data.deep_transform_keys { |k| k.to_s.underscore.to_sym }

    RecursiveOpenStruct.new(data)
  end
  
  let(:original_email) do
    create(:email,
      global_message_id: '<<EMAIL>>',
      connected_account: connected_account,
      tenant_id: tenant.id,
      status: :sent,
      owner: user
    )
  end

  subject { BounceProcessor.call(gmail_bounce_data, connected_account) }

  before do
    original_email
    original_email.update!(direction: 'sent')
  end

  describe '#call' do
    context 'with valid bounce email' do
      it 'updates the original email with bounce information' do
        subject
        original_email.reload
        
        expect(original_email.bounce_type).to eq('hard')
        expect(original_email.failed_reason).to eq('550-5.1.1 The email account that you tried to reach does not exist. Please try')
        expect(original_email.status).to eq('failed')
      end
    end

    context 'when original email is not found' do
      before do
        original_email.update!(global_message_id: 'different-message-id')
      end

      it 'logs a warning' do
        expect(Rails.logger).to receive(:warn).with(/Could not find original email/)
        subject
      end
    end

    context 'with non-bounce email' do
      let(:regular_gmail_data) do
        gmail_bounce_data.deep_dup.tap do |data|
          data['payload']['headers'].find { |h| h['name'] == 'Subject' }['value'] = 'Regular email'
          data['payload']['headers'].find { |h| h['name'] == 'From' }['value'] = '<EMAIL>'
        end
      end

      subject { BounceProcessor.call(regular_gmail_data, connected_account) }

      it 'does not process as bounce' do
        subject
        original_email.reload

        expect(original_email.bounce_type).to be_nil
        expect(original_email.failed_reason).to be_nil
      end
    end
  end
end
