require 'rails_helper'

RSpec.describe DeleteEmailThread do
  describe '#call' do
    before do
      @user = create(:user, id: 12, tenant: create(:tenant, id: 99))
      @user_from_other_tenant = create(:user, id: 21, tenant: create(:tenant, id: 100))
      auth_data = build(:auth_data, permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

      thread = Thread.current
      thread[:auth] = auth_data

      @token = FactoryBot.build(:auth_token, user_id: 12, tenant_id: 99).token
      thread[:token] = @token
    end

    context 'when sufficient permissions are avaliable to delete thread' do
      let(:permission) { :email_with_delete_permission }

      before do
        @thread = create(:email_thread, owner: @user, tenant_id: @user.tenant_id)
        @email = create(:email, email_thread: @thread)
        @attachment = create(:attachment, email: @email)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailDeletedWorkflowV2))

        expect(PublishUsageJob).to receive(:perform_later).with(@user.tenant_id)
      end

      it 'soft deletes thread and child entities' do
        described_class.call(@thread.id)
        expect(@thread.reload.deleted).to be_truthy
        expect(@email.reload.deleted).to be_truthy
        expect(@attachment.reload.deleted).to be_truthy
      end

      it 'soft deletes thread and child entities when publish_usage is nil' do
        described_class.call(@thread.id, nil)
        expect(@thread.reload.deleted).to be_truthy
        expect(@email.reload.deleted).to be_truthy
        expect(@attachment.reload.deleted).to be_truthy
      end
    end

    context 'when publish usage is false' do
      let(:permission) { :email_with_delete_permission }

      before do
        @thread = create(:email_thread, owner: @user, tenant_id: @user.tenant_id)
        @email = create(:email, email_thread: @thread)
        @attachment = create(:attachment, email: @email)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailDeletedWorkflowV2))
        expect(PublishUsageJob).not_to receive(:perform_later).with(@user.tenant_id)
      end

      it 'soft deletes thread and child entities and does not publish usage'do
        described_class.call(@thread.id, false)
        expect(@thread.reload.deleted).to be_truthy
        expect(@email.reload.deleted).to be_truthy
        expect(@attachment.reload.deleted).to be_truthy
      end

      it 'soft deletes thread and child entities and does not publish usage'do
        described_class.call(@thread.id, 'false')
        expect(@thread.reload.deleted).to be_truthy
        expect(@email.reload.deleted).to be_truthy
        expect(@attachment.reload.deleted).to be_truthy
      end
    end

    context 'when email belongs to different tenant' do
      let(:permission) { :email_with_delete_permission }

      before{ @thread = create(:email_thread, owner: @user_from_other_tenant, tenant_id: @user_from_other_tenant.tenant_id)}

      it 'throws not found error' do
        expect{ described_class.call(@thread.id) }.to raise_error(ExceptionHandler::NotFound, '01602001')
      end
    end

    context 'when delete permission is not available' do
      let(:permission) { :email_without_delete_permission }

      before{ @thread = create(:email_thread, owner: @user, tenant_id: @user.tenant_id)}

      it 'throws delete not allowed error' do
        expect{ described_class.call(@thread.id) }.to raise_error(ExceptionHandler::DeleteNotAllowedError, '01603014')
      end
    end

    context 'when user is not conversation owner' do
      let(:permission) { :email_with_delete_permission }

      before{ @thread = create(:email_thread, owner: @user_from_other_tenant, tenant_id: @user.tenant_id)}

      it 'throws not found error' do
        expect{ described_class.call(@thread.id) }.to raise_error(ExceptionHandler::DeleteNotAllowedError, '01603014')
      end
    end
  end
end
