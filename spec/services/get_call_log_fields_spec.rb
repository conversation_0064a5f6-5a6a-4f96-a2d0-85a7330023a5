# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetFields::CallLog do
  describe "#call" do
    before do
      @entity_label = 'Call Log'
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    def stub_get_call_log_fields_request(status: 200, body: file_fixture('call-fields.json'))
      stub_request(:get, "#{SERVICE_CALL}/v1/call-logs/fields").
        with(
          headers: {
            'Authorization' => "Bearer #{@token}"
          }
        ).
        to_return(status: status, body: body, headers: {})
    end

    context 'with valid input' do
      before(:each) do
        stub_get_call_log_fields_request
      end
      it 'returns fields in output' do
        result = described_class.call.result
        expect(result.count).to eq(18)
        expect(result.first['displayName']).to eq('ID')
        expect(result.first['internalName']).to eq('id')
        expect(result.first['entity']).to eq('call_log')
      end
    end

    context 'when api call for call log fields fails' do
      it 'raises invalid data error' do
        allow(Rails.logger).to receive(:error).with(/Get CallLog Fields - 400/)
        stub_get_call_log_fields_request(status: 400)
        expect { described_class.call }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      end
    end

    context 'when something went wrong while processing API' do
      it 'raises 500 error' do
        allow(Rails.logger).to receive(:error).with(/Get CallLog Fields - 500/)
        stub_get_call_log_fields_request(status: 500)
        expect { described_class.call }.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
      end
    end
  end
end
