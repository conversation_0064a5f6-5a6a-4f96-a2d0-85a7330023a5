# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetTask do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    def stub_get_task_details_request(status: 200, body: file_fixture('task-details.json'), task_id: 'task-id')
      stub_request(:get, "#{SERVICE_PRODUCTIVITY}/v1/tasks/#{task_id}").
      with(
        headers: {
          'Authorization' => "Bearer #{@token}"
          }).
        to_return(status: status, body: body, headers: {}
      )
    end

    context 'with valid input' do
      it 'returns task details' do
        stub_get_task_details_request
        result = GetTask.call('task-id').result
        expect(result['id']).to be_present
      end
    end

    context 'with invalid input' do
      context 'when data not found' do
        it 'raises invalid data error and logs proper message' do
          allow(Rails.logger).to receive(:error).with(/Get Task - 404/)
          stub_get_task_details_request(status: 404)
          expect { GetTask.call('task-id') }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      context 'when internal server error' do
        it 'raises error and logs proper message' do
          allow(Rails.logger).to receive(:error).with(/Get Task - 500/)
          stub_get_task_details_request(status: 500)
          expect { GetTask.call('task-id') }.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        end
      end
    end
  end
end
