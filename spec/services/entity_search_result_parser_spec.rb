require 'rails_helper'

RSpec.describe EntitySearchResultParser do
  describe "#call" do
    context "with valid input - " do
      it "returns matched and unmatched data correctly in output" do
        emails = ['<EMAIL>', '<EMAIL>']
        data = {"content":[{"emails":[{"type":"OFFICE","value":"<EMAIL>",
                "primary":true}],"firstName":"lead1","lastName":"test","id":1},
               {"emails":[{"type":"OFFICE","value":"<EMAIL>","primary":true}],
               "firstName":"Lead 40","lastName":"test","id":2},
               {"emails":[{"type":"OFFICE","value":"<EMAIL>","primary":true}],
               "firstName":"Lead 33","lastName":"test","id":3},
               {"emails":[{"type":"OFFICE","value":"<EMAIL>","primary":true}],
               "firstName":"Test lead 32","lastName":"test","id":4}]}.to_json

        result = EntitySearchResultParser.call(emails, JSON(data), LOOKUP_LEAD, 1).result

        expect(result[:matched].first[:entity]).to eq('lead_1')
        expect(result[:matched].first[:email]).to eq('<EMAIL>')
        expect(result[:matched].first[:name]).to eq('lead1 test')
        expect(result[:matched].count).to eq(1)

        expect(result[:unmatched].first[:entity]).to eq('customemail')
        expect(result[:unmatched].first[:email]).to eq('<EMAIL>')
        expect(result[:unmatched].count).to eq(1)
      end
    end

    context "when search result data is empty" do
      it "returns matched and unmatched data correctly in output" do
        emails = ['<EMAIL>', '<EMAIL>']
        data = {"content": []}.to_json

        result = EntitySearchResultParser.call(emails, JSON(data), LOOKUP_LEAD, 1).result

        expect(result[:matched].count).to eq(0)

        expect(result[:unmatched].first[:entity]).to eq('customemail')
        expect(result[:unmatched].first[:email]).to eq('<EMAIL>')
        expect(result[:unmatched].last[:entity]).to eq('customemail')
        expect(result[:unmatched].last[:email]).to eq('<EMAIL>')
        expect(result[:unmatched].count).to eq(2)
      end
    end
  end
end
