# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForLeadReassign do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic LEAD_EXCHANGE
    end

    context 'valid input' do
      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::LookUpUpdated)).once
        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: LEAD_OWNER_UPDATED_EVENT
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(LEAD_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
      end

      context 'lead reassigned event' do
        let(:payload) { { "entityId": 123, "newOwnerId": 5, "oldOwnerId": 12, "tenantId": 10 }.to_json }

        before do
          allow(RabbitmqConnection).to receive(:subscribe)
          .with(LEAD_EXCHANGE, LEAD_OWNER_UPDATED_EVENT, LEAD_OWNER_UPDATED_QUEUE)
          .and_yield(payload.to_s)
          @lookup = create(:look_up, entity_type: LOOKUP_LEAD, entity_id: 123, owner_id: 12, tenant_id: 10)
        end

        it 'updates lookup with new owner id' do
          expect(Rails.logger).to receive(:info).with(/Received message/)
          expect(Rails.logger).to receive(:info).with(/UpdateOwnerForEntity LookUp updated/)
          described_class.call
          expect(@lookup.reload.owner_id).to eq(5)
        end
      end
    end
  end
end
