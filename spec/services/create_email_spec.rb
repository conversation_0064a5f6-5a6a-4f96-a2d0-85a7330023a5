require 'google/apis/gmail_v1'
require 'rails_helper'
require 'bunny-mock'

RSpec.describe CreateEmail do

  before do
    connection = BunnyMock.new
    channel = connection.start.channel
    exchange = channel.topic EMAIL_EXCHANGE

    queue = channel.queue "email.update.deal.metaInfo"
    queue.bind exchange, routing_key: "email.update.deal.metaInfo"
    allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(queue)
  end

  describe '#call' do
    before do

      # TODO: Remove this hardcoding when actual implementation of
      # authentication is in place
      @user = create(:user, id: 12, tenant: create(:tenant, id: 14, tracking_enabled: true ))
      @connected_account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: @user, tenant: @user.tenant)
      setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', @connected_account.email).and_return(setting)
      auth_data =  build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

      thread = Thread.current
      thread[:auth] = auth_data

      header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
      payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
      return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
      user_lookup = build(:look_up, entity_type: LOOKUP_USER, entity_id: @user.id, tenant_id: @user.tenant_id, name: @user.name)
      allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
        user_lookup
      ])
    end

    context 'Success' do
      before do
        @params = {
          subject: "test",
          body: "some text",
          "trackingEnabled": true,
          relatedTo: {entity: "lead", id: 1, name: "lead", email: "<EMAIL>"},
          to: [{entity: "lead", id: 1, name: "lead", email: "<EMAIL>"}],
          cc: [{entity: "contact", id: 3, name: "contact_cc", email: "<EMAIL>"}],
          bcc: [{entity: "contact", id: 4, name: "contact_bcc", email: "<EMAIL>"}],
        }
        allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
          build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1, tenant_id: @user.tenant_id, name: 'lead 1', email: "<EMAIL>"),
        ])
        allow(ValidateContacts).to receive_message_chain(:call, :result).and_return([
          build(:look_up, entity_type: LOOKUP_CONTACT, entity_id: 3, tenant_id: @user.tenant_id, name: 'contact 3', email: "<EMAIL>"),
          build(:look_up, entity_type: LOOKUP_CONTACT, entity_id: 4, tenant_id: @user.tenant_id, name: 'contact 4', email: "<EMAIL>")
        ])
      end

      context 'Send email to lead' do
        context 'Success' do
          before do
            @res = CreateEmail.call(@params.dup).result
          end

          it 'Creates email in db' do
            expect(Email.count).to eq 1
          end

          it 'sets status' do
            expect(Email.last.status).to eq 'sent'
          end

          it 'saves whether tracking is enabled or not on email' do
            expect(Email.last.tracking_enabled).to eq true
          end

          it 'Creates Associated records' do
            expect(@res.email_look_ups.count).to eq 3
            expect(@res.related_to.count).to eq 3
          end

          it 'creates "to" recipients successfully' do
            expect(@res.to_recipients.count).to eq (1)
            expect(@res.to_recipients.map(&:email)).to eq (['<EMAIL>'])
          end

          it 'creates "cc" recipients successfully' do
            expect(@res.cc_recipients.count).to eq (1)
            expect(@res.cc_recipients.map(&:email)).to eq (['<EMAIL>'])
          end

          it 'creates "bcc" recipients successfully' do
            expect(@res.bcc_recipients.count).to eq (1)
            expect(@res.bcc_recipients.map(&:email)).to eq (['<EMAIL>'])
          end

          it 'checks data - email' do
            expect(@res.subject).to eq @params[:subject]
            expect(@res.body).to include @params[:body]
            expect(@res.to).to eq @params[:to].collect{|datum| datum[:email]}
            expect(@res.cc).to eq @params[:cc].collect{|datum| datum[:email]}
            expect(@res.bcc).to eq @params[:bcc].collect{|datum| datum[:email]}
            expect(@res.source_id).to eq 'SOURCE_ID'
          end
        end

        it 'publishes event correctly for lead entity' do
          expect(EmailReceivedRelatedToEntityPublisher).to receive(:call).once
          expect(EmailCreatedWorkflowV2EventPublisher).to receive(:call).with(instance_of(Email), nil).once
          CreateEmail.call(@params.dup).result
        end

        it 'publishes event correctly for contact entity' do
          @params = {
            subject: "test",
            body: "some text",
            relatedTo: {entity: "contact", id: 1, name: "lead", email: "<EMAIL>"},
            to: [{entity: "contact", id: 1, name: "lead", email: "<EMAIL>"}]
          }

          expect(EmailReceivedRelatedToEntityPublisher).to receive(:call).once
          CreateEmail.call(@params.dup).result
        end

        context 'Success - Associated objects already exists' do

          before do
            @params = {
              subject: "test",
              body: "some text",
              relatedTo: {entity: "lead", id: 1, name: "lead", email: "<EMAIL>"},
              to: [{entity: "lead", id: 1, name: "lead", email: "<EMAIL>"}],
            }

            create(:look_up, entity: 'lead_1', email: '<EMAIL>', tenant_id: 14)
            @res = CreateEmail.call(@params.dup).result
          end

          it do
            expect(@res.related_to.count).to eq 1
          end
        end

        context 'Failure - with invalid input' do
          before do
            @params = {
              subject: "test",
              body: "some text",
              to: [{entity: "lead", id: 1, name: "lead", email: "leaddomain.com"},
                   {entity: "contact", id: 1, name: "contact", email: "<EMAIL>"}]
            }
            allow(ValidateLeads).to receive_message_chain(:call, :result).and_raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
          end

          it 'invalid email in to' do
            expect { CreateEmail.call(@params) }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
            expect(Email.count).to eq 0
            expect(LookUp.count).to eq 0
            expect(EmailLookUp.count).to eq 0
          end
        end

        context 'Failure - account not connected' do
          before do
            @params = {
              subject: "test",
              body: "some text",
              to: [{entity: "lead", id: 1, name: "lead", email: "leaddomain.com"},
                   {entity: "contact", id: 1, name: "contact", email: "<EMAIL>"}]
            }
          end

          it 'raises error' do
            ConnectedAccount.destroy_all
            expect { CreateEmail.call(@params) }.to raise_error(ExceptionHandler::NotConnectedError, ErrorCode.not_connected)
            expect(Email.count).to eq 0
            expect(LookUp.count).to eq 0
            expect(EmailLookUp.count).to eq 0
          end
        end
      end

      context 'send email to deal' do
        before do
          @params = {
            subject: "test",
            body: "some text",
            relatedTo: { entity: LOOKUP_DEAL, id: 10, name: "deal" },
            to: [{entity: "contact", id: 10, name: "contact_to", email: "<EMAIL>"}],
            cc: [{entity: "contact", id: 11, name: "contact_cc", email: "<EMAIL>"}],
            bcc: [{entity: "contact", id: 12, name: "contact_bcc", email: "<EMAIL>"}]
          }

          allow(ValidateDeals).to receive_message_chain(:call, :result).and_return([
            build(:look_up, entity_type: LOOKUP_DEAL, entity_id: 10, tenant_id: @user.tenant_id, name: 'Deal 1', email: ""),
          ])
          allow(ValidateContacts).to receive_message_chain(:call, :result).and_return([
            build(:look_up, entity_type: LOOKUP_CONTACT, entity_id: 10, tenant_id: @user.tenant_id, name: 'contact 10', email: "<EMAIL>"),
            build(:look_up, entity_type: LOOKUP_CONTACT, entity_id: 11, tenant_id: @user.tenant_id, name: 'contact 11', email: "<EMAIL>"),
            build(:look_up, entity_type: LOOKUP_CONTACT, entity_id: 12, tenant_id: @user.tenant_id, name: 'contact 12', email: "<EMAIL>")
          ])
        end

        context 'Success' do
          before { @res = CreateEmail.call(@params.dup).result }

          it 'Creates email in db' do
            expect(Email.count).to eq 1
          end

          it 'Creates Associated records' do
            expect(@res.email_look_ups.count).to eq 4
            expect(@res.related_to.count).to eq 4
          end

          it 'creates "to" recipients successfully' do
            expect(@res.to_recipients.count).to eq (1)
            expect(@res.to_recipients.map(&:email)).to eq (['<EMAIL>'])
          end

          it 'creates "cc" recipients successfully' do
            expect(@res.cc_recipients.count).to eq (1)
            expect(@res.cc_recipients.map(&:email)).to eq (['<EMAIL>'])
          end

          it 'creates "bcc" recipients successfully' do
            expect(@res.bcc_recipients.count).to eq (1)
            expect(@res.bcc_recipients.map(&:email)).to eq (['<EMAIL>'])
          end

          it 'checks data - email' do
            expect(@res.subject).to eq @params[:subject]
            expect(@res.body).to eq @params[:body]
            expect(@res.to).to eq @params[:to].collect{|datum| datum[:email]}
            expect(@res.cc).to eq @params[:cc].collect{|datum| datum[:email]}
            expect(@res.bcc).to eq @params[:bcc].collect{|datum| datum[:email]}
            expect(@res.source_id).to eq 'SOURCE_ID'
          end
        end
      end

      context 'send email with inline images' do
        before do
          @params = {
            subject: "test",
            body: 'some text<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJAAAACQCAYAAADnRuK4AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAG" style="width: 100%;"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJAAAACQCAYAAADnRuK4AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAG" style="width: 100%;">',
            relatedTo: { entity: "lead", id: 1, name: "lead", email: "<EMAIL>" },
            to: [{ entity: "lead", id: 1, name: "lead", email: "<EMAIL>" }],
            cc: [{ entity: "contact", id: 3, name: "contact_cc", email: "<EMAIL>" }],
            bcc: [{ entity: "contact", id: 4, name: "contact_bcc", email: "<EMAIL>" }],
            trackingEnabled: true
          }
          allow(UploadFileToS3).to receive(:call).with(any_args, /tenant_14\/user_12\/[^>]*.png/, S3_EMAIL_BUCKET).and_return(nil)
          @res = CreateEmail.call(@params.dup).result
        end

        it 'creates email in db' do
          expect(Email.count).to eq(1)
        end

        it 'creates attachments' do
          expect(@res.attachments.count).to eq(2)
          expect(@res.attachments.first.inline).to eq(true)
          expect(@res.attachments.second.inline).to eq(true)
        end

        it 'checks data - email' do
          expect(@res.subject).to eq @params[:subject]
          expect(@res.body).to match /some text<img src="cid:.*" style="width: 100%;">/
          expect(@res.body).to include "email_mappings"
          expect(@res.to).to eq @params[:to].collect { |datum| datum[:email] }
          expect(@res.cc).to eq @params[:cc].collect { |datum| datum[:email] }
          expect(@res.bcc).to eq @params[:bcc].collect { |datum| datum[:email] }
          expect(@res.source_id).to eq('SOURCE_ID')
        end

        it 'should not add count to external attachment count' do
          expect(Email.last.external_attachment_count).to eq(0)
        end
      end

      it 'should publish email created event with correct format' do
        @publish_email_created_event = false
        allow_any_instance_of(BunnyMock::Queue).to receive(:publish) do |request, arguments|
          if arguments.present?
            args = JSON.parse(arguments)
            if args.keys == ['entity', 'oldEntity', 'metadata'] && args['entity']['direction']
              @publish_email_created_event = true
            end
          end
        end

        CreateEmail.call(@params.dup).result
        expect(@publish_email_created_event).to eq(true)
      end
    end

    context 'Reply' do
      before do

        header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
        payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
        return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
        allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
        allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)

        thread = create(:email_thread, id:1, owner: @user, tenant_id: @user.tenant_id)
        lead_lookup = build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1, tenant_id: @user.tenant_id, name: @user.name)
        contact_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, entity_id: 3, tenant_id: @user.tenant_id, name: @user.name)
        allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([lead_lookup])
        allow(ValidateContacts).to receive_message_chain(:call, :result).and_return([contact_lookup])
        allow(CreateAttachment).to receive(:call).and_return(nil)
        create_list(:email, 2,owner: @user, tenant_id: @user.tenant_id, connected_account: @connected_account, email_thread_id: thread.id, from: @connected_account.email)
        @params = {
          thread_id: 1,
          subject: 'Re:test',
          body: 'some text',
          relatedTo: {entity: 'lead', id: 1, name: 'lead', email: '<EMAIL>'},
          to: [{entity: 'lead', id: 1, name: 'lead', email: '<EMAIL>'}],
          cc: [{entity: 'contact', id: 3, name: 'contact_cc', email: '<EMAIL>'}],
          bcc: [{entity: 'contact', id: 4, name: 'contact_bcc', email: '<EMAIL>'}],
          replyTo: thread.emails.first.id
        }
      end

      context 'when user context is present' do
        before do
          auth_data = build(:auth_data, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
          thread = Thread.current
          thread[:auth] = auth_data
        end
        context 'And user is not part of email thread' do
          before do
            new_thread = create(:email_thread, id: 88, owner: @user, tenant_id: @user.tenant_id)
            create_list(:email, 2, owner: @user, tenant_id: @user.tenant_id, connected_account: @connected_account, email_thread_id: new_thread.id)
            @params[:thread_id] = new_thread.id
          end
          it 'should throw InvalidDataError' do
            expect{ CreateEmail.call(@params) }.to raise_error(ExceptionHandler::InvalidDataError).with_message(ErrorCode.reply_not_allowed)
          end
        end

        context 'And user is part of email thread' do
          it 'should send email with given data to given recipients' do
            CreateEmail.call(@params)
            reply = Email.where(tenant_id: @user.tenant_id).order(created_at: :desc).first
            expect(reply.email_thread_id).to eq(1)
            expect(reply.subject).to eq('Re:test')
          end
        end
      end

      context 'when user context not present' do
        before do
          thread = Thread.current
          thread[:auth] = nil
        end
        it 'should throw AuthenticationError' do
          @params = {
            thread_id: 1,
            subject: 'test',
            body: 'some text',
            relatedTo: {entity: 'lead', id: 1, name: 'lead', email: '<EMAIL>'},
            to: [{entity: 'lead', id: 1, name: 'lead', email: '<EMAIL>'}],
            cc: [{entity: 'contact', id: 3, name: 'contact_cc', email: '<EMAIL>'}],
            bcc: [{entity: 'contact', id: 4, name: 'contact_bcc', email: '<EMAIL>'}],
          }
          expect{ CreateEmail.call(@params) }.to raise_error(ExceptionHandler::AuthenticationError).with_message(ErrorCode.unauthorized)
        end
      end
    end

    context 'failure' do
      context 'when creating inline attachments' do
        before do
          @params = {
            subject: "test",
            body: 'some text<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJAAAACQCAYAAADnRuK4AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAG" style="width: 100%;"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJAAAACQCAYAAADnRuK4AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAG" style="width: 100%;">',
            relatedTo: { entity: "lead", id: 1, name: "lead", email: "<EMAIL>" },
            to: [{ entity: "lead", id: 1, name: "lead", email: "<EMAIL>" }],
            cc: [],
            bcc: [],
          }
          allow(ValidateLeads).to receive_message_chain(:call, :result).and_return([
            build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1, tenant_id: @user.tenant_id, name: 'lead 1', email: "<EMAIL>"),
          ])
          # This stub creates a deadlock as Rails logger also tries writing to a file
          # allow_any_instance_of(File).to receive(:write).and_raise(StandardError)

          allow_any_instance_of(File).to receive(:define_singleton_method).and_raise(StandardError)
        end

        it 'raises error' do
          expect { CreateEmail.call(@params) }.to raise_error(ExceptionHandler::InternalServerError, '01602003')
        end
      end
    end
  end
end
