require 'rails_helper'

RSpec.describe RenewOutlookWebhook do
  describe '#call' do
    let(:subscription_id) { 'xyz' }

    before do
      @user = create(:user)
      @expires_at = (Time.now + 1.days).to_i
      @new_expires_at = (Time.now + 2.days).utc.strftime('%Y-%m-%dT%H:%M:%S.%LZ')
      @acct = create(:connected_account, provider_name: MICROSOFT_PROVIDER, expires_at: @expires_at, subscription_id: subscription_id, subscription_expiry: @expires_at, user: @user)
      allow(GetConnectedAccountAccessToken).to receive_message_chain([:call, :success?]).and_return(true)
      allow(GetConnectedAccountAccessToken).to receive_message_chain([:call, :result]).and_return('123')
      stub_request(:patch, "https://graph.microsoft.com/v1.0/subscriptions/#{subscription_id}").
        to_return(status: 200, body: { 'id' => subscription_id, 'expirationDateTime': @new_expires_at }.to_json, headers: {})
    end

    context 'when subscription renew is successful' do
      it 'stores subscription id and expiry in the database' do
        acct = RenewOutlookWebhook.call(@acct).result
        expect(@acct.subscription_expiry).to eq(Time.parse(@new_expires_at).to_i)
        expect(acct.subscription_id).to eq(@acct.subscription_id)
      end
    end

    context 'when subscription renew is failed' do
      context 'invalid outlook token' do
        before do
          stub_request(:patch, "https://graph.microsoft.com/v1.0/subscriptions/#{subscription_id}").
          to_return(status: 401, body: '', headers: {})
        end

        it 'raises error' do
          expect { RenewOutlookWebhook.call(@acct) }.to raise_error(ExceptionHandler::OutlookAuthenticationError, ErrorCode.outlook_authentication_failed)
        end
      end

      context 'invalid details' do
        before do
          stub_request(:patch, "https://graph.microsoft.com/v1.0/subscriptions/#{subscription_id}").
          to_return(status: 422, body: '', headers: {})
        end

        it 'raises error' do
          expect { RenewOutlookWebhook.call(@acct) }.to raise_error(ExceptionHandler::ThirdPartyAPIError, ErrorCode.third_party_api)
        end
      end

      context 'when subscription is already expired' do
        before do
          stub_request(:patch, "https://graph.microsoft.com/v1.0/subscriptions/#{subscription_id}").
            to_return(status: 404, body: '', headers: {})
          stub_request(:post, "https://graph.microsoft.com/v1.0/subscriptions?validationToken=kylas").
            to_return(status: 200, body: { 'id' => '789', 'expirationDateTime': '2021-05-01T19:59:45.9356913Z' }.to_json, headers: {})
        end

        it 'registers new webhook' do
          acct = RenewOutlookWebhook.call(@acct).result
          expect(@acct.reload.subscription_expiry).to eq(Time.parse('2021-05-01T19:59:45.9356913Z').to_i)
          expect(acct.subscription_id).to eq('789')
        end
      end
    end
  end
end
