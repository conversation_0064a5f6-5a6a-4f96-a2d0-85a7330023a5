require 'rails_helper'

RSpec.describe OutlookMailSender do
  describe '#call' do
    before do
      user = create(:user)
      thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
      @message_source_id = 'message-1234'
      @connected_account = create(:connected_account, provider_name: MICROSOFT_PROVIDER, expires_at: (Time.now + 1.hour).to_i)
      @email = create(:email, owner: user, connected_account: @connected_account, email_thread_id: thread.id, source_id: @message_source_id)
    end

    context 'Send Email' do
      context 'Success' do
        before do
          stub_request(:post, GRAPH_HOST + "/v1.0/me/messages").
            to_return(status: 201, body: { id: 'SOURCE_ID', conversationId: 'THREAD_ID' }.to_json, headers: {})
          allow_any_instance_of(OutlookMailSender).to receive(:send_draft).and_return(nil)
          @params = { email: @email }.with_indifferent_access
        end

        it 'returns thread id and source id' do
          @res = OutlookMailSender.call(@params, @connected_account.access_token).result
          expect(@res.thread_id).to eq 'THREAD_ID'
          expect(@res.id).to eq 'SOURCE_ID'
        end

        context 'when large file more than 3 mb is in attachment' do
          let(:file_name) { 'test_photo.jpg' }

          before { @attachment = File.new("#{Rails.root}/spec/fixtures/files/#{file_name}") }

          it 'calls file chunk upload Api correctly' do
            @params[:attachments] = [{ data: @attachment, fileName: file_name }]
            expect_any_instance_of(OutlookMailSender).to receive(:create_attachment_upload_session).once
            expect_any_instance_of(OutlookMailSender).to receive(:upload_chunk).twice
            expect_any_instance_of(OutlookMailSender).not_to receive(:upload_small_attachment)

            OutlookMailSender.call(@params, @connected_account.access_token)
          end
        end

        context 'when small file less than 3 mb is in attachment' do
          let(:file_name) { 'test-text-file.txt' }

          before { @attachment = File.new("#{Rails.root}/spec/fixtures/files/#{file_name}") }

          it 'calls file upload Api correctly' do
            @params[:attachments] = [{ data: @attachment, fileName: file_name }]
            expect_any_instance_of(OutlookMailSender).to receive(:upload_small_attachment).once
            expect_any_instance_of(OutlookMailSender).not_to receive(:create_attachment_upload_session)
            expect_any_instance_of(OutlookMailSender).not_to receive(:upload_chunk)
            OutlookMailSender.call(@params, @connected_account.access_token)
          end
        end

        context 'when 1 small and 1 large attachment is provided with email' do
          let(:file_1_name) { 'test_photo.jpg' }
          let(:file_2_name) { 'test-text-file.txt' }

          before do
            @attachment_1 = File.new("#{Rails.root}/spec/fixtures/files/#{file_1_name}")
            @attachment_2 = File.new("#{Rails.root}/spec/fixtures/files/#{file_2_name}")
          end

          it 'calls file upload Apis correctly' do
            @params[:attachments] = [{ data: @attachment_1, fileName: file_1_name }, { data: @attachment_2, fileName: file_2_name }]

            expect_any_instance_of(OutlookMailSender).to receive(:upload_small_attachment).once
            expect_any_instance_of(OutlookMailSender).to receive(:create_attachment_upload_session).once
            expect_any_instance_of(OutlookMailSender).to receive(:upload_chunk).twice
            OutlookMailSender.call(@params, @connected_account.access_token)
          end
        end

        context 'when inline attachment less than 3mb is in attachment' do
          let(:file_name) { 'test-text-file.txt' }

          before { @attachment = File.new("#{Rails.root}/spec/fixtures/files/#{file_name}") }

          it 'calls file upload Api correctly' do
            attahcment_request_stub = stub_request(:post, GRAPH_HOST + "/v1.0/me/messages/SOURCE_ID/attachments")
              .with({ body: hash_including({ 'contentId': "1234", "isInline": true }) })
              .to_return(status: 201, body: { id: 'SOURCE_ID', conversationId: 'THREAD_ID' }.to_json, headers: {})

            @params[:attachments] = [{ data: @attachment, fileName: file_name, content_id: '1234' }]
            expect_any_instance_of(OutlookMailSender).not_to receive(:create_attachment_upload_session)
            expect_any_instance_of(OutlookMailSender).not_to receive(:upload_chunk)
            OutlookMailSender.call(@params, @connected_account.access_token)

            expect(attahcment_request_stub).to have_been_requested
          end
        end

        context 'when inline attachment more than 3mb is in attachment' do
          let(:file_name) { 'test_photo.jpg' }

          before { @attachment = File.new("#{Rails.root}/spec/fixtures/files/#{file_name}") }

          it 'calls file chunk upload api correctly' do
            attahcment_request_stub = stub_request(:post, GRAPH_HOST + "/v1.0/me/messages/SOURCE_ID/attachments/createUploadSession")
              .with({ body: hash_including({ AttachmentItem: { "attachmentType":"file","name":"test_photo.jpg","size":3238197,"contentId":"1234","isInline":true } }) })
              .to_return(status: 201, body: { uploadUrl: 'https://uploadUrl.com' }.to_json, headers: {})

            @params[:attachments] = [{ data: @attachment, fileName: file_name, content_id: '1234' }]
            expect_any_instance_of(OutlookMailSender).not_to receive(:upload_small_attachment)
            expect_any_instance_of(OutlookMailSender).to receive(:upload_chunk).twice
            OutlookMailSender.call(@params, @connected_account.access_token)

            expect(attahcment_request_stub).to have_been_requested
          end
        end
      end
    end

    context "Reply on an Email" do
      before do
        stub_request(:post, GRAPH_HOST + "/v1.0/me/messages/#{@message_source_id}/createReply").
          to_return(status: 201, body: { id: 'SOURCE_ID', conversationId: 'THREAD_ID' }.to_json, headers: {})
        stub_request(:patch, GRAPH_HOST + "/v1.0/me/messages/SOURCE_ID").
          to_return(status: 201, body: { id: 'SOURCE_ID', conversationId: 'THREAD_ID' }.to_json, headers: {})

        allow_any_instance_of(OutlookMailSender).to receive(:send_draft).and_return(nil)
        @params = { email: @email, reply_to_email_id: @message_source_id }.with_indifferent_access
      end

      it 'returns thread id and source id' do
        @res = OutlookMailSender.call(@params, @connected_account.access_token).result
        expect(@res.thread_id).to eq 'THREAD_ID'
        expect(@res.id).to eq 'SOURCE_ID'
      end
    end

    context 'when graph api authentication fails' do
      before do
        stub_request(:post, GRAPH_HOST + "/v1.0/me/messages").
          to_return(status: 401, body: '', headers: {})
      end

      it 'returns thread id and source id' do
        expect{ OutlookMailSender.call({ email: @email }, "test-token").result }.to raise_error(ExceptionHandler::OutlookAuthenticationError, ErrorCode.outlook_authentication_failed)
      end
    end

    context 'when any other graph api error occurs' do
      before do
        stub_request(:post, GRAPH_HOST + "/v1.0/me/messages").
          to_return(status: 422, body: '', headers: {})
      end

      it 'returns thread id and source id' do
        expect{ OutlookMailSender.call({ email: @email }, "test-token").result }.to raise_error(ExceptionHandler::ThirdPartyAPIError, ErrorCode.third_party_api)
      end
    end

    context 'when user rate limit exceeded' do
      before { stub_request(:post, GRAPH_HOST + "/v1.0/me/messages").to_return(status: 429, body: '', headers: {}) }

      it 'raises invalid error with rate limit error code' do
        expect(Rails.logger).to receive(:error).with("OutlookMailSender Too Many Requests 429")
        expect{ OutlookMailSender.call({ email: @email }, "test-token").result }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.rate_limit_error)
      end
    end
  end
end
