require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForUserUpdate do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic USER_EXCHANGE
    end

    context 'valid input' do
      before do

        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(USER_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(USER_EXCHANGE, USER_NAME_UPDATED_EVENT, USER_NAME_UPDATED_QUEUE)
          .and_yield(payload.to_s)
        ListenForUserUpdate.call()
      end

      context 'user name updated event' do
        let(:routing_key) { USER_NAME_UPDATED_EVENT }
        let(:payload)     { { "userId" => @user.id, "tenantId" => @user.tenant_id, "firstName" => "<PERSON>", "lastName" => "Doe" }.to_json }

        it 'updates the User with new name' do
          user = User.find(@user.id)
          expect(user.name).to be == "John Doe"
        end
      end
    end
  end
end
