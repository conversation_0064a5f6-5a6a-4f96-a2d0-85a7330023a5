require 'rails_helper'

RSpec.describe RegisterOutlookWebhook do
  describe '#call' do
    before do
      @user = create(:user)
      @expires_at = (Time.now + 3.days).to_i
      @acct = create(:connected_account, provider_name: MICROSOFT_PROVIDER, expires_at: @expires_at, user: @user)
      stub_request(:post, "https://graph.microsoft.com/v1.0/subscriptions?validationToken=kylas").
        to_return(status: 200, body: { 'id' => '123', 'expirationDateTime': '2021-05-01T18:59:45.9356913Z' }.to_json, headers: {})

      thread = Thread.current
      thread[:token] = GenerateToken.call(@acct.user.id, @acct.user.tenant_id).result
    end

    context 'when subscription is successful' do
      it 'stores subscription id and expiry in the database' do
        acct = RegisterOutlookWebhook.call(@acct).result
        expect(@acct.subscription_expiry).to eq(Time.parse('2021-05-01T18:59:45.9356913Z').to_i)
        expect(acct.subscription_id).to eq(@acct.subscription_id)
      end
    end

    context 'when subscription is failed' do
      context 'invalid outlook token' do
        before do
          stub_request(:post, "https://graph.microsoft.com/v1.0/subscriptions?validationToken=kylas").
            to_return(status: 401, body: '', headers: {})
        end

        it 'raises error' do
          expect { RegisterOutlookWebhook.call(@acct) }.to raise_error(ExceptionHandler::OutlookAuthenticationError, ErrorCode.outlook_authentication_failed)
        end
      end

      context 'invalid details' do
        before do
          stub_request(:post, "https://graph.microsoft.com/v1.0/subscriptions?validationToken=kylas").
            to_return(status: 422, body: '', headers: {})
        end

        it 'raises error' do
          expect { RegisterOutlookWebhook.call(@acct) }.to raise_error(ExceptionHandler::ThirdPartyAPIError, ErrorCode.third_party_api)
        end
      end
    end
  end
end
