require 'rails_helper'

RSpec.describe CreateTrackMapping do
  describe '#call' do
    before do
      @body = 'This is test body'
    end

    context 'create track mapping' do
      before do
        @email =  create(:email)
        @result = CreateTrackMapping.call(@body, @email.id, @email.tenant_id).result
      end

      it 'returns updated body' do
        expect(@result[:body]).to include "This is test body<img src=\"http://localhost:3000/v1/email_mappings/#{@result[:track_mapping].id}"
      end

      it 'returns track_mapping record' do
        expect(@result[:track_mapping]).not_to be_blank
        expect(TrackMapping.last.email_id).to eq @email.id
        expect(TrackMapping.last.tenant_id).to eq(@email.tenant_id)
      end
    end
  end
end
