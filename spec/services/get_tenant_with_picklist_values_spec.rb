require 'rails_helper'

RSpec.describe GetTenantWithPicklistValues do
  describe "Get Tenant Data" do
    before do
      stub_entity_labels_api
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      let(:tenant_data) { file_fixture('tenant-account.json').read }
      let(:standard_picklist) { file_fixture('standard-picklist.json').read }
      let(:processed_tenant_data){ JSON.parse(file_fixture('processed-tenant-data.json').read) }

      before(:each) do
        stub_request(:get, SERVICE_IAM + "/v1/tenants").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}"
            }).
          to_return(status: 200, body: tenant_data, headers: {})

        stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
          with(
            headers: {
              "Authorization" => "Bearer #{@token}"
            }).
          to_return(status: 200, body: standard_picklist, headers: {})
      end

      it "returns processed tenant data in output" do
        command = described_class.call('lead')
        expect(command.success?).to be(true)
        expect(command.result).to eq(processed_tenant_data)
      end
    end
  end
end
