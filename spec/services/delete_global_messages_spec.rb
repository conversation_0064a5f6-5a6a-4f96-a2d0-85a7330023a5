# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DeleteGlobalMessages do
  describe "#call" do
    context 'when called' do
      before do
        global_message = create(:global_message)
        global_message_created_before_seven_days = create(:global_message, created_at: 8.days.ago)
      end

      it 'deletes all messages created before last 7 days' do
        expect { described_class.call }.to change { GlobalMessage.count }.by(-1)
      end
    end
  end
end
