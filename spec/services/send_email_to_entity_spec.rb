# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SendEmailToEntity do
  describe '#call' do
    before do
      stub_entity_labels_api
      allow(TenantEmailTemplateUsagePublisher).to receive(:publish).with(instance_of(Integer))
      @user = create(:user, id: 12, tenant: create(:tenant, id: 99))
      auth_data =  build(:auth_data, :email_template_with_read_access, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      thread = Thread.current
      thread[:auth] = auth_data
      @token = build(:auth_token, :with_email_template_read_all_permission, user_id: 12, tenant_id: 99).token
      thread[:token] = @token

      stub_request(:get, SERVICE_IAM + "/v1/users/me").
        with(
          headers: {
            "Authorization" => "Bearer #{@token}",
            'Content-Type'=>'application/json'
          }).
        to_return(status: 200, body: file_fixture('user-profile-response.json').read, headers: {})

      stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
        with(
          headers: {
            "Authorization" => "Bearer #{@token}"
          }).
        to_return(status: 200, body: file_fixture('standard-picklist.json').read, headers: {})

      stub_request(:get, SERVICE_SALES + "/v1/leads/1").
        with(
          headers: {
          "Authorization" => "Bearer #{@token}"
          }).
        to_return(status: 200, body: file_fixture('lead-data.json').read, headers: {})

      stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?entityType=lead&custom-only=false&sort=createdAt,asc&page=0&size=100").
        with(
          headers: {
          "Authorization" => "Bearer #{@token}"
          }).
        to_return(status: 200, body: file_fixture('lead-fields.json').read, headers: {})

      stub_request(:post, SERVICE_IAM + "/v1/users/search?sort=updatedAt,desc&page=0&size=100")
        .to_return(status: 200, body: file_fixture('user-response.json'), headers: {})

      @connected_account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: @user, tenant_id: @user.tenant_id)
      create(:look_up, entity: 'lead_1', tenant_id: @user.tenant_id, email: '<EMAIL>')
    
      setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', @connected_account.email).and_return(setting)

    end

    def prepare_payload(args = {})
      {
        subject: 'Test subject',
        body: 'Test Email Body',
        emailTemplateId: args[:email_template_id],
        to: [
          {
            entity: 'lead',
            id: '1',
            email: '<EMAIL>',
            name: 'Sample Lead'
          }
        ],
        relatedTo: {
          entity: 'lead',
          id: '1',
          email: '<EMAIL>',
          name: 'Sample Lead'
        },
        cc:[
          {
            entity: 'user',
            id: '1',
            name: 'User Name',
            email: '<EMAIL>'
          }
        ],
        bcc:[
          {
            entity: 'user',
            id: '2',
            name: 'Other User Name',
            email: '<EMAIL>'
          }
        ],
        attachments:[
          {
            id: nil,
            data: Rack::Test::UploadedFile.new(file_fixture('test-text-file.txt'), 'application/txt'),
            fileName: 'test-file.txt'
          }
        ]
      }.with_indifferent_access
    end

    def prepare_payload_with_campaign_info(args = {})
      {
        subject: 'Test subject',
        body: 'Test Email Body',
        emailTemplateId: args[:email_template_id],
        to: [
          {
            entity: 'lead',
            id: '1',
            email: '<EMAIL>',
            name: 'Sample Lead'
          }
        ],
        relatedTo: {
          entity: 'lead',
          id: '1',
          email: '<EMAIL>',
          name: 'Sample Lead'
        },
        cc:[
          {
            entity: 'user',
            id: '1',
            name: 'User Name',
            email: '<EMAIL>'
          }
        ],
        bcc:[
          {
            entity: 'user',
            id: '2',
            name: 'Other User Name',
            email: '<EMAIL>'
          }
        ],
        attachments:[
          {
            id: nil,
            data: Rack::Test::UploadedFile.new(file_fixture('test-text-file.txt'), 'application/txt'),
            fileName: 'test-file.txt'
          }
        ],
        campaign: { id: 123, name: 'Test Campaign' },
        activity: { id: 234, name: 'Test Activity' }
      }.with_indifferent_access
    end

    context 'when email template id is present' do
      before do
        @email_template = create(
          :email_template,
          tenant_id: @user.tenant_id,
          created_by: @user,
          subject: "Lead created {{lead.salutation}} {{lead.firstName}} {{lead.lastName}}",
          body: "Lead {{lead.salutation}} {{lead.firstName}} {{lead.lastName}} created at {{lead.createdAt}} has owner {{lead.ownerId}}({{ownerId.profileId}})"
        )
        create(:template_attachment, email_template: @email_template, file_name: "tenant_#{@email_template.tenant_id}/user_#{@email_template.created_by_id}/#{@email_template.id}_some file name_#{DateTime.now.to_i}.pdf")
      end

      context 'valid' do
        before do
          expect(UploadFileToS3).to receive(:call).exactly(2).times
          expect(DownloadAttachmentFromS3).to receive_message_chain(:call, :result).and_return(File.new(file_fixture('test-text-file.txt')))
          expect(PublishEvent).to receive(:call).exactly(4).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailCreatedWorkflowV2)).once
          header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
          payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
          return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
          expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
          expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
        end

      
        it 'should replace variables and create email with attachments' do
          params = prepare_payload({ email_template_id: @email_template.id })
          described_class.call(params)

          email = Email.last
          expect(email.to).to match_array(params[:to].map { |to| to['email'] })
          expect(email.cc).to match_array(params[:cc].map { |cc| cc['email'] })
          expect(email.bcc).to match_array(params[:bcc].map { |bcc| bcc['email'] })
          expect(email.from).to eq(@user.connected_accounts.first.email)
          expect(email.tenant_id).to eq(@user.tenant_id)
          expect(email.subject).to eq('Lead created Miss shweta kylas')
          expect(email.body).to eq('Lead Miss shweta kylas created at Apr 01,2022 at 3:06 pm IST has owner Siby P()')
          expect(email.attachments.count).to be(2)
        end
        
        context 'when campaign info is not present in the params' do
          it 'should replace variables and create email with attachments and campaign info' do
            params = prepare_payload_with_campaign_info({ email_template_id: @email_template.id })
            described_class.call(params)

            email = Email.last
            expect(email.to).to match_array(params[:to].map { |to| to['email'] })
            expect(email.cc).to match_array(params[:cc].map { |cc| cc['email'] })
            expect(email.bcc).to match_array(params[:bcc].map { |bcc| bcc['email'] })
            expect(email.from).to eq(@user.connected_accounts.first.email)
            expect(email.tenant_id).to eq(@user.tenant_id)
            expect(email.subject).to eq('Lead created Miss shweta kylas')
            expect(email.body).to eq('Lead Miss shweta kylas created at Apr 01,2022 at 3:06 pm IST has owner Siby P()')
            expect(email.attachments.count).to be(2)
            expect(email.campaign_info).to eq({
              "email"=>"<EMAIL>", 
              "entityId"=>"1", 
              "activityId"=>234, 
              "campaignId"=>123, 
              "entitytype"=>"lead"
            })
          end
        end
      end

      context 'invalid' do
        context 'when email template is not present' do
          it 'should raise not found error' do
            params = prepare_payload({ email_template_id: @email_template.id + 1 })
            expect(Rails.logger).to receive(:error).with("SendEmailToEntity EmailTemplate not found for tenant #{@user.tenant_id} with id #{@email_template.id + 1} for entity type lead")
            expect do
              described_class.call(params)
            end.to raise_error(ExceptionHandler::NotFound, ErrorCode.not_found)
          end
        end

        context 'when email template is inactive' do
          before { @email_template.update!(active: false) }

          it 'should raise invalid error with template inactive error code' do
            params = prepare_payload({ email_template_id: @email_template.id })
            expect(Rails.logger).to receive(:error).with("SendEmailToEntity EmailTemplate inactive for tenant #{@user.tenant_id} with id #{@email_template.id}")
            expect do
              described_class.call(params)
            end.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.inactive_email_template)
          end
        end

        context 'when email template category differs from related entity' do
          before { @email_template.update!(category: 'contact', active: true) }

          it 'should raise not found error' do
            params = prepare_payload({ email_template_id: @email_template.id })
            expect(Rails.logger).to receive(:error).with("SendEmailToEntity EmailTemplate not found for tenant #{@user.tenant_id} with id #{@email_template.id} for entity type lead")
            expect do
              described_class.call(params)
            end.to raise_error(ExceptionHandler::NotFound, ErrorCode.not_found)
          end
        end
      end
    end

    context 'when email template id is not present' do
      context 'valid' do
        before do
          expect(UploadFileToS3).to receive(:call)
          expect(PublishEvent).to receive(:call).exactly(4).times
          expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailCreatedWorkflowV2)).once
          header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
          payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
          return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
          expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
          expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
        end

        it 'should replace variables and create email with attachments' do
          params = prepare_payload
          described_class.call(params)

          email = Email.last
          expect(email.to).to match_array(params[:to].map { |to| to['email'] })
          expect(email.cc).to match_array(params[:cc].map { |cc| cc['email'] })
          expect(email.bcc).to match_array(params[:bcc].map { |bcc| bcc['email'] })
          expect(email.from).to eq(@user.connected_accounts.first.email)
          expect(email.tenant_id).to eq(@user.tenant_id)
          expect(email.subject).to eq('Test subject')
          expect(email.body).to eq('Test Email Body')
          expect(email.attachments.count).to be(1)
        end
      end
    end
  end
end
