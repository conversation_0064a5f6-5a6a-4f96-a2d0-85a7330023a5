# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SharedAccess do
  describe '#fetch' do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity|
      context 'success' do
        context "entity is #{entity}" do
          before do
            stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/EMAIL")
              .with(
                headers: {
                  Authorization: "Bearer #{@token}"
                }
              )
              .to_return(status: 200, body: { accessByOwners: {}, accessByRecords: { "123" => { read: true, email: true } } }.to_json)
          end

          it 'should return access by owner & records' do
            result = described_class.new(entity).fetch
            expect(result).to eq({ "accessByOwners" => {}, "accessByRecords" => { "123" => { "read" => true, "email" => true } } })
          end
        end
      end

      context 'failure' do
        context 'when API returns 404' do
          before do
            stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/EMAIL").to_return(status: 404)
          end

          it 'should log & raise error' do
            expect(Rails.logger).to receive(:error).with("SharedAccess - 404")
            expect do
              described_class.new(entity).fetch
            end.to raise_error(ExceptionHandler::InvalidDataError, "01603001")
          end
        end
      end

      context 'when API returns 500' do
        before do
          stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/EMAIL").to_return(status: 500)
        end

        it 'should log & raise error' do
          expect(Rails.logger).to receive(:error).with("SharedAccess - 500")
          expect do
            described_class.new(entity).fetch
          end.to raise_error(ExceptionHandler::InternalServerError, "01602003")
        end
      end

      context 'when API returns 400' do
        before do
          stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/EMAIL").to_return(status: 400)
        end

        it 'should log & raise error' do
          expect(Rails.logger).to receive(:error).with("SharedAccess - 400")
          expect do
            described_class.new(entity).fetch
          end.to raise_error(ExceptionHandler::InvalidDataError, "01603001")
        end
      end
    end
  end
end
