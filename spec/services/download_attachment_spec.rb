# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DownloadAttachment do
  describe '#call' do
    let(:user)      { create(:user) }
    let(:token)     { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ).token }
    let(:auth_data) { ParseToken.call(token).result }

    before do
      @email = create(:email, owner_id: user.id, tenant_id: user.tenant_id)
      @attachment = create(:attachment, email: @email)
      allow(GetPresignedUrlFromS3).to receive_message_chain(:call, :result).and_return({ url: 'https://www.kylas.io', file_name: 'abc.pdf' })
    end

    context 'success' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = token
      end

      it 'returns presigned url' do
        command = described_class.call(@email.id, @attachment.id)

        expect(command.success?).to be_truthy
        expect(command.result).to eq({ url: 'https://www.kylas.io', file_name: 'abc.pdf' })
      end
    end

    context 'failure' do
      context 'when email is not present' do
        before do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = token
        end

        it 'raises & logs error' do
          expect(Rails.logger).to receive(:error).with("Email not found while downloading attachment. User id #{user.id} email id #{@email.id + 1} attachment id #{@attachment.id}")
          expect do
            described_class.call(@email.id + 1, @attachment.id)
          end.to raise_error(ExceptionHandler::NotFound, "01602001")
        end
      end

      context 'when user does not have read on email' do
        before do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = token
          @email.update(owner: create(:user, tenant_id: user.tenant_id))
          allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => {} })
        end

        it 'raises & logs error' do
          expect(Rails.logger).to receive(:error).with("Unauthorised: User does not have access to email while downloading attachment. User id #{user.id} email id #{@email.id} attachment id #{@attachment.id}")
          expect do
            described_class.call(@email.id, @attachment.id)
          end.to raise_error(ExceptionHandler::Forbidden, "01601005")
        end
      end

      context 'when attachment is not present' do
        before do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = token
        end

        it 'raises & logs error' do
          expect(Rails.logger).to receive(:error).with("Attachment not found while downloading attachment. User id #{user.id} email id #{@email.id} attachment id #{@attachment.id + 1}")
          expect do
            described_class.call(@email.id, @attachment.id + 1)
          end.to raise_error(ExceptionHandler::NotFound, "01602001")
        end
      end

      context 'when user context is not present' do
        before do
          Thread.current[:auth] = nil
          Thread.current[:token] = nil
        end

        it 'raises & logs error' do
          expect(Rails.logger).to receive(:error).with('Unauthorised: User context missing in DownloadAttachment')
          expect do
            described_class.call(@email.id, @attachment.id + 1)
          end.to raise_error(ExceptionHandler::AuthenticationError, '01601005')
        end
      end
    end
  end
end
