require 'rails_helper'
require 'bunny-mock'

RSpec.describe SmtpAccountDisconnectPublisher do
  describe '#call' do
    before do
      BunnyMock.use_bunny_queue_pop_api = true
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic EMAIL_EXCHANGE

      @connected_account = create(:connected_account)

      @event = Event::SmtpAccountDisconnect.new(@connected_account.user_id, @connected_account.tenant_id, @connected_account.email, 'some reason')

      @queue = @channel.queue "email.disconnect.emailClient"
      @queue.bind @exchange, routing_key: "email.disconnect.emailClient"
      allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)
    end

    context "when active connected account exists" do
      before { SmtpAccountDisconnectPublisher.call(@connected_account.access_token) }

      it 'raises an event in the Email Exchange & routes it to queue email.opened.notify.user' do
        expect(@queue.message_count).to eq(1)
      end
    end

    context "when active connected account doesnt" do
      before { SmtpAccountDisconnectPublisher.call("dummy") }

      it 'raises an event in the Email Exchange & routes it to queue email.opened.notify.user' do
        expect(@queue.message_count).to eq(0)
      end
    end
  end
end
