require 'rails_helper'

RSpec.describe ValidateDeals do
  describe '#call' do
    let(:deal_search_response) { file_fixture('deal-search-response.json').read }

    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @deal = build(:look_up, entity_type: LOOKUP_DEAL, entity_id: 9, tenant_id: 10)
      @auth_data = ParseToken.call(@token).result
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
      stub_request(:post, "http://localhost:8083/v1/search/deal?page=0&size=1000&sort=updatedAt,desc").
        with(
          headers: {
            Authorization: "Bearer #{@token}"
          },
          body: {
            fields: %w[id name ownedBy ownerId],
            jsonRule:{
              condition: 'AND',
              rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: '9' }],
              valid: true
            }
          }
        ).
        to_return(status: 200, body: deal_search_response, headers: {})
    end

    context 'valid input' do
      it ' returns if no deals are passed' do
        expect(described_class.call(nil).success?).to be true
      end

      context 'when deal id matches' do
        it 'returns array of updated deal look ups' do
          command = described_class.call([@deal])
          expect(command.result.class).to be Array
          expect(command.result.count).to be == 1
          expect(command.result[0].class).to be == LookUp
        end

        it 'returns lookups with updated names' do
          command = described_class.call([@deal])
          expect(command.result.first.name).to eq('Email Deal')
          expect(command.result.first.owner_id).to eq(4167)
        end
      end
    end
    context 'invalid entry from deal' do
      before do
        @invalid_deal = build(:look_up, entity_type: LOOKUP_DEAL, entity_id: 10, tenant_id: 9)
        stub_request(:post, "http://localhost:8083/v1/search/deal?page=0&size=1000&sort=updatedAt,desc").
          with(
            headers: {
              Authorization: "Bearer #{@token}"
            },
            body: {
              fields: %w[id name ownedBy ownerId],
              jsonRule:{
                condition: 'AND',
                rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: '9,10' }],
                valid: true
              }
            }
          ).
          to_return(status: 200, body: deal_search_response, headers: {})
      end

      it 'raises InvalidDataError for invalid deal' do
        expect {described_class.call([@deal, @invalid_deal])}.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid
        )
      end
    end
  end
end
