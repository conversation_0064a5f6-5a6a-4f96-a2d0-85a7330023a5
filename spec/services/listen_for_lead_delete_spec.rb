require 'rails_helper'

RSpec.describe ListenForLeadDelete do
  describe '#call' do
    before do
      @user = create(:user)
      @lead_lookup = create(:look_up, entity: 'lead_12')
      allow(RabbitmqConnection).to receive(:subscribe).with(LEAD_EXCHANGE, LEAD_DELETED_EVENT,
                                                            LEAD_DELETED_QUEUE).and_yield(payload.to_s)
    end

    context 'valid input' do
      context 'for lead deleted event' do
        let(:payload) do
          {
            'id' => @lead_lookup.entity_id,
            'tenantId' => @lead_lookup.tenant_id,
            'userId' => @user.id,
            'publishUsage' => true
          }.to_json
        end

        it 'should call relevant job with parameters' do
          expected_options = { id: @lead_lookup.entity_id, tenant_id: @lead_lookup.tenant_id, user_id: @user.id, publish_usage: true, entity_type: LOOKUP_LEAD }
          expect(UpdateEmailForEntityDeleteEventJob).to receive(:perform_later).with(expected_options)
          described_class.call
        end
      end
    end
  end
end
