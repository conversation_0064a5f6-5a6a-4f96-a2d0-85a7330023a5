# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UpdateEmailForEntityDeleteEvent do
  describe '#call' do
    context 'when entity is lead' do
      context 'valid' do
        before do
          @user = create(:user)
          @lead_lookup = create(:look_up, entity: 'lead_12')
          allow(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata))
          allow(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata))
          allow(FetchUser).to receive_message_chain(:call, :result).and_return(User.new(name: 'S k'))
        end

        context 'valid input' do
          context 'for lead deleted event' do
            let(:options) do
              {
                id: @lead_lookup.entity_id,
                tenant_id: @lead_lookup.tenant_id,
                user_id: @user.id,
                publish_usage: true,
                entity_type: LOOKUP_LEAD
              }
            end

            context 'only one email is associated with lookup' do
              it 'Should delete email and look up' do
                email = create(:email, owner: @user)
                email.to_recipients << @lead_lookup

                expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction)).once
                expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailDeletedWorkflowV2)).once
                expect(PublishUsageJob).to receive(:perform_later).with(@lead_lookup.tenant_id)

                described_class.call(options[:id], options[:tenant_id], options[:user_id], options[:entity_type], options[:publish_usage])

                expect(Email.count).to eq(0)
                expect(EmailLookUp.count).to eql(0)
              end
            end

            context 'Multiple email ids are associated with lookup' do
              it 'deletes all the lookups with given lookup id' do
                email = create(:email, owner: @user)
                email.to_recipients << @lead_lookup

                expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction)).once
                expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailDeletedWorkflowV2)).once

                expect(PublishUsageJob).to receive(:perform_later).with(@lead_lookup.tenant_id)

                described_class.call(options[:id], options[:tenant_id], options[:user_id], options[:entity_type], options[:publish_usage])

                expect(Email.count).to eq(0)
                expect(EmailLookUp.count).to eql(0)
                expect(LookUp.unscoped.where(entity: 'lead_12').count).to eql(1)
                expect(LookUp.unscoped.where(entity: 'lead_12').pluck(:deleted)).to eq([true])
              end
            end

            context 'Multiple emails are associated with lookup' do
              it 'Should delete email and look up' do
                email1 = create(:email, owner: @user)
                email2 = create(:email, owner: @user)
                other_lookup = build(:look_up, entity: 'customemail', tenant_id: @user.tenant_id,
                                               email: 'janedoe@example.com1')
                email1.to_recipients << @lead_lookup
                email1.to_recipients << other_lookup

                email2.to_recipients << @lead_lookup
                email2.to_recipients << other_lookup

                expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction)).twice
                expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailDeletedWorkflowV2)).twice

                expect(PublishUsageJob).to receive(:perform_later).with(@lead_lookup.tenant_id)

                described_class.call(options[:id], options[:tenant_id], options[:user_id], options[:entity_type], options[:publish_usage])

                expect(Email.count).to eq(0)
                expect(EmailLookUp.count).to eql(0)
              end
            end

            context 'Email has other recipients as well' do
              before do
                email = create(:email, owner: @user)
                email.to_recipients << @lead_lookup
                email.to_recipients << create(:look_up, entity: 'lead_121')
                expect(PublishUsageJob).to receive(:perform_later).with(@lead_lookup.tenant_id)
                described_class.call(options[:id], options[:tenant_id], options[:user_id], options[:entity_type], options[:publish_usage])
              end

              it "Shouldn't delete email" do
                expect(Email.count).to eq(1)
              end

              it 'should change lookup to custom' do
                expect(LookUp.find(@lead_lookup.id).entity).to eq(LOOKUP_CUSTOM_EMAIL)
              end
            end
          end
        end
      end
    end

    context 'when entity is contact' do
      before do
        @user = create(:user)
        @contact_lookup = create(:look_up, entity: 'contact_12')
        allow(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata))
        allow(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata))
        allow(FetchUser).to receive_message_chain(:call, :result).and_return(User.new(name: 'S k'))
      end

      context 'valid input' do
        context 'for contact deleted event' do
          let(:options) do
            {
              id: @contact_lookup.entity_id,
              tenant_id: @contact_lookup.tenant_id,
              user_id: @user.id,
              publish_usage: true,
              entity_type: LOOKUP_CONTACT
            }
          end

          context 'only one email is associated with lookup' do
            it 'should delete email and look up' do
              email = create(:email, owner: @user)
              email.to_recipients << @contact_lookup

              expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction)).once
              expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailDeletedWorkflowV2)).once
              expect(PublishUsageJob).to receive(:perform_later).with(@contact_lookup.tenant_id)

              described_class.call(options[:id], options[:tenant_id], options[:user_id], options[:entity_type], options[:publish_usage])

              expect(Email.count).to eq(0)
              expect(EmailLookUp.count).to eql(0)
            end
          end

          context 'Email has other recipients as well' do
            before do
              email = create(:email, owner: @user)
              email.to_recipients << @contact_lookup
              email.to_recipients << create(:look_up, entity: 'contact_121')
              expect(PublishUsageJob).to receive(:perform_later).with(@contact_lookup.tenant_id)
              described_class.call(options[:id], options[:tenant_id], options[:user_id], options[:entity_type], options[:publish_usage])
            end

            it "shouldn't delete email" do
              expect(Email.count).to eq(1)
            end

            it 'should change lookup to custom' do
              expect(LookUp.find(@contact_lookup.id).entity).to eq(LOOKUP_CUSTOM_EMAIL)
            end
          end
        end
      end
    end

    context 'when entity is deal' do
      before do
        @user = create(:user)
        allow(PublishEvent).to receive(:call).with(instance_of(Event::LeadMetadata))
        allow(PublishEvent).to receive(:call).with(instance_of(Event::DealMetadata))
        @deal_lookup = create(:look_up, entity: 'deal_12')
      end

      context 'valid input' do
        context 'for deal deleted event' do
          let(:options) do
            {
              id: @deal_lookup.entity_id,
              tenant_id: @deal_lookup.tenant_id,
              user_id: @user.id,
              publish_usage: true,
              entity_type: LOOKUP_DEAL
            }
          end

          context 'only one email is associated with lookup but current lookup is deal' do
            it "shouldn't delete email and look up" do
              email = create(:email, owner: @user)
              email.to_recipients << @deal_lookup

              expect(PublishUsageJob).to receive(:perform_later).with(@deal_lookup.tenant_id)

              described_class.call(options[:id], options[:tenant_id], options[:user_id], options[:entity_type], options[:publish_usage])

              expect(Email.count).to eq(1)
              expect(EmailLookUp.count).to eql(1)
            end
          end

          context 'Email has other recipients as well' do
            before do
              email = create(:email, owner: @user)
              email.to_recipients << @deal_lookup
              email.to_recipients << create(:look_up, entity: 'deal_121')
              expect(PublishUsageJob).to receive(:perform_later).with(@deal_lookup.tenant_id)
              described_class.call(options[:id], options[:tenant_id], options[:user_id], options[:entity_type], options[:publish_usage])
            end

            it "shouldn't delete email" do
              expect(Email.count).to eq(1)
            end

            it 'should change lookup to custom' do
              expect(@deal_lookup.reload.entity).to eq(LOOKUP_CUSTOM_EMAIL)
            end
          end
        end
      end
    end
  end
end
