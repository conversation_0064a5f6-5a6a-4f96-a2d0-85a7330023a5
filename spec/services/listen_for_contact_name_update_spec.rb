require 'rails_helper'
require 'bunny-mock'

RSpec.describe ListenForContactNameUpdate do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic CONTACT_EXCHANGE
      @contact = create(:look_up, entity_type: 'contact', tenant_id: @user.tenant_id)
    end

    context 'valid input' do
      before do

        expect(PublishEvent).to receive(:call).with(instance_of(Event::LookUpUpdated)).once
        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(CONTACT_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(CONTACT_EXCHANGE, CONTACT_NAME_UPDATED_EVENT, CONTACT_NAME_UPDATED_QUEUE)
          .and_yield(payload.to_s)
        ListenForContactNameUpdate.call()
      end

      context 'contact name updated event' do
        let(:routing_key) { CONTACT_NAME_UPDATED_EVENT }
        let(:payload)     { { "contactId" => @contact.entity_id, "tenantId" => @user.tenant_id, "firstName" => "John", "lastName" => "Doe" }.to_json }

        it 'updates the User with new name' do
          expect(@contact.reload.name).to be == "John Doe"
        end
      end
    end
  end
end
