# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetEmailDetails do
  describe "#call" do
    let(:user) { create(:user) }
    let(:auth_data) { build(:auth_data, :email_with_delete_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id).token }
    let(:email_thread) { create(:email_thread, owner: user, tenant_id: user.tenant_id) }
    let(:email) { create(:email, owner: user, tenant_id: user.tenant_id, email_thread: email_thread) }

    before do
      thread = Thread.current
      thread[:auth] = auth_data
      thread[:token] = token
      @inline_attachment = email.attachments.create(file_name: 'tenant_1/user_1/123_filename1_123.jpg', file_size: 1234, inline: true, content_id: 'content-id')
      @external_attachment = email.attachments.create(file_name: 'tenant_1/user_1/123_filename2_34432.pdf', file_size: 555, inline: false)
    end

    context "with valid input - " do
      it "returns email details for given email_id in output" do
        result = described_class.call(email.id)

        sender = {
          "id"=> email.sender.entity_id,
          "entity"=> email.sender.entity_type,
          "name"=> email.sender.name || email.sender.email
        }

        sender["email"] = email.sender.email if [LOOKUP_USER, LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_CUSTOM_EMAIL].include?(email.sender.entity_type)

        expected_result = {
          "id" => email.id,
          "body" => "Email body goes here",
          "sender" => sender,
          "relatedTo" => [],
          "toRecipients" => [],
          "ccRecipients" => [],
          "bccRecipients" => [],
          "subject" => "MyText",
          "sentAt" => email.created_at.iso8601(3),
          "status" => "received",
          "trackingEnabled" => false,
          "tenantId" => email.tenant_id,
          "threadId" => email.email_thread_id,
          "owner" => {
            "id" => email.owner.id,
            "name" => email.owner.name
          },
          "direction" => "received",
          "linksClickedAt" => [],
          "attachments" => [
            {
              "cid" => nil,
              "fileName" => 'filename2.pdf',
              "fileSize"=> 555,
              "id" => @external_attachment.id,
              "inline" => false
            },
            {
              "cid" => "content-id",
              "fileName" => 'filename1.jpg',
              "fileSize" => 1234,
              "id" => @inline_attachment.id,
              "inline" => true
            }
          ],
          "openedAt" => []
        }

        expect(result.result).to include(expected_result.except('attachments')).and(
          include('attachments' => match_array(expected_result['attachments']))
        )
      end
    end

    context "with invalid input - " do
      context "when unauthorized" do
        before { Thread.current[:auth] = nil }

        it 'should raise authentication error' do
          expect(Rails.logger).to receive(:error).with("Unauthorised: Missing user context in GetEmailDetails")

          expect do
            described_class.call(email.id)
          end.to raise_error(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
        end
      end

      context "when email is not present" do
        before do
          thread = Thread.current
          thread[:auth] = auth_data
          thread[:token] = token
        end

        it 'should raise not found error' do
          expect(Rails.logger).to receive(:error).with("Email not found")

          expect do
            described_class.call(324324)
          end.to raise_error(ExceptionHandler::NotFound, ErrorCode.not_found)
        end
      end
    end
  end
end
