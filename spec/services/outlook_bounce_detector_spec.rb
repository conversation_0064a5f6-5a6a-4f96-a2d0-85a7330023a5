require 'rails_helper'

RSpec.describe OutlookBounceDetector, type: :service do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:connected_account) { create(:connected_account, user: user, tenant_id: tenant.id) }

  let(:outlook_bounce_message) do
    JSON.parse(File.read(Rails.root.join('spec/fixtures/files/outlook_delivery_status_email.json')))
  end

  let(:regular_outlook_message) do
    {
      'id' => 'regular123',
      'subject' => 'Regular email',
      'from' => {
        'emailAddress' => {
          'address' => '<EMAIL>'
        }
      },
      'body' => {
        'content' => 'This is a regular email message.'
      }
    }
  end

  describe '.call' do
    context 'with an Outlook bounce message' do
      subject { OutlookBounceDetector.call(outlook_bounce_message, connected_account) }

      it 'detects the message as a bounce' do
        expect(subject[:is_bounce]).to be true
      end

      it 'determines the correct bounce type' do
        expect(subject[:bounce_type]).to eq('hard')
      end

      it 'extracts the failed reason' do
        expect(subject[:failed_reason]).to include('550')
      end

      it 'extracts the failed recipient' do
        expect(subject[:failed_recipient]).to eq('<EMAIL>')
      end

      it 'includes bounce details' do
        expect(subject[:bounce_details]).to include(
          provider: 'outlook',
          message_id: outlook_bounce_message['id']
        )
      end
    end

    context 'with a regular Outlook message' do
      subject { OutlookBounceDetector.call(regular_outlook_message, connected_account) }

      it 'does not detect the message as a bounce' do
        expect(subject).to be_nil
      end
    end

    context 'with soft bounce indicators' do
      let(:soft_bounce_message) do
        {
          'id' => 'soft123',
          'subject' => 'Delivery Status Notification (Delay)',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => '421 4.2.1 Mailbox temporarily unavailable. Try again later.'
          }
        }
      end

      subject { OutlookBounceDetector.call(soft_bounce_message, connected_account) }

      it 'detects as soft bounce' do
        expect(subject[:bounce_type]).to eq('soft')
      end
    end
  end

  describe '#bounce_by_sender_and_subject?' do
    let(:detector) { OutlookBounceDetector.new(outlook_bounce_message, connected_account) }

    it 'detects bounce by subject patterns' do
      expect(detector.send(:bounce_by_subject?)).to be true
    end
  end

  describe '#determine_bounce_type' do
    let(:detector) { OutlookBounceDetector.new(outlook_bounce_message, connected_account) }

    it 'correctly identifies hard bounces' do
      expect(detector.send(:determine_bounce_type)).to eq('hard')
    end

    context 'with soft bounce patterns' do
      let(:soft_bounce_message) do
        {
          'subject' => 'Delivery Status Notification (Delay)',
          'body' => {
            'content' => '450 Requested action not taken: mailbox unavailable'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(soft_bounce_message, connected_account) }

      it 'correctly identifies soft bounces' do
        expect(detector.send(:determine_bounce_type)).to eq('soft')
      end
    end
  end

  describe '#extract_failed_reason' do
    let(:detector) { OutlookBounceDetector.new(outlook_bounce_message, connected_account) }

    it 'extracts the failure reason' do
      reason = detector.send(:extract_failed_reason)
      expect(reason).to include('550')
    end
  end
end
