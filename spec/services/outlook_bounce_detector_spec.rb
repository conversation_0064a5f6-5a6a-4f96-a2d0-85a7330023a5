# frozen_string_literal: true

require 'rails_helper'

RSpec.describe OutlookBounceDetector, type: :service do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:connected_account) { create(:connected_account, user: user, tenant_id: tenant.id) }

  let(:outlook_bounce_message) do
    JSON.parse(File.read(Rails.root.join('spec/fixtures/files/outlook_delivery_status_email.json')))
  end

  let(:outlook_raw_mime) do
    File.read(Rails.root.join('spec/fixtures/files/outlook_delivery_status_raw_mime.txt'))
  end

  let(:regular_outlook_message) do
    {
      'id' => 'regular123',
      'subject' => 'Regular email',
      'from' => {
        'emailAddress' => {
          'address' => '<EMAIL>'
        }
      },
      'body' => {
        'content' => 'This is a regular email message.'
      }
    }
  end

  # Mock the GraphApiClient methods
  before do
    allow_any_instance_of(OutlookBounceDetector).to receive(:get_raw_mime_message).and_return(outlook_raw_mime)
  end

  describe '.call' do
    context 'with an Outlook bounce message' do
      subject { OutlookBounceDetector.call(outlook_bounce_message, connected_account) }

      it 'detects the message as a bounce' do
        expect(subject[:is_bounced]).to be true
      end

      it 'determines the correct bounce type' do
        expect(subject[:bounce_type]).to eq('hard')
      end

      it 'extracts the failed reason' do
        expect(subject[:failed_reason]).to include('Action: failed')
      end

      it 'extracts the original message ID from headers' do
        expect(subject[:original_message_id]).to include('PN2P287MB1310')
      end
    end

    context 'with a regular Outlook message' do
      subject { OutlookBounceDetector.call(regular_outlook_message, connected_account) }

      it 'does not detect the message as a bounce' do
        expect(subject[:is_bounced]).to be false
      end
    end

    context 'with Microsoft Exchange sender bounce' do
      let(:microsoft_exchange_bounce) do
        {
          'id' => 'exchange123',
          'subject' => 'Undeliverable: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'Your message could not be delivered.'
          }
        }
      end

      before do
        allow_any_instance_of(OutlookBounceDetector).to receive(:get_raw_mime_message).and_return(outlook_raw_mime)
      end

      subject { OutlookBounceDetector.call(microsoft_exchange_bounce, connected_account) }

      it 'detects as bounce from Microsoft Exchange' do
        expect(subject[:is_bounced]).to be true
      end
    end

    context 'with postmaster sender bounce' do
      let(:postmaster_bounce) do
        {
          'id' => 'postmaster123',
          'subject' => 'Undeliverable: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'Mail delivery failed.'
          }
        }
      end

      before do
        allow_any_instance_of(OutlookBounceDetector).to receive(:get_raw_mime_message).and_return(outlook_raw_mime)
      end

      subject { OutlookBounceDetector.call(postmaster_bounce, connected_account) }

      it 'detects as bounce from postmaster' do
        expect(subject[:is_bounced]).to be true
      end
    end

    context 'with soft bounce indicators' do
      let(:soft_bounce_message) do
        {
          'id' => 'soft123',
          'subject' => 'Undeliverable: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => '421 4.2.1 Mailbox temporarily unavailable. Try again later.'
          }
        }
      end

      before do
        soft_bounce_mime = <<~MIME
          From: <EMAIL>
          To: <EMAIL>
          Subject: Undeliverable: Test Message
          Content-Type: multipart/report; report-type=delivery-status; boundary="boundary123"

          --boundary123
          Content-Type: text/plain

          This is a delivery status notification.

          --boundary123
          Content-Type: message/delivery-status

          Action: delayed
          Status: 4.2.1
          Diagnostic-Code: smtp; 421 4.2.1 Mailbox temporarily unavailable. Try again later.

          --boundary123--
        MIME
        allow_any_instance_of(OutlookBounceDetector).to receive(:get_raw_mime_message).and_return(soft_bounce_mime)
      end

      subject { OutlookBounceDetector.call(soft_bounce_message, connected_account) }

      it 'detects as soft bounce' do
        # Debug the result
        puts "Result: #{subject.inspect}"
        expect(subject[:is_bounced]).to be true
        expect(subject[:bounce_type]).to eq('soft')
      end
    end

    context 'when raw MIME verification fails' do
      let(:bounce_message_no_dsn) do
        {
          'id' => 'no_dsn123',
          'subject' => 'Undeliverable: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'Your message could not be delivered.'
          }
        }
      end

      before do
        # Mock raw MIME without delivery-status part
        raw_mime_no_dsn = "From: <EMAIL>\nTo: <EMAIL>\nSubject: Test\n\nTest body"
        allow_any_instance_of(OutlookBounceDetector).to receive(:get_raw_mime_message).and_return(raw_mime_no_dsn)
      end

      subject { OutlookBounceDetector.call(bounce_message_no_dsn, connected_account) }

      it 'returns false when DSN verification fails' do
        expect(subject[:is_bounced]).to be false
      end
    end

    context 'when message has no bounce indicators' do
      let(:non_bounce_message) do
        {
          'id' => 'normal123',
          'subject' => 'Regular email subject',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'This is a normal email message with no bounce indicators.'
          }
        }
      end

      subject { OutlookBounceDetector.call(non_bounce_message, connected_account) }

      it 'does not detect as bounce' do
        expect(subject[:is_bounced]).to be false
      end
    end
  end

  describe '#bounce_by_sender_and_subject?' do
    context 'with Microsoft Exchange sender' do
      let(:microsoft_exchange_message) do
        {
          'subject' => 'Undeliverable: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'Your message could not be delivered.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(microsoft_exchange_message, connected_account) }

      it 'detects bounce from Microsoft Exchange' do
        expect(detector.send(:bounce_by_sender_and_subject?)).to be true
      end
    end

    context 'with Microsoft Exchange sender but excluded subject' do
      let(:microsoft_exchange_delivered) do
        {
          'subject' => 'Delivered: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'Your message was delivered.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(microsoft_exchange_delivered, connected_account) }

      it 'does not detect as bounce for excluded subjects' do
        expect(detector.send(:bounce_by_sender_and_subject?)).to be false
      end
    end

    context 'with postmaster sender' do
      let(:postmaster_message) do
        {
          'subject' => 'Undeliverable: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'Mail delivery failed.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(postmaster_message, connected_account) }

      it 'detects bounce from postmaster' do
        expect(detector.send(:bounce_by_sender_and_subject?)).to be true
      end
    end

    context 'with postmaster sender but excluded subject' do
      let(:postmaster_relayed) do
        {
          'subject' => 'Relayed: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'Message was relayed.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(postmaster_relayed, connected_account) }

      it 'does not detect as bounce for excluded subjects' do
        expect(detector.send(:bounce_by_sender_and_subject?)).to be false
      end
    end

    context 'with regular sender' do
      let(:regular_message) do
        {
          'subject' => 'Regular email',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'This is a regular email.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(regular_message, connected_account) }

      it 'does not detect as bounce from regular sender' do
        expect(detector.send(:bounce_by_sender_and_subject?)).to be_falsy
      end
    end

    context 'with nil sender' do
      let(:nil_sender_message) do
        {
          'subject' => 'Test Message',
          'from' => nil,
          'body' => {
            'content' => 'Test content.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(nil_sender_message, connected_account) }

      it 'returns false for nil sender' do
        expect(detector.send(:bounce_by_sender_and_subject?)).to be false
      end
    end
  end

  describe '#bounce_by_content?' do
    context 'with hard bounce patterns in body' do
      let(:hard_bounce_content) do
        {
          'subject' => 'Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => '550 5.1.1 The email account that you tried to reach does not exist.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(hard_bounce_content, connected_account) }

      it 'detects hard bounce patterns' do
        expect(detector.send(:bounce_by_content?)).to be true
      end
    end

    context 'with soft bounce patterns in body' do
      let(:soft_bounce_content) do
        {
          'subject' => 'Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => '421 4.2.1 Mailbox temporarily unavailable. Try again later.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(soft_bounce_content, connected_account) }

      it 'detects soft bounce patterns' do
        expect(detector.send(:bounce_by_content?)).to be true
      end
    end

    context 'with no bounce patterns' do
      let(:normal_content) do
        {
          'subject' => 'Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'This is a normal email message with no bounce indicators.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(normal_content, connected_account) }

      it 'does not detect bounce patterns' do
        expect(detector.send(:bounce_by_content?)).to be false
      end
    end

    context 'with nil body' do
      let(:nil_body_message) do
        {
          'subject' => 'Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => nil
        }
      end
      let(:detector) { OutlookBounceDetector.new(nil_body_message, connected_account) }

      it 'returns false for nil body' do
        expect(detector.send(:bounce_by_content?)).to be false
      end
    end
  end

  describe '#determine_bounce_type' do
    context 'with DSN body containing hard bounce patterns' do
      let(:detector) { OutlookBounceDetector.new(outlook_bounce_message, connected_account) }

      before do
        detector.instance_variable_set(:@dsn_body, '550-5.1.1 The email account that you tried to reach does not exist.')
      end

      it 'correctly identifies hard bounces from DSN body' do
        expect(detector.send(:determine_bounce_type)).to eq('hard')
      end
    end

    context 'with DSN body containing soft bounce patterns' do
      let(:detector) { OutlookBounceDetector.new(outlook_bounce_message, connected_account) }

      before do
        detector.instance_variable_set(:@dsn_body, '421 4.2.1 Mailbox temporarily unavailable.')
      end

      it 'correctly identifies soft bounces from DSN body' do
        expect(detector.send(:determine_bounce_type)).to eq('soft')
      end
    end

    context 'with regular body containing hard bounce patterns' do
      let(:hard_bounce_message) do
        {
          'subject' => 'Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => '550 5.1.1 The email account that you tried to reach does not exist.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(hard_bounce_message, connected_account) }

      it 'correctly identifies hard bounces from body' do
        expect(detector.send(:determine_bounce_type)).to eq('hard')
      end
    end

    context 'with regular body containing soft bounce patterns' do
      let(:soft_bounce_message) do
        {
          'subject' => 'Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => '421 4.2.1 Mailbox temporarily unavailable. Try again later.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(soft_bounce_message, connected_account) }

      it 'correctly identifies soft bounces from body' do
        expect(detector.send(:determine_bounce_type)).to eq('soft')
      end
    end

    context 'with no bounce patterns' do
      let(:normal_message) do
        {
          'subject' => 'Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'This is a normal email message.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(normal_message, connected_account) }

      it 'defaults to hard bounce when no patterns match' do
        expect(detector.send(:determine_bounce_type)).to eq('hard')
      end
    end
  end

  describe '#extract_failed_reason' do
    context 'with DSN body containing diagnostic code' do
      let(:detector) { OutlookBounceDetector.new(outlook_bounce_message, connected_account) }

      before do
        dsn_body = <<~DSN
          Diagnostic-Code: smtp; 550-5.1.1 The email account that you tried to reach does not exist.
          Action: failed
          Status: 5.1.1
        DSN
        detector.instance_variable_set(:@dsn_body, dsn_body)
      end

      it 'extracts diagnostic code from DSN body' do
        reason = detector.send(:extract_failed_reason)
        expect(reason).to eq('smtp; 550-5.1.1 The email account that you tried to reach does not exist.')
      end
    end

    context 'with DSN body containing action' do
      let(:detector) { OutlookBounceDetector.new(outlook_bounce_message, connected_account) }

      before do
        dsn_body = <<~DSN
          Action: failed
          Status: 5.1.1
        DSN
        detector.instance_variable_set(:@dsn_body, dsn_body)
      end

      it 'extracts action from DSN body' do
        reason = detector.send(:extract_failed_reason)
        expect(reason).to eq('Action: failed')
      end
    end

    context 'with DSN body containing status' do
      let(:detector) { OutlookBounceDetector.new(outlook_bounce_message, connected_account) }

      before do
        dsn_body = <<~DSN
          Status: 5.1.1
        DSN
        detector.instance_variable_set(:@dsn_body, dsn_body)
      end

      it 'extracts status from DSN body' do
        reason = detector.send(:extract_failed_reason)
        expect(reason).to eq('Status: 5.1.1')
      end
    end

    context 'with regular body containing bounce patterns' do
      let(:bounce_message) do
        {
          'subject' => 'Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => "Line 1\n550-5.1.1 The email account that you tried to reach does not exist.\nLine 3"
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(bounce_message, connected_account) }

      it 'extracts bounce pattern line from body' do
        reason = detector.send(:extract_failed_reason)
        expect(reason).to eq('550-5.1.1 The email account that you tried to reach does not exist.')
      end
    end

    context 'with no bounce patterns' do
      let(:normal_message) do
        {
          'subject' => 'Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'This is a normal email message.'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(normal_message, connected_account) }

      it 'returns nil when no patterns match' do
        reason = detector.send(:extract_failed_reason)
        expect(reason).to be_nil
      end
    end
  end

  describe '#extract_body' do
    context 'with valid body content' do
      let(:message_with_body) do
        {
          'body' => {
            'content' => 'Test email body content'
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(message_with_body, connected_account) }

      it 'extracts body content' do
        expect(detector.send(:extract_body)).to eq('Test email body content')
      end
    end

    context 'with nil body' do
      let(:message_with_nil_body) do
        {
          'body' => nil
        }
      end
      let(:detector) { OutlookBounceDetector.new(message_with_nil_body, connected_account) }

      it 'returns empty string for nil body' do
        expect(detector.send(:extract_body)).to eq('')
      end
    end

    context 'with missing body key' do
      let(:message_without_body) { {} }
      let(:detector) { OutlookBounceDetector.new(message_without_body, connected_account) }

      it 'returns empty string for missing body' do
        expect(detector.send(:extract_body)).to eq('')
      end
    end
  end

  describe '#extract_from' do
    context 'with valid from address' do
      let(:message_with_from) do
        {
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          }
        }
      end
      let(:detector) { OutlookBounceDetector.new(message_with_from, connected_account) }

      it 'extracts from address' do
        expect(detector.send(:extract_from)).to eq('<EMAIL>')
      end
    end

    context 'with nil from' do
      let(:message_with_nil_from) do
        {
          'from' => nil
        }
      end
      let(:detector) { OutlookBounceDetector.new(message_with_nil_from, connected_account) }

      it 'returns nil for nil from' do
        expect(detector.send(:extract_from)).to be_nil
      end
    end

    context 'with missing from key' do
      let(:message_without_from) { {} }
      let(:detector) { OutlookBounceDetector.new(message_without_from, connected_account) }

      it 'returns nil for missing from' do
        expect(detector.send(:extract_from)).to be_nil
      end
    end
  end

  describe '#extract_subject' do
    context 'with valid subject' do
      let(:message_with_subject) do
        {
          'subject' => 'Test Subject'
        }
      end
      let(:detector) { OutlookBounceDetector.new(message_with_subject, connected_account) }

      it 'extracts subject' do
        expect(detector.send(:extract_subject)).to eq('Test Subject')
      end
    end

    context 'with nil subject' do
      let(:message_with_nil_subject) do
        {
          'subject' => nil
        }
      end
      let(:detector) { OutlookBounceDetector.new(message_with_nil_subject, connected_account) }

      it 'returns empty string for nil subject' do
        expect(detector.send(:extract_subject)).to eq('')
      end
    end

    context 'with missing subject key' do
      let(:message_without_subject) { {} }
      let(:detector) { OutlookBounceDetector.new(message_without_subject, connected_account) }

      it 'returns empty string for missing subject' do
        expect(detector.send(:extract_subject)).to eq('')
      end
    end
  end

  describe '#update_body_and_headers_from_mime_message' do
    let(:detector) { OutlookBounceDetector.new(outlook_bounce_message, connected_account) }

    context 'with valid MIME message' do
      it 'parses headers from MIME message' do
        detector.send(:update_body_and_headers_from_mime_message, outlook_raw_mime)
        headers = detector.instance_variable_get(:@headers)

        expect(headers['From']).to include('Microsoft Outlook')
        expect(headers['Subject']).to eq('Undeliverable: attack on titan')
        expect(headers['In-Reply-To']).to include('PN2P287MB1310')
      end

      it 'extracts DSN body from multipart message' do
        detector.send(:update_body_and_headers_from_mime_message, outlook_raw_mime)
        dsn_body = detector.instance_variable_get(:@dsn_body)

        expect(dsn_body).to include('550-5.1.1')
        expect(dsn_body).to include('The email account that you tried to reach does not exist')
      end
    end

    context 'with invalid MIME message' do
      let(:invalid_mime) { 'Invalid MIME content' }

      it 'handles parsing errors gracefully' do
        expect {
          detector.send(:update_body_and_headers_from_mime_message, invalid_mime)
        }.not_to raise_error
      end
    end
  end

  describe '#verify_bounce_with_raw_mime?' do
    let(:detector) { OutlookBounceDetector.new(outlook_bounce_message, connected_account) }

    context 'with valid bounce MIME message' do
      before do
        allow(detector).to receive(:get_raw_mime_message).and_return(outlook_raw_mime)
      end

      it 'returns true when DSN body contains bounce patterns' do
        expect(detector.send(:verify_bounce_with_raw_mime?)).to be true
      end
    end

    context 'with non-bounce MIME message' do
      let(:non_bounce_mime) do
        <<~MIME
          From: <EMAIL>
          To: <EMAIL>
          Subject: Regular email
          Content-Type: text/plain

          This is a regular email message.
        MIME
      end

      before do
        allow(detector).to receive(:get_raw_mime_message).and_return(non_bounce_mime)
      end

      it 'returns false when DSN body has no bounce patterns' do
        expect(detector.send(:verify_bounce_with_raw_mime?)).to be false
      end
    end
  end

  describe '#bounce_by_dsn_body?' do
    let(:detector) { OutlookBounceDetector.new(outlook_bounce_message, connected_account) }

    context 'with DSN body containing bounce patterns' do
      before do
        detector.instance_variable_set(:@dsn_body, '550-5.1.1 The email account that you tried to reach does not exist.')
      end

      it 'detects bounce patterns in DSN body' do
        expect(detector.send(:bounce_by_dsn_body?)).to be true
      end
    end

    context 'with DSN body containing soft bounce patterns' do
      before do
        detector.instance_variable_set(:@dsn_body, '421 4.2.1 Mailbox temporarily unavailable.')
      end

      it 'detects soft bounce patterns in DSN body' do
        expect(detector.send(:bounce_by_dsn_body?)).to be true
      end
    end

    context 'with DSN body containing no bounce patterns' do
      before do
        detector.instance_variable_set(:@dsn_body, 'This is a normal message.')
      end

      it 'does not detect bounce patterns' do
        expect(detector.send(:bounce_by_dsn_body?)).to be false
      end
    end

    context 'with nil DSN body' do
      before do
        detector.instance_variable_set(:@dsn_body, nil)
      end

      it 'returns false for nil DSN body' do
        expect(detector.send(:bounce_by_dsn_body?)).to be false
      end
    end
  end

  describe 'integration with GraphApiClient' do
    let(:detector) { OutlookBounceDetector.new(outlook_bounce_message, connected_account) }

    context 'when get_raw_mime_message is called' do
      before do
        allow(detector).to receive(:get_raw_mime_message).with(outlook_bounce_message['id']).and_return(outlook_raw_mime)
      end

      it 'calls get_raw_mime_message with correct message ID' do
        detector.send(:verify_bounce_with_raw_mime?)
        expect(detector).to have_received(:get_raw_mime_message).with(outlook_bounce_message['id'])
      end
    end

    context 'when GraphApiClient raises an error' do
      before do
        allow(detector).to receive(:get_raw_mime_message).and_raise(StandardError.new('API Error'))
      end

      it 'handles API errors gracefully' do
        expect {
          detector.send(:verify_bounce_with_raw_mime?)
        }.to raise_error(StandardError, 'API Error')
      end
    end
  end

  describe 'edge cases and error handling' do
    context 'with malformed message data' do
      let(:malformed_message) { 'not a hash' }

      it 'handles non-hash message data' do
        expect {
          OutlookBounceDetector.new(malformed_message, connected_account)
        }.not_to raise_error
      end
    end

    context 'with empty message data' do
      let(:empty_message) { {} }
      let(:detector) { OutlookBounceDetector.new(empty_message, connected_account) }

      it 'handles empty message data' do
        expect(detector.send(:extract_subject)).to eq('')
        expect(detector.send(:extract_from)).to be_nil
        expect(detector.send(:extract_body)).to eq('')
      end
    end

    context 'with nil connected_account' do
      it 'handles nil connected_account' do
        expect {
          OutlookBounceDetector.new(outlook_bounce_message, nil)
        }.not_to raise_error
      end
    end
  end
end
