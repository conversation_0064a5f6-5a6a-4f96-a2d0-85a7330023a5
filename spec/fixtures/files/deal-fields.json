[{"id": 530, "type": "TEXT_FIELD", "name": "name", "displayName": "Name", "description": null, "sortable": true, "filterable": true, "required": true, "active": true, "standard": true, "min": 3, "max": 255, "picklist": null}, {"id": 531, "type": "FORECASTING_TYPE", "name": "forecastingType", "displayName": "ForecastingType", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 532, "type": "LOOK_UP", "name": "ownedBy", "displayName": "Owner", "description": null, "sortable": false, "filterable": true, "required": true, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 533, "type": "MONEY", "name": "estimatedValue", "displayName": "Estimated Value", "description": null, "sortable": true, "filterable": true, "required": true, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 534, "type": "MONEY", "name": "actualValue", "displayName": "Actual Value", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 535, "type": "DATE_PICKER", "name": "estimatedClosureOn", "displayName": "Estimated Closure Date", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 536, "type": "LOOK_UP", "name": "product", "displayName": "Product or Service", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 537, "type": "LOOK_UP", "name": "company", "displayName": "Company", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 538, "type": "PIPELINE", "name": "pipeline", "displayName": "Pipeline", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 539, "type": "PIPELINE_STAGE", "name": "pipelineStage", "displayName": "Pipeline Stage", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 540, "type": "LOOK_UP", "name": "associatedContacts", "displayName": "Contacts", "description": null, "sortable": false, "filterable": true, "required": true, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 541, "type": "LOOK_UP", "name": "created<PERSON>y", "displayName": "Created By", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 542, "type": "DATETIME_PICKER", "name": "createdAt", "displayName": "Created At", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 543, "type": "LOOK_UP", "name": "updatedBy", "displayName": "Updated By", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 544, "type": "DATETIME_PICKER", "name": "updatedAt", "displayName": "Updated At", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 545, "type": "DATE_PICKER", "name": "actualClosureDate", "displayName": "Actual Closure Date", "description": null, "sortable": true, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 546, "type": "TEXT_FIELD", "name": "pipelineStageReason", "displayName": "Pipeline Stage Reason", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": 3, "max": 255, "picklist": null}, {"id": 547, "type": "DATETIME_PICKER", "name": "taskDueOn", "displayName": "Task Due On", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 548, "type": "DATETIME_PICKER", "name": "meetingScheduledOn", "displayName": "Meeting Scheduled On", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 549, "type": "DATETIME_PICKER", "name": "latestActivityCreatedAt", "displayName": "Latest Activity On", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 550, "type": "TOGGLE", "name": "isNew", "displayName": "Is New", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": null}, {"id": 551, "type": "PICK_LIST", "name": "campaign", "displayName": "Campaign", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": {"id": 47, "name": "CAMPAIGN", "picklistValues": [{"id": 1766, "name": "TEST", "displayName": "test", "disabled": false}, {"id": 1765, "name": "NEW", "displayName": "new", "disabled": false}, {"id": 139, "name": "ORGANIC", "displayName": "Organic", "disabled": false}]}}, {"id": 552, "type": "PICK_LIST", "name": "source", "displayName": "Source", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "picklist": {"id": 48, "name": "SOURCE", "picklistValues": [{"id": 4261, "name": "LINKEDIN", "displayName": "LinkedIn", "disabled": false}, {"id": 4262, "name": "GOOGLE", "displayName": "Google", "disabled": false}, {"id": 4263, "name": "EXHIBITION", "displayName": "Exhibition", "disabled": false}, {"id": 4264, "name": "MICROSOFT", "displayName": "Microsoft", "disabled": false}, {"id": 4265, "name": "COLD CALLING", "displayName": "Cold Calling", "disabled": false}, {"id": 4266, "name": "FACEBOOK", "displayName": "Facebook", "disabled": false}, {"id": 4242, "name": "TWITTER", "displayName": "Twitter", "disabled": false}, {"id": 140, "name": "GOOGLE", "displayName": "Google", "disabled": false}, {"id": 143, "name": "EXHIBITION", "displayName": "Exhibition", "disabled": false}, {"id": 141, "name": "FACEBOOK", "displayName": "Facebook", "disabled": false}, {"id": 144, "name": "COLD_CALLING", "displayName": "Cold Calling", "disabled": false}, {"id": 142, "name": "LINKEDIN", "displayName": "LinkedIn", "disabled": false}]}}, {"id": 19417, "type": "TEXT_FIELD", "name": "text", "displayName": "Text", "description": null, "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": 3, "max": 255, "picklist": null}, {"id": 19418, "type": "PARAGRAPH_TEXT", "name": "picklist", "displayName": "Paragraph", "description": null, "sortable": false, "filterable": false, "required": false, "active": true, "standard": false, "min": 3, "max": 2550, "picklist": null}, {"id": 19419, "type": "PICK_LIST", "name": "picklistC", "displayName": "Picklist C", "description": null, "sortable": false, "filterable": false, "required": false, "active": false, "standard": false, "min": null, "max": null, "picklist": {"id": 1701, "name": "PICKLIST_C", "picklistValues": [{"id": 5208, "name": "1", "displayName": "1", "disabled": false}, {"id": 5209, "name": "2", "displayName": "2", "disabled": false}, {"id": 5210, "name": "3", "displayName": "3", "disabled": false}, {"id": 5211, "name": "4", "displayName": "4", "disabled": false}, {"id": 5212, "name": "5", "displayName": "5", "disabled": false}]}}, {"id": 140470, "type": "MULTI_PICKLIST", "name": "cfMultiPicklist", "displayName": "Deal Multi Value Picklist", "description": null, "sortable": false, "filterable": false, "required": false, "active": true, "standard": false, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": {"id": 4415, "name": "NEW_MULTIVAL", "picklistValues": [{"id": 13684, "name": "VAL123", "displayName": "val123", "disabled": false}, {"id": 13685, "name": "VAL456", "displayName": "val456", "disabled": false}, {"id": 13726, "name": "VAL789", "displayName": "val789", "disabled": true}]}}, {"id": 160226, "type": "PICK_LIST", "name": "units", "displayName": "Unit", "description": null, "sortable": false, "filterable": true, "required": false, "active": true, "standard": true, "min": null, "max": null, "internal": false, "width": null, "systemRequired": false, "picklist": null}]