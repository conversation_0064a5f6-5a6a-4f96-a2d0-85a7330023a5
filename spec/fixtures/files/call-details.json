{"id": 39494, "outcome": "connected", "startTime": "2022-10-10T05:13:13.000Z", "phoneNumber": "+919527890092", "callType": "outgoing", "duration": 23, "isManual": false, "originator": "+18053211523", "receiver": "+919527890092", "createdBy": {"id": 4167, "name": " <PERSON><PERSON><PERSON>", "phoneNumber": "+919082408054"}, "updatedBy": {"id": 4167, "name": " <PERSON><PERSON><PERSON>", "phoneNumber": "+919082408054"}, "owner": {"id": 6781, "name": "New aircall User", "phoneNumber": "+918527963314"}, "relatedTo": [{"id": 311575, "entity": "lead", "name": "09527890092", "phoneNumber": "+919527890092"}, {"id": 25605, "entity": "deal", "name": "<PERSON> Durham", "phoneNumber": "7029144803"}, {"id": 112885, "entity": "contact", "name": "<PERSON>", "phoneNumber": "7029144803"}], "associatedTo": [], "callRecording": {"id": 24044, "fileName": "+919527890092.mp3", "fileSize": 0, "url": "https://qa-call-recording.sgp1.digitaloceanspaces.com/tenant_2174/user_4010/call_recordings/39494_%2B919527890092_1665378851.mp3?response-content-disposition=attachment%3B%20filename%3D%2B919527890092.mp3&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=Q3HPYXCYNPT55Y6NZF2M%2F20221010%2Fsgp1%2Fs3%2Faws4_request&X-Amz-Date=20221010T084826Z&X-Amz-Expires=300&X-Amz-SignedHeaders=host&X-Amz-Signature=9083ccd69e412b04819a4e402f00030e7703c5b7f5bdde1eb2c011696e7d2bde"}, "createdAt": "2022-10-10T05:13:26.412Z", "updatedAt": "2022-10-10T05:13:39.076Z", "tenantId": 2174, "deviceId": "Aircall", "recordActions": {"read": true, "update": true}, "notes": [{"id": 12072, "description": "demo notesss", "createdBy": {"id": 4010, "name": " <PERSON><PERSON><PERSON>", "phoneNumber": "+919082408054"}, "createdAt": "2022-10-10T05:13:39.644Z"}], "callbackDuration": null, "customFieldValues": {}}