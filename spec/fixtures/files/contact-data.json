{"createdAt": "2022-03-28T10:30:41.901+0000", "updatedAt": "2022-04-06T07:19:20.074+0000", "createdBy": 11, "updatedBy": 11, "importedBy": 11, "recordActions": {"read": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "deleteAll": true}, "metaData": {"idNameStore": {"country": {}, "updatedBy": {"11": "Miss <PERSON><PERSON><PERSON><PERSON> k"}, "createdBy": {"11": "Miss <PERSON><PERSON><PERSON><PERSON> k"}, "timezone": {}, "company": {"6463": "Test Company"}, "cfCustomMultiPicklist": {"551953": "$5*", "551954": "%@#", "551951": "abc", "551952": "123"}, "salutation": {"816": "Mr"}, "ownerId": {"11": "Miss <PERSON><PERSON><PERSON><PERSON> k"}, "importedBy": {"11": "Miss <PERSON><PERSON><PERSON><PERSON> k"}}}, "id": 11320, "ownerId": 11, "salutation": 816, "firstName": "SK", "lastName": "Company", "phoneNumbers": [{"type": "MOBILE", "code": "IN", "value": "9999999901", "dialCode": "+91", "primary": true}, {"type": "WORK", "code": "IN", "value": "9999999912", "dialCode": "+91", "primary": false}, {"type": "PERSONAL", "code": "IN", "value": "7777777777", "dialCode": "+91", "primary": false}], "emails": [{"type": "OFFICE", "value": "<EMAIL>", "primary": true}], "customFieldValues": {"textCustom": "Text Custom", "cfCustomMultiPicklist": [551954, 551952, 551953, 551951]}, "dnd": true, "timezone": "US/Alaska", "address": "Address Line 1", "city": "Address City", "state": "Address State", "zipcode": "347378", "country": "DZ", "facebook": "https://www.facebook.com", "twitter": "https://www.twitter.com", "linkedin": "http://shw1.com", "company": 6463, "department": "Marketing", "designation": "Marketing", "stakeholder": true}