[{"createdAt": "2022-02-01T14:32:26.957+0000", "updatedAt": "2022-02-01T14:32:29.700+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135844, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Task Name", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "name", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": true, "important": false, "active": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2022-02-01T14:32:26.963+0000", "updatedAt": "2022-02-01T14:32:29.700+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135845, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Description", "description": null, "type": "PARAGRAPH_TEXT", "internalType": null, "name": "description", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": 2550, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2022-02-01T14:32:26.977+0000", "updatedAt": "2022-02-01T14:32:29.700+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135846, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Type", "description": null, "type": "PICK_LIST", "internalType": null, "name": "type", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2022-02-01T14:32:26.979+0000", "updatedAt": "2022-02-01T14:32:26.979+0000", "createdBy": 4010, "updatedBy": 4010, "id": 9958, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "", "displayName": null, "global": false, "systemDefault": false, "values": [{"createdAt": "2022-02-01T14:32:26.982+0000", "updatedAt": "2022-02-01T14:32:26.982+0000", "createdBy": 4010, "updatedBy": 4010, "id": 38252, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "CALL", "displayName": "Call", "disabled": false}, {"createdAt": "2022-02-01T14:32:26.986+0000", "updatedAt": "2022-02-01T14:32:26.986+0000", "createdBy": 4010, "updatedBy": 4010, "id": 38253, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "FOLLOW_UP", "displayName": "Follow Up", "disabled": false}, {"createdAt": "2022-02-01T14:32:26.997+0000", "updatedAt": "2022-02-01T14:32:26.997+0000", "createdBy": 4010, "updatedBy": 4010, "id": 38254, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "REMINDER", "displayName": "Reminder", "disabled": false}, {"createdAt": "2022-02-01T14:32:27.000+0000", "updatedAt": "2022-02-01T14:32:27.000+0000", "createdBy": 4010, "updatedBy": 4010, "id": 38255, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "TODO", "displayName": "Todo", "disabled": false}]}, "regex": null}, {"createdAt": "2022-02-01T14:32:27.013+0000", "updatedAt": "2022-02-01T14:32:29.700+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135847, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Due Date", "description": null, "type": "DATETIME_PICKER", "internalType": null, "name": "dueDate", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2022-02-01T14:32:27.017+0000", "updatedAt": "2022-02-01T14:32:29.701+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135848, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Status", "description": null, "type": "PICK_LIST", "internalType": null, "name": "status", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": true, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2022-02-01T14:32:27.017+0000", "updatedAt": "2022-02-01T14:32:27.017+0000", "createdBy": 4010, "updatedBy": 4010, "id": 9959, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "", "displayName": null, "global": false, "systemDefault": false, "values": [{"createdAt": "2022-02-01T14:32:27.020+0000", "updatedAt": "2022-02-01T14:32:27.020+0000", "createdBy": 4010, "updatedBy": 4010, "id": 38256, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "OPEN", "displayName": "Open", "disabled": false}, {"createdAt": "2022-02-01T14:32:27.025+0000", "updatedAt": "2022-02-01T14:32:27.025+0000", "createdBy": 4010, "updatedBy": 4010, "id": 38257, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "IN_PROGRESS", "displayName": "In Progress", "disabled": false}, {"createdAt": "2022-02-01T14:32:27.028+0000", "updatedAt": "2022-02-01T14:32:27.028+0000", "createdBy": 4010, "updatedBy": 4010, "id": 38258, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "COMPLETED", "displayName": "Completed", "disabled": false}, {"createdAt": "2022-08-18T11:51:46.701+0000", "updatedAt": "2022-08-18T11:51:46.701+0000", "createdBy": 4010, "updatedBy": 4010, "id": 374008, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "CANCELLED", "displayName": "Cancelled", "disabled": false}]}, "regex": null}, {"createdAt": "2022-02-01T14:32:27.035+0000", "updatedAt": "2022-02-01T14:32:29.701+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135849, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Priority", "description": null, "type": "PICK_LIST", "internalType": null, "name": "priority", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2022-02-01T14:32:27.035+0000", "updatedAt": "2022-02-01T14:32:27.035+0000", "createdBy": 4010, "updatedBy": 4010, "id": 9960, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "", "displayName": null, "global": false, "systemDefault": false, "values": [{"createdAt": "2022-02-01T14:32:27.038+0000", "updatedAt": "2022-02-01T14:32:27.038+0000", "createdBy": 4010, "updatedBy": 4010, "id": 38259, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "HIGH", "displayName": "High", "disabled": false}, {"createdAt": "2022-02-01T14:32:27.049+0000", "updatedAt": "2022-02-01T14:32:27.049+0000", "createdBy": 4010, "updatedBy": 4010, "id": 38260, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "MEDIUM", "displayName": "Medium", "disabled": false}, {"createdAt": "2022-02-01T14:32:27.054+0000", "updatedAt": "2022-02-01T14:32:27.054+0000", "createdBy": 4010, "updatedBy": 4010, "id": 38261, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "LOW", "displayName": "Low", "disabled": false}]}, "regex": null}, {"createdAt": "2022-02-01T14:32:27.063+0000", "updatedAt": "2022-02-01T14:32:29.701+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135850, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Assigned To", "description": null, "type": "LOOK_UP", "internalType": "java.math.BigDecimal", "name": "assignedTo", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": true, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": false, "lookupUrl": "/users/lookup?q=firstName:", "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2022-02-01T14:32:27.070+0000", "updatedAt": "2022-02-01T14:32:29.701+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135851, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Relation", "description": null, "type": "ENTITY_LOOKUP", "internalType": null, "name": "relation", "entityType": null, "standard": true, "sortable": false, "filterable": false, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2022-02-01T14:32:27.073+0000", "updatedAt": "2022-02-01T14:32:29.842+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135852, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Owner", "description": null, "type": "LOOK_UP", "internalType": "java.math.BigDecimal", "name": "ownerId", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": true, "lookupUrl": "/users/lookup?q=firstName:", "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2022-02-01T14:32:27.076+0000", "updatedAt": "2022-02-01T14:32:29.843+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135853, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Created At", "description": null, "type": "DATETIME_PICKER", "internalType": null, "name": "createdAt", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2022-02-01T14:32:27.083+0000", "updatedAt": "2022-02-01T14:32:29.843+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135854, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Updated At", "description": null, "type": "DATETIME_PICKER", "internalType": null, "name": "updatedAt", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2022-02-01T14:32:27.086+0000", "updatedAt": "2022-02-01T14:32:29.843+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135855, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Created By", "description": null, "type": "LOOK_UP", "internalType": "java.math.BigDecimal", "name": "created<PERSON>y", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": true, "lookupUrl": "/users/lookup?q=firstName:", "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2022-02-01T14:32:27.089+0000", "updatedAt": "2022-02-01T14:32:29.843+0000", "createdBy": 4010, "updatedBy": 4010, "id": 135856, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Updated By", "description": null, "type": "LOOK_UP", "internalType": "java.math.BigDecimal", "name": "updatedBy", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": true, "lookupUrl": "/users/lookup?q=firstName:", "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2022-04-01T14:18:52.020+0000", "updatedAt": "2022-04-01T14:18:52.020+0000", "createdBy": 2174, "updatedBy": 2174, "id": 180584, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Completed At", "description": null, "type": "DATETIME_PICKER", "internalType": null, "name": "completedAt", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-04-01T14:18:52.020+0000", "updatedAt": "2022-04-01T14:18:52.020+0000", "createdBy": 2174, "updatedBy": 2174, "id": 181895, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Original Due Date", "description": null, "type": "DATETIME_PICKER", "internalType": null, "name": "originalDueDate", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-06-24T08:32:46.504+0000", "updatedAt": "2022-06-24T08:32:46.504+0000", "createdBy": 2174, "updatedBy": 2174, "id": 438989, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Created Via Id", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "createdViaId", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-06-24T08:32:46.504+0000", "updatedAt": "2022-06-24T08:32:46.504+0000", "createdBy": 2174, "updatedBy": 2174, "id": 441386, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Created Via Name", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "createdViaName", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-06-24T08:32:46.504+0000", "updatedAt": "2022-06-24T08:32:46.504+0000", "createdBy": 2174, "updatedBy": 2174, "id": 443070, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Created Via Type", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "createdViaType", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-06-24T08:32:46.504+0000", "updatedAt": "2022-06-24T08:32:46.504+0000", "createdBy": 2174, "updatedBy": 2174, "id": 443636, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Updated Via Id", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "updatedViaId", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-06-24T08:32:46.504+0000", "updatedAt": "2022-06-24T08:32:46.504+0000", "createdBy": 2174, "updatedBy": 2174, "id": 445547, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Updated Via Name", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "updatedViaName", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-06-24T08:32:46.504+0000", "updatedAt": "2022-06-24T08:32:46.504+0000", "createdBy": 2174, "updatedBy": 2174, "id": 447878, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Updated Via Type", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "updatedViaType", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-08-25T05:08:58.006+0000", "updatedAt": "2022-08-25T05:08:58.006+0000", "createdBy": 2174, "updatedBy": 2174, "id": 524972, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Cancelled At", "description": null, "type": "DATETIME_PICKER", "internalType": null, "name": "cancelledAt", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2023-02-13T12:07:45.433+0000", "updatedAt": "2023-02-13T12:07:45.433+0000", "createdBy": 4010, "updatedBy": 4010, "id": 672430, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 2174, "displayName": "Reminder", "description": null, "type": "PICK_LIST", "internalType": null, "name": "reminder", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": false, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2023-02-13T12:07:45.433+0000", "updatedAt": "2023-02-13T12:07:45.433+0000", "createdBy": 2174, "updatedBy": 2174, "id": 73854, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "reminder", "displayName": "Reminder", "global": false, "systemDefault": true, "values": [{"createdAt": "2023-02-13T12:07:45.433+0000", "updatedAt": "2023-02-13T12:07:45.433+0000", "createdBy": 2174, "updatedBy": 2174, "id": 698274, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "NO_REMINDER", "displayName": "No reminder", "disabled": false}, {"createdAt": "2023-02-13T12:07:45.433+0000", "updatedAt": "2023-02-13T12:07:45.433+0000", "createdBy": 2174, "updatedBy": 2174, "id": 700544, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "FIFTEEN_MINUTES", "displayName": "15 minutes before the due date and time", "disabled": false}, {"createdAt": "2023-02-13T12:07:45.433+0000", "updatedAt": "2023-02-13T12:07:45.433+0000", "createdBy": 2174, "updatedBy": 2174, "id": 702814, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "THIRTY_MINUTES", "displayName": "30 minutes before the due date and time", "disabled": false}, {"createdAt": "2023-02-13T12:07:45.433+0000", "updatedAt": "2023-02-13T12:07:45.433+0000", "createdBy": 2174, "updatedBy": 2174, "id": 705084, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "ONE_HOUR", "displayName": "1 hour before the due date and time", "disabled": false}, {"createdAt": "2023-02-13T12:07:45.433+0000", "updatedAt": "2023-02-13T12:07:45.433+0000", "createdBy": 2174, "updatedBy": 2174, "id": 707354, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "TWO_HOURS", "displayName": "2 hours before the due date and time", "disabled": false}, {"createdAt": "2023-02-13T12:07:45.433+0000", "updatedAt": "2023-02-13T12:07:45.433+0000", "createdBy": 2174, "updatedBy": 2174, "id": 709624, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 2174, "name": "ONE_DAY", "displayName": "1 day before the due date and time", "disabled": false}]}, "regex": null}]