{"createdAt": "2022-04-01T09:36:59.656+0000", "updatedAt": "2022-04-01T09:37:00.027+0000", "createdBy": 11, "updatedBy": 11, "importedBy": 11, "convertedBy": 11, "recordActions": {"read": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "deleteAll": true}, "metaData": {"idNameStore": {"country": {}, "convertedBy": {"11": "Miss <PERSON><PERSON><PERSON><PERSON> k"}, "cfTestMultiPlForLead": {"454425": "d", "454424": "c", "454422": "a"}, "updatedBy": {"11": "Miss <PERSON><PERSON><PERSON><PERSON> k"}, "multivalueField": {"36888": "test2"}, "timezone": {}, "picklist": {"4519": "test3"}, "requirementCurrency": {}, "vdfv": {"24649": "p        q"}, "source": {"800": "LinkedIn"}, "ownerId": {"3397": "Siby P"}, "companyEmployees": {"3061": "20-49"}, "companyBusinessType": {}, "importedBy": {"11": "Miss <PERSON><PERSON><PERSON><PERSON> k"}, "bVbBv": {}, "pipeline": {"7": "Default Lead Pipeline"}, "createdBy": {"11": "Miss <PERSON><PERSON><PERSON><PERSON> k"}, "companyCountry": {}, "campaign": {"9536": "new"}, "salutation": {"796": "Miss"}, "pipelineStage": {"7800": "Requirements Gathered"}, "companyIndustry": {}}}, "id": 217416, "ownerId": 3397, "firstName": "shweta", "lastName": "kylas", "phoneNumbers": [{"type": "MOBILE", "code": "IN", "value": "9999999999", "dialCode": "+91", "primary": true}, {"type": "MOBILE", "code": "IN", "value": "8888888888", "dialCode": "+91", "primary": false}], "salutation": 796, "emails": [{"type": "OFFICE", "value": "<EMAIL>", "primary": true}, {"type": "OFFICE", "value": "<EMAIL>", "primary": false}], "pipeline": {"id": 7, "name": "Default Lead Pipeline", "stage": {"id": 7800, "name": "Requirements Gathered"}}, "forecastingType": "OPEN", "timezone": "US/Central", "city": "Pune", "state": "Maharashtra", "zipcode": "411036", "country": "IN", "department": "computer", "dnd": true, "facebook": "https://facebook.com", "twitter": "https://twitter.com", "linkedIn": "https://linkedin.com", "address": "Keshavnagar", "companyName": "commpany name", "companyAddress": "baner", "companyCity": "pune", "companyState": "maharashtra", "companyCountry": "AQ", "companyEmployees": 3061, "companyAnnualRevenue": 12345.0, "companyWebsite": "https://twitter.com", "companyPhones": [{"type": "MOBILE", "code": "IN", "value": "4444444444", "dialCode": "+91", "primary": true}], "companyIndustry": "COMPUTER_SOFTWARE", "companyBusinessType": "competitor", "requirementName": "new requrirement", "requirementCurrency": "EUR", "requirementBudget": 11111.0, "products": [{"id": 1138, "name": "Test 14"}], "designation": "TL", "campaign": 9536, "source": 800, "customFieldValues": {"date": "2022-04-06T06:30:00.000Z", "multivalueField": 36888, "field": "ads", "subSource": "new subsouce", "picklist": 4519, "vdfv": 24649, "myUrl": "https://twitter.com", "age": 12, "testfield": "111", "aP": "first name custom", "cfTestMultiPlForLead": [454422, 454424, 454425]}}