[{"id": "user", "name": "user", "displayName": "User", "type": "LOOK_UP", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": true, "active": true, "lookup": {"entity": "USER", "lookupUrl": "/users/lookup?q=name:"}, "picklist": null}, {"id": "sentBy", "name": "sentBy", "displayName": "<PERSON><PERSON>", "type": "LOOK_UP", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": true, "active": true, "lookup": {"entity": "USER", "lookupUrl": "/users/lookup?q=name:"}, "picklist": null}, {"id": "receivedBy", "name": "receivedBy", "displayName": "Received By", "type": "LOOK_UP", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": true, "active": true, "lookup": {"entity": "USER", "lookupUrl": "/users/lookup?q=name:"}, "picklist": null}, {"id": "createdAt", "name": "createdAt", "displayName": "Mailed At", "type": "DATETIME_PICKER", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": true, "active": true, "lookup": null, "picklist": null}, {"id": "subject", "name": "subject", "displayName": "Subject", "type": "TEXT_FIELD", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": true, "active": true, "lookup": null, "picklist": null}, {"id": "associatedLeads", "name": "associatedLeads", "displayName": "Associated Leads", "type": "LOOK_UP", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": true, "active": true, "lookup": {"entity": "LEAD", "lookupUrl": "/search/lead/lookup?q=firstName:"}, "picklist": null}, {"id": "associatedDeals", "name": "associatedDeals", "displayName": "Associated Deals", "type": "LOOK_UP", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": true, "active": true, "lookup": {"entity": "DEAL", "lookupUrl": "/search/deal/lookup?q=name:"}, "picklist": null}, {"id": "associatedContacts", "name": "associatedContacts", "displayName": "Associated Contacts", "type": "LOOK_UP", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": true, "active": true, "lookup": {"entity": "CONTACT", "lookupUrl": "/search/contact/lookup?q=firstName:"}, "picklist": null}, {"id": "direction", "name": "direction", "displayName": "Email Type", "type": "PICK_LIST", "standard": true, "sortable": false, "filterable": true, "internal": true, "required": true, "active": true, "lookup": null, "picklist": {"id": 1, "displayName": "Email Type Picklist", "picklistValues": [{"id": 1, "displayName": "Received", "name": "received", "systemDefault": true}, {"id": 2, "displayName": "<PERSON><PERSON>", "name": "sent", "systemDefault": true}]}}, {"id": "status", "name": "status", "displayName": "Status", "type": "PICK_LIST", "standard": true, "sortable": false, "filterable": true, "internal": true, "required": true, "active": true, "lookup": null, "picklist": {"id": 1, "displayName": "Status Picklist", "picklistValues": [{"id": 1, "displayName": "Draft", "name": "draft", "systemDefault": true}, {"id": 2, "displayName": "<PERSON><PERSON>", "name": "sent", "systemDefault": true}, {"id": 3, "displayName": "Opened", "name": "opened", "systemDefault": true}, {"id": 4, "displayName": "Sending", "name": "sending", "systemDefault": true}, {"id": 5, "displayName": "Failed", "name": "failed", "systemDefault": true}, {"id": 6, "displayName": "Received", "name": "received", "systemDefault": true}]}}, {"id": "userFields", "name": "userFields", "displayName": "User Fields", "type": "ENTITY_FIELDS", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": true, "active": true, "lookup": {"entity": "TEAM", "lookupUrl": "/teams/lookup?q=name:"}, "picklist": null}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON>", "type": "ENTITY_FIELDS", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": true, "active": true, "lookup": {"entity": "TEAM", "lookupUrl": "/teams/lookup?q=name:"}, "picklist": null}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "Received By <PERSON>", "type": "ENTITY_FIELDS", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": true, "active": true, "lookup": {"entity": "TEAM", "lookupUrl": "/teams/lookup?q=name:"}, "picklist": null}, {"id": "attachments", "name": "attachments", "displayName": "Attachments", "type": "FILE_PICKER", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": false, "active": true, "lookup": null, "picklist": null}, {"id": "read", "name": "read", "displayName": "Read", "type": "TOGGLE", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": false, "active": true, "lookup": null, "picklist": null}, {"id": "openedAt", "name": "openedAt", "displayName": "Opened At", "type": "DATETIME_PICKER", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": false, "active": true, "lookup": null, "picklist": null}, {"id": "clickedAt", "name": "clickedAt", "displayName": "Clicked At", "type": "DATETIME_PICKER", "standard": true, "sortable": false, "filterable": true, "internal": false, "required": false, "active": true, "lookup": null, "picklist": null}, {"id": "relatedTo", "name": "relatedTo", "displayName": "Related To", "type": "ENTITY_LOOKUP", "standard": true, "sortable": false, "filterable": false, "internal": false, "required": false, "active": true, "lookup": null, "picklist": {"id": 1, "displayName": "Entity Picklist", "picklistValues": [{"id": 0, "displayName": "LEAD", "name": "LEAD", "lookupUrl": "/search/lead/lookup?converted=false&q=firstName:", "systemDefault": false, "disabled": false}, {"id": 1, "displayName": "DEAL", "name": "DEAL", "lookupUrl": "/deals/lookup?view=task&q=name:", "systemDefault": false, "disabled": false}, {"id": 2, "displayName": "CONTACT", "name": "CONTACT", "lookupUrl": "/search/contact/lookup?q=firstName:", "systemDefault": false, "disabled": false}]}}]