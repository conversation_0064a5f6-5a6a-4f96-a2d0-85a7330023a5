{"id": 39494, "outcome": "connected", "phoneNumber": "+919527890092", "callType": "outgoing", "duration": 23, "isManual": false, "originator": "+18053211523", "receiver": "+919527890092", "createdBy": " <PERSON><PERSON><PERSON>", "updatedBy": " <PERSON><PERSON><PERSON>", "owner": "New aircall User", "relatedTo": [{"id": 311575, "entity": "lead", "name": "09527890092", "phoneNumber": "+919527890092"}, {"id": 25605, "entity": "deal", "name": "<PERSON> Durham", "phoneNumber": "7029144803"}, {"id": 112885, "entity": "contact", "name": "<PERSON>", "phoneNumber": "7029144803"}], "associatedTo": [], "createdAt": "Oct 10,2022 at10:43 am IST", "updatedAt": "Oct 10,2022 at10:43 am IST", "deviceId": "Aircall", "callbackDuration": null, "customFieldValues": {}, "associatedLeads": "09527890092", "associatedContacts": "<PERSON>", "associatedDeals": "<PERSON> Durham", "callRecording": {"id": 24044, "fileName": "+919527890092.mp3", "fileSize": 0, "url": "https://qa-call-recording.sgp1.digitaloceanspaces.com/tenant_2174/user_4010/call_recordings/39494_%2B919527890092_1665378851.mp3?response-content-disposition=attachment%3B%20filename%3D%2B919527890092.mp3&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=Q3HPYXCYNPT55Y6NZF2M%2F20221010%2Fsgp1%2Fs3%2Faws4_request&X-Amz-Date=20221010T084826Z&X-Amz-Expires=300&X-Amz-SignedHeaders=host&X-Amz-Signature=9083ccd69e412b04819a4e402f00030e7703c5b7f5bdde1eb2c011696e7d2bde"}}