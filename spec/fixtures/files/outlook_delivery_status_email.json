{"@odata.context": "https://graph.microsoft.com/v1.0/$metadata#users('6c633ee5-72c4-4a0c-8888-d9c656ed2650')/messages/$entity", "@odata.etag": "W/\"FwAAABYAAABZY7vvQEw8RZzpjUYsAWzWAADSyrOp\"", "id": "AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AWWO770BMPEWc6Y1GLAFs1gAA04FOTgAA", "createdDateTime": "2025-08-26T06:14:59Z", "lastModifiedDateTime": "2025-08-26T06:15:00Z", "changeKey": "FwAAABYAAABZY7vvQEw8RZzpjUYsAWzWAADSyrOp", "categories": [], "receivedDateTime": "2025-08-26T06:14:59Z", "sentDateTime": "2025-08-26T06:14:58Z", "hasAttachments": false, "internetMessageId": "<<EMAIL>>", "subject": "Undeliverable: attack on titan", "bodyPreview": "Your <NAME_EMAIL> couldn't be delivered.\r\nsjkfjklsdjflkjlkjfdsljj wasn't found at gmail.com.\r\nkalpesh.vasave  Office 365      sjkfjklsdjflkjlkjfds. . .\r\nAction Required                 Recipient\r\nUnknown To address\r\n\r\nHow t", "importance": "normal", "parentFolderId": "AQMkAGU1OGQ1Y2I0LTUxMzMALTQ2ZDQtOTNkOS00ZTM1MjRhNmViZmMALgAAA37Se5exY2ZGpbLYTflmiXUBAFlju_9ATDxFnOmNRiwBbNYAAAIBDAAAAA==", "conversationId": "AAQkAGU1OGQ1Y2I0LTUxMzMtNDZkNC05M2Q5LTRlMzUyNGE2ZWJmYwAQAK0PYrj_euZGr0Q4yR4gBk0=", "conversationIndex": "AQHcFlC8rQ9iuP565kavRDjJHiAGTbR0dN2F", "isDeliveryReceiptRequested": null, "isReadReceiptRequested": false, "isRead": false, "isDraft": false, "webLink": "https://outlook.office365.com/owa/?ItemID=AAkALgAAAAAAHYQDEapmEc2byACqAC%2FEWg0AWWO770BMPEWc6Y1GLAFs1gAA04FOTgAA&exvsurl=1&viewmodel=ReadMessageItem", "inferenceClassification": "focused", "body": {"contentType": "html", "content": "<html><head>\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\"></head><body style=\"background-color:white\"><table width=\"548\" cellspacing=\"0\" cellpadding=\"0\" style=\"background-color:white; max-width:548px; color:black; border-spacing:0px 0px; padding-top:0px; padding-bottom:0px; border-collapse:collapse\"><tbody><tr><td style=\"text-align:left; padding-bottom:20px\"><img height=\"28\" width=\"126\" src=\"https://products.office.com/en-us/CMSImages/Office365Logo_Orange.png?version=b8d100a9-0a8b-8e6a-88e1-ef488fee0470\" style=\"max-width:100%\"> </td></tr><tr><td style=\"font-family:'Segoe UI',<PERSON><PERSON><PERSON>,<PERSON><PERSON>,sans-serif; font-size:16px; padding-bottom:10px; text-align:left\">Your message to <span style=\"color:#0072c6\"><EMAIL></span> couldn't be delivered.<br></td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:24px; padding-top:0px; padding-bottom:20px; text-align:center\"><span style=\"color:#0072c6\">sjkfjklsdjflkjlkjfdsljj</span> wasn't found at <span style=\"color:#0072c6\">gmail.com</span>.<br></td></tr><tr><td style=\"padding-bottom:15px; padding-left:0px; padding-right:0px; border-spacing:0px 0px\"><table style=\"max-width:548px; font-weight:600; border-spacing:0px 0px; padding-top:0px; padding-bottom:0px; border-collapse:collapse\"><tbody><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:15px; font-weight:600; text-align:left; width:181px; vertical-align:bottom\"><font color=\"#ffffff\"><span style=\"color:#000000\">kalpesh.vasave</span> </font></td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:15px; font-weight:600; text-align:center; width:186px; vertical-align:bottom\"><font color=\"#ffffff\"><span style=\"color:#000000\">Office 365</span> </font></td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:15px; font-weight:600; text-align:right; width:181px; vertical-align:bottom\"><font color=\"#ffffff\"><span style=\"color:#000000\">sjkfjklsdjflkjlkjfds. . .</span> </font></td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; font-weight:400; text-align:left; padding-top:0px; padding-bottom:0px; vertical-align:middle; width:181px\"><font color=\"#ffffff\"><span style=\"color:#c00000\"><b>Action Required</b> </span></font></td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; font-weight:400; text-align:center; padding-top:0px; padding-bottom:0px; vertical-align:middle; width:186px\"></td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; font-weight:400; text-align:right; padding-top:0px; padding-bottom:0px; vertical-align:middle; width:181px\"><font color=\"#ffffff\"><span style=\"color:#000000\">Recipient</span> </font></td></tr><tr><td colspan=\"3\" style=\"padding-top:0; padding-bottom:0; padding-left:0; padding-right:0\"><table cellspacing=\"0\" cellpadding=\"0\" style=\"border-spacing:0px 0px; padding-top:0px; padding-bottom:0px; padding-left:0px; padding-right:0px; border-collapse:collapse\"><tbody><tr height=\"10\"><td width=\"180\" height=\"10\" bgcolor=\"#c00000\" style=\"width:180px; line-height:10px; height:10px; font-size:6px; padding-top:0; padding-bottom:0; padding-left:0; padding-right:0\"></td><td width=\"4\" height=\"10\" bgcolor=\"#ffffff\" style=\"width:4px; line-height:10px; height:10px; font-size:6px; padding-top:0; padding-bottom:0; padding-left:0; padding-right:0\"></td><td width=\"180\" height=\"10\" bgcolor=\"#cccccc\" style=\"width:180px; line-height:10px; height:10px; font-size:6px; padding-top:0; padding-bottom:0; padding-left:0; padding-right:0\"></td><td width=\"4\" height=\"10\" bgcolor=\"#ffffff\" style=\"width:4px; line-height:10px; height:10px; font-size:6px; padding-top:0; padding-bottom:0; padding-left:0; padding-right:0\"></td><td width=\"180\" height=\"10\" bgcolor=\"#cccccc\" style=\"width:180px; line-height:10px; height:10px; font-size:6px; padding-top:0; padding-bottom:0; padding-left:0; padding-right:0\"></td></tr></tbody></table></td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; text-align:left; width:181px; line-height:20px; font-weight:400; padding-top:0px; padding-left:0px; padding-right:0px; padding-bottom:0px\"><font color=\"#ffffff\"><span style=\"color:#c00000\">Unknown To address</span> </font></td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; text-align:center; width:186px; line-height:20px; font-weight:400; padding-top:0px; padding-left:0px; padding-right:0px; padding-bottom:0px\"></td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; text-align:right; width:181px; line-height:20px; font-weight:400; padding-top:0px; padding-left:0px; padding-right:0px; padding-bottom:0px\"></td></tr></tbody></table></td></tr><tr><td style=\"width:100%; padding-top:0px; padding-right:10px; padding-left:10px\"><br><table style=\"width:100%; padding-right:0px; padding-left:0px; padding-top:0px; padding-bottom:0px; background-color:#f2f5fa; margin-left:0px\"><tbody><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:21px; font-weight:500; background-color:#f2f5fa; padding-top:0px; padding-bottom:0px; padding-left:10px; padding-right:10px\">How to Fix It</td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:16px; font-weight:400; padding-top:0px; padding-bottom:6px; padding-left:10px; padding-right:10px; background-color:#f2f5fa\">The address may be misspelled or may not exist. Try one or more of the following:</td></tr><tr><td style=\"padding-top:0px; padding-bottom:0px; padding-left:0px; padding-right:0px; border-spacing:0px 0px\"><ul style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:16px; font-weight:400; margin-left:40px; margin-bottom:5px; background-color:#f2f5fa; padding-top:0px; padding-bottom:0px; padding-left:6px; padding-right:6px\"><li>Send the message again following these steps: In Outlook, open this non-delivery report (NDR) and choose <b>Send Again</b> from the Report ribbon. In Outlook on the web, select this NDR, then select the link &quot;<b>To send this message again, click here.</b>&quot; Then delete and retype the entire recipient address. If prompted with an Auto-Complete List suggestion don't select it. After typing the complete address, click <b>Send</b>.</li><li>Contact the recipient (by phone, for example) to check that the address exists and is correct.</li><li>The recipient may have set up email forwarding to an incorrect address. Ask them to check that any forwarding they've set up is working correctly.</li><li>Clear the recipient Auto-Complete List in Outlook or Outlook on the web by following the steps in this article: <a href=\"https://go.microsoft.com/fwlink/?LinkId=389363\">Fix email delivery issues for error code 5.1.1 in Office 365</a>, and then send the message again. Retype the entire recipient address before selecting <b>Send</b>.</li></ul></td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:16px; font-weight:400; padding-top:0px; padding-bottom:6px; padding-left:10px; padding-right:10px; background-color:#f2f5fa\">If the problem continues, forward this message to your email admin. If you're an email admin, refer to the <b>More Info for Email Admins</b> section below.</td></tr></tbody></table></td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; font-weight:400; padding-top:10px; padding-bottom:0px; padding-bottom:4px\"><br><em>Was this helpful? <a href=\"https://go.microsoft.com/fwlink/?LinkId=525920\">Send feedback to Microsoft</a>.</em> </td></tr><tr><td style=\"font-size:0px; line-height:0px; padding-top:0px; padding-bottom:0px\"><hr></td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:21px; font-weight:500\"><br>More Info for Email Admins</td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px\"><em>Status code: 550 5.1.1</em> <br><br>This error occurs because the sender sent a message to an email address outside of Office 365, but the address is incorrect or doesn't exist at the destination domain. The error is reported by the recipient domain's email server, but most often it must be fixed by the person who sent the message. If the steps in the <b>How to Fix It</b> section above don't fix the problem, and you're the email admin for the recipient, try one or more of the following:<br><br><b>The email address exists and is correct</b> - Confirm that the recipient address exists, is correct, and is accepting messages.<br><br><b>Synchronize your directories</b> - If you have a hybrid environment and are using directory synchronization make sure the recipient's email address is synced correctly in both Office 365 and in your on-premises directory.<br><br><b>Errant forwarding rule</b> - Check for forwarding rules that aren't behaving as expected. Forwarding can be set up by an admin via mail flow rules or mailbox forwarding address settings, or by the recipient via the Inbox Rules feature.<br><br><b>Mail flow settings and MX records are not correct</b> - Misconfigured mail flow or MX record settings can cause this error. Check your Office 365 mail flow settings to make sure your domain and any mail flow connectors are set up correctly. Also, work with your domain registrar to make sure the MX records for your domain are configured correctly.<br><br>For more information and additional tips to fix this issue, see <a href=\"https://go.microsoft.com/fwlink/?LinkId=389363\">Fix email delivery issues for error code 550 5.1.1 in Office 365</a>.<br><br></td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:17px; font-weight:500\">Original Message Details</td></tr><tr><td style=\"font-size:14px; line-height:20px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500\"><table style=\"width:100%; border-collapse:collapse; margin-left:10px\"><tbody><tr><td valign=\"top\" style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; white-space:nowrap; font-weight:500; width:140px\">Created Date:</td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; font-weight:400\">26/08/2025 06:14:54</td></tr><tr><td valign=\"top\" style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; white-space:nowrap; font-weight:500; width:140px\">Sender Address:</td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; font-weight:400\"><EMAIL></td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; white-space:nowrap; font-weight:500; width:140px\">Recipient Address:</td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; font-weight:400\"><EMAIL></td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; white-space:nowrap; font-weight:500; width:140px\">Subject:</td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; font-weight:400\">attack on titan</td></tr></tbody></table></td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:17px; font-weight:500\"><br>Error Details</td></tr><tr><td style=\"font-size:14px; line-height:20px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500\"><table style=\"width:100%; border-collapse:collapse; margin-left:10px\"><tbody><tr><td valign=\"top\" style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; white-space:nowrap; font-weight:500; width:140px\">Error:</td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; font-weight:400\"><em>550-5.1.1 The email account that you tried to reach does not exist. Please try 550-5.1.1 double-checking the recipient's email address for typos or 550-5.1.1 unnecessary spaces. For more information, go to 550 5.1.1 https://support.google.com/mail/?p=NoSuchUser 3f1490d57ef6-e96d57c7011si1017092276.535 - gsmtp</em> </td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; white-space:nowrap; font-weight:500; width:140px\">Message rejected by:</td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; font-weight:400\">mx.google.com</td></tr></tbody></table></td></tr><tr><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:17px; font-weight:500\"><br>Notification Details</td></tr><tr><td style=\"font-size:14px; line-height:20px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500\"><table style=\"width:100%; border-collapse:collapse; margin-left:10px\"><tbody><tr><td valign=\"top\" style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; white-space:nowrap; font-weight:500; width:140px\">Sent by:</td><td style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:14px; font-weight:400\"><em>MA0P287MB1322.INDP287.PROD.OUTLOOK.COM</em> </td></tr></tbody></table></td></tr></tbody></table><br><table cellspacing=\"0\" style=\"width:880px\"><tbody><tr><td colspan=\"6\" style=\"padding-top:4px; border-bottom:1px solid #999999; padding-bottom:4px; line-height:120%; font-size:17px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500\">Message Hops</td></tr><tr><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; background-color:#f2f5fa; border-bottom:1px solid #999999; white-space:nowrap; padding:8px\">HOP</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; background-color:#f2f5fa; border-bottom:1px solid #999999; white-space:nowrap; padding:8px; width:80px\">TIME (UTC)</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; background-color:#f2f5fa; border-bottom:1px solid #999999; white-space:nowrap; padding:8px\">FROM</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; background-color:#f2f5fa; border-bottom:1px solid #999999; white-space:nowrap; padding:8px\">TO</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; background-color:#f2f5fa; border-bottom:1px solid #999999; white-space:nowrap; padding:8px\">WITH</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; background-color:#f2f5fa; border-bottom:1px solid #999999; white-space:nowrap; padding:8px\">RELAY TIME</td></tr><tr><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; border-bottom:1px solid #999999; padding:8px; text-align:center\">1</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; border-bottom:1px solid #999999; padding:8px; text-align:left; width:80px\">26/08/2025<br>06:14:54</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; border-bottom:1px solid #999999; padding:8px; text-align:left\">PN2P287MB1310.INDP287.PROD.OUTLOOK.COM</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; border-bottom:1px solid #999999; padding:8px; text-align:left\">PN2P287MB1310.INDP287.PROD.OUTLOOK.COM</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; border-bottom:1px solid #999999; padding:8px; text-align:left\">mapi</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; border-bottom:1px solid #999999; padding:8px; text-align:left\">*</td></tr><tr><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; border-bottom:1px solid #999999; padding:8px; text-align:center\">2</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; border-bottom:1px solid #999999; padding:8px; text-align:left; width:80px\">26/08/2025<br>06:14:54</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; border-bottom:1px solid #999999; padding:8px; text-align:left\">PN2P287MB1310.INDP287.PROD.OUTLOOK.COM</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; border-bottom:1px solid #999999; padding:8px; text-align:left\">MA0P287MB1322.INDP287.PROD.OUTLOOK.COM</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; border-bottom:1px solid #999999; padding:8px; text-align:left\">Microsoft SMTP Server (version=TLS1_2, cipher=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384)</td><td style=\"font-size:12px; font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-weight:500; border-bottom:1px solid #999999; padding:8px; text-align:left\">*</td></tr></tbody></table><p style=\"font-family:'Segoe UI',Frutiger,Arial,sans-serif; font-size:17px; font-weight:500; padding-top:4px; padding-bottom:0; margin-top:19px; margin-bottom:5px\">Original Message Headers</p><pre style=\"color:gray; white-space:pre; padding-top:0; margin-top:5px\">ARC-Seal: i=1; a=rsa-sha256; s=arcselector10001; d=microsoft.com; cv=none;\r\n b=gKQOTBy2nX/Hc++VLkri3r3k1T4WdrfuOLdO1OjdQUU2e5Knlqp+xtOLj3KoyVhvLR+vkp+bb8vf5jL/JCi9LqijqJfw90YpNWAoZ8GFCm7kaYrYwXsLGrg6M0lpoqRJpBVAwiTvlOewhutmgq5ye5aQQV1l80svBNSLUkJBILYWubNBxe030SIU8AHpcfmMfET2HJQK3IJ6wkqo03Noyr2vpFC3mmmq57fa6iq8q5K4qf8poqwb6Td9/tKjTToGhZjAnpwKrp3MDTbyjJyBzq9C+MSbC1QfyZxkV/pbIBLcI8WX6E4VxPc0Th6/bCT/h9WNTc4v06eIyHE/aEsZaw==\r\nARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=microsoft.com;\r\n s=arcselector10001;\r\n h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-AntiSpam-MessageData-ChunkCount:X-MS-Exchange-AntiSpam-MessageData-0:X-MS-Exchange-AntiSpam-MessageData-1;\r\n bh=qfR+mL8uA95rHBppsrvu5fQB/sVigMuxXmjkeyhyWPc=;\r\n b=N7W3qDbW6OXmRPkHt/BKHhaU67/7jkNWIOzbQJtBS1zVaMMR6isc7OO0dTzkrWznEg+etdHL0vOy9Glfct9DwB66l6uDxQb9dOplbDKbJEmyyXcl9Ix5I6qR4bZpEcOaUOl1ISRw6AUL2p3bY11/Kb9tnuMB0rrLl1nizDUO8/D/0+kv5v+yv/OSGlsRhgsOc4MThT250tBzGKVRUa3Hpmu35B3VH2B7boEnoWs1t43hpK2WsNEH8EpFr9SOk7nCSNM3TH0Jk5DnDSSHvhUW1ZMWJ93PDhKz0GWEcYQDPcuDoqrScLTVFmHK2H4dGia31Jub2XNkyjmfPPLPl6v2Xw==\r\nARC-Authentication-Results: i=1; mx.microsoft.com 1; spf=pass\r\n smtp.mailfrom=kylas.io; dmarc=pass action=none header.from=kylas.io;\r\n dkim=pass header.d=kylas.io; arc=none\r\nReceived: from PN2P287MB1310.INDP287.PROD.OUTLOOK.COM (2603:1096:c01:1b3::11)\r\n by MA0P287MB1322.INDP287.PROD.OUTLOOK.COM (2603:1096:a01:f4::6) with\r\n Microsoft SMTP Server (version=TLS1_2,\r\n cipher=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384) id 15.20.9073.13; Tue, 26 Aug\r\n 2025 06:14:54 +0000\r\nReceived: from PN2P287MB1310.INDP287.PROD.OUTLOOK.COM\r\n ([fe80::2574:c107:e23a:1a6b]) by PN2P287MB1310.INDP287.PROD.OUTLOOK.COM\r\n ([fe80::2574:c107:e23a:1a6b%6]) with mapi id 15.20.9052.019; Tue, 26 Aug 2025\r\n 06:14:54 +0000\r\nFrom: kalpesh vasave &lt;<EMAIL>&gt;\r\nTo: &quot;<EMAIL>&quot; &lt;<EMAIL>&gt;\r\nSubject: attack on titan\r\nThread-Topic: attack on titan\r\nThread-Index: AQHcFlC8rQ9iuP565kavRDjJHiAGTQ==\r\nDate: Tue, 26 Aug 2025 06:14:54 +0000\r\nMessage-ID: &lt;<EMAIL>&gt;\r\nAccept-Language: en-GB, en-US\r\nContent-Language: en-US\r\nX-MS-Has-Attach:\r\nX-MS-TNEF-Correlator:\r\nauthentication-results: dkim=none (message not signed)\r\n header.d=none;dmarc=none action=none header.from=kylas.io;\r\nx-ms-publictraffictype: Email\r\nx-ms-traffictypediagnostic: PN2P287MB1310:EE_|MA0P287MB1322:EE_\r\nx-ms-office365-filtering-correlation-id: 21c44dab-036d-493b-0dd9-08dde467dfad\r\nx-ms-exchange-senderadcheck: 1\r\nx-ms-exchange-antispam-relay: 0\r\nx-microsoft-antispam: BCL:0;ARA:13230040|69100299015|376014|**********|366016|38070700018|**********;\r\nx-microsoft-antispam-message-info: =?us-ascii?Q?51YpMwBNwL+5qtUxbhXge+r5MfqrJciy7YNI32b2U7+BWz+iUSgsrjuY+tMa?=\r\n =?us-ascii?Q?0FBNuAmJy8z+eJe5Sc0K+04BuIyuEbfoGWpH3gQCg/kej7XIwCrPMeoo6WhK?=\r\n =?us-ascii?Q?GnssFt8X/4KKtmyFYJpn5WidtQ/BLXlKmjhDZ99c3Saxn/GGFdFAk5cGvv5Z?=\r\n =?us-ascii?Q?duJaz22jQ3qTZUiP6HpiNNwUU8BliNu5ejukvPjj3Ma/D9oxYKxB6tRzcVHx?=\r\n =?us-ascii?Q?aPy/5yIDSWD+LtrhxYny6LcoqOazbsf4mQtJ/4YxUXu5tC61/CiwfNIMLPku?=\r\n =?us-ascii?Q?CxeezzBvHFGUroKUXIxFz8Oz5WsD3wgh8EfIXwokLaTyZFMzH5VJip+nje13?=\r\n =?us-ascii?Q?OLC+L+0yOZ/chgzHcFtKw1gqTjTgL+kSk2zvRVyr2hKy2HVpq3xJtMunQMxY?=\r\n =?us-ascii?Q?icGaT+WBJZWFjEZO8e/Hn2F54o7ZT0y5DAB/R5q1qizDgj1FaPRnogC/cRUP?=\r\n =?us-ascii?Q?gsy1Oq1BoalRi57i5RIcw7J+cuC3J98weeUxfOuxh60s+G6m1ja1LVTEfWK2?=\r\n =?us-ascii?Q?IfRYfY/YrHqIR4LL5RaRNQTbzWj0IB/0zi2i2XlS5pPkXp7qxb6pzwKzahfv?=\r\n =?us-ascii?Q?gH3nAaTDAh2VRNotwPIMcl0mmZvVJWVQbxBihqjHbGVUEtwwEGsLeASbdYwH?=\r\n =?us-ascii?Q?3Ugdnwo47bFETm2DYwLkYfoqVk/1taqEDH9//BiWPtbMDFOQQPjrZpk4zeFc?=\r\n =?us-ascii?Q?ONY+1E7g7K3QHr8Yqz0AhZlDT3zRTsVT4gC5KwoP0FJgRpu8wcm7hh1X037K?=\r\n =?us-ascii?Q?7XWpnWfXE66jMXicrFvVQuGwra4gh9mfBPPQ27Sti6l39oZuGThfQlKIVLpK?=\r\n =?us-ascii?Q?UtWMkgGIJaY1GH23R0qsTuybt6O9Yq1z7wfReTg1LTrR0Ag9Yqlx8uKpw08y?=\r\n =?us-ascii?Q?72x83PNG/EmQtVdZy6zWh68+sOxn7xi8nYvhk6ZTnsRUBQFwAFT3kKdBZWVa?=\r\n =?us-ascii?Q?uzzVeCg1thsbFddp1NU9y8hIqKR7pwgx6EiViUn1KdgBr3oIFd8c4/hpL8yD?=\r\n =?us-ascii?Q?GEUQyPvtQSa8tKUzZFI6wIhwdzvFHmVL/QgYC15JxD+qY0PtZ5tr23ajwsFu?=\r\n =?us-ascii?Q?dEQ1HPd3hT4u8JSkXEHScpXTfeOkWgr7WcHpwsRZf6ifl1PgOEQOdcNlcVbE?=\r\n =?us-ascii?Q?QwkqaXQ02Pl/KluFj7c/88H7W9AELqYhzspApqRMMOSTjzmsPwKnHdp1sK5O?=\r\n =?us-ascii?Q?Ll/opGCbg8CNqT7ymWXL+GfK1Da6FwQygKeR50D6LA+vEvT3i4Npiyl6s4+1?=\r\n =?us-ascii?Q?gqawoC/y9aIZQpLdv6FGg2Gy5aoot43EwFC2XcCOWgDfHHOaNmp4Yj+11lg2?=\r\n =?us-ascii?Q?JCp5kMeJmwY5llh0SSsj+I4K+Pln9se3tEBHRLnZ5Mgq9Y3NDrJFJYEnzVUC?=\r\n =?us-ascii?Q?sYyuA2FAwQoyjwbQWUCfGqu/45DWtjDucMBPbtGIwtUx03XLKt3wBAIBiQOo?=\r\n =?us-ascii?Q?q8TxaPPULFc=3D?=\r\nx-forefront-antispam-report: CIP:***************;CTRY:;LANG:en;SCL:1;SRV:;IPV:NLI;SFV:NSPM;H:PN2P287MB1310.INDP287.PROD.OUTLOOK.COM;PTR:;CAT:NONE;SFS:(13230040)(69100299015)(376014)(**********)(366016)(38070700018)(**********);DIR:OUT;SFP:1102;\r\nx-ms-exchange-antispam-messagedata-chunkcount: 1\r\nx-ms-exchange-antispam-messagedata-0: =?us-ascii?Q?3Nac/ywb9flnIun6pnhAQwM7SKHV5VM9niOdw8+lIqmUzoIb+nTaiJKZwTJB?=\r\n =?us-ascii?Q?dTfgTegnMtdsEv/GHXh+uM7cUzEAe01YgoOuOF94DoFWD1U++eGEfn/1t76D?=\r\n =?us-ascii?Q?veeizzErzW0dbZYLmN7MkyXEcpRPROzqIwEudRpHw5KqoQY7GJhTvPqHb063?=\r\n =?us-ascii?Q?R6ouZkSeiWNBrr4yIVSVsT6ROuRKkhBIcw6vEhH+WYJkVdU1X0Qf3k8Nrp6/?=\r\n =?us-ascii?Q?TcmBFkjEnOVlLUuXFaHo7GAHp02Gfdw80BPKbj815A6ntu/Wcz6zBKZanjpT?=\r\n =?us-ascii?Q?9RetJRPrVFo/pguXfrGM4XpPWnPBuFw5QJ/WhYddKJ6GezPivyyr5d8OHsNn?=\r\n =?us-ascii?Q?4xZpH74IhUOZW/LjhwZ1OR6FpwHnmXgz/asrxjdT5a4C3QJMvHc8SZkI3x82?=\r\n =?us-ascii?Q?Q/hEji/puaX60+TcrmFKkxPqfCPc4aO4+IUi/vyb7wdgEh8ixnQVR1Hs1Xdl?=\r\n =?us-ascii?Q?tPFDIrKk4kBkrDaGYHmcO9LuXkNRSoQX9OSpHGazlP+4waRY5IktYt3Md3AV?=\r\n =?us-ascii?Q?UzohMer6oBBNk2XpAb72/kmOgJdvqKCTQSZs2nRuzKVaavEYizNwMeL8OK5T?=\r\n =?us-ascii?Q?LBFRG9RwSnXcytu37bBuCaOjPdbRtNlF80bi90snOe2EXZzzMCOe2dfi5Uyi?=\r\n =?us-ascii?Q?TAnOiIdvNVXxlcrMedHJbj7h+20ETbKZpiQsffz/gJafMppjcXt4AJ8n0NJP?=\r\n =?us-ascii?Q?R/7NBA9WfVJcHpaFFJUciNPSpDLyTkWAcQ0gQgmMdvHrLYlFQ8gwx3sNIBTp?=\r\n =?us-ascii?Q?6JFVM0uRvjV+mamS9bA48GuotfPuOlzzOSgixPkmgZZKzNd6zD/LyxkLrJO0?=\r\n =?us-ascii?Q?914ZaAzvk57CZW8w4GO1WPgrVkpUcTSRu7PuctU/apPlks+OX4oI3rHllJPw?=\r\n =?us-ascii?Q?IPbYVdEvoBs1RGWAYpCAdxno25k1T+pwsMGP0aOMVUny/Q6ffZaKoikc6xYU?=\r\n =?us-ascii?Q?S2Z1x4h7T2gCmDwHLbxkYjqANEYlkS+f6SqP/NtV/29pnELj5yxmyrUTBKWG?=\r\n =?us-ascii?Q?uaI79iklKfU5G03gJQ+6TihDyuq5S8c2XHFHEco1GVgxTQza6/xy4qxakBB3?=\r\n =?us-ascii?Q?po2Uq4WoVtIg1OgSCFod/1bxlDLId7AUmhUj08Dv1zNAGuoYC0gHD/BjxDyO?=\r\n =?us-ascii?Q?zIFya4JHqkiJM35RBSI5gV0le6I4ettCdhhesGSjedARXwPepyEDLESIUdhl?=\r\n =?us-ascii?Q?Y7v+woBz0eFkKg2+zpyzcv7jr73ZkeJVZVvnyfobixaAGTm8fmjQpj376XJf?=\r\n =?us-ascii?Q?kD5qTZEN0AnmwELXdGEj4lRgqefmCdz/icc6Smfd9hnc8BwWgBJGjJAArquf?=\r\n =?us-ascii?Q?pGcuMvDsRTqFWx4WND1J1sIAPrbqS+mJ3TqUQ9XFPciElsp6zS0DzcTouAwQ?=\r\n =?us-ascii?Q?0pd9y5EJyGfJDRhlDwlcWsqo5lhK5ZkFwqqdQT1gHFx/3XpVrW5vIFIYPS+K?=\r\n =?us-ascii?Q?pXBKhn015KWAAcIKfmCKBvhUJrdXCykSPE154ouR3288SV1nPA7jJhnG2DjR?=\r\n =?us-ascii?Q?bnWLm/h2ghKvNfIjHMY1JdYrow3ofPaPS8xebWGEFIMuKsV8pXFo0+yVLYno?=\r\n =?us-ascii?Q?pI+9IhPmBhPQ3iiWaIfb+8JjBNnYnfakKQVh38jd?=\r\nContent-Type: multipart/alternative;\r\n\tboundary=&quot;_000_PN2P287MB13106C8F6EFBAEC248691958EE39APN2P287MB1310INDP_&quot;\r\nMIME-Version: 1.0\r\nX-OriginatorOrg: kylas.io\r\nX-MS-Exchange-CrossTenant-AuthAs: Internal\r\nX-MS-Exchange-CrossTenant-AuthSource: PN2P287MB1310.INDP287.PROD.OUTLOOK.COM\r\nX-MS-Exchange-CrossTenant-Network-Message-Id: 21c44dab-036d-493b-0dd9-08dde467dfad\r\nX-MS-Exchange-CrossTenant-originalarrivaltime: 26 Aug 2025 06:14:54.1502\r\n (UTC)\r\nX-MS-Exchange-CrossTenant-fromentityheader: Hosted\r\nX-MS-Exchange-CrossTenant-id: f0dce677-c472-4176-a936-4eb93f005cf5\r\nX-MS-Exchange-CrossTenant-mailboxtype: HOSTED\r\nX-MS-Exchange-CrossTenant-userprincipalname: lEdq7XZgdA4yEyHcA6eodsA1CUUfXtmPBcewMnVATUZXgXkSeQ0jMyTqOpAGTjmrgYecWzAE9zsf1avKlf6SYQ==\r\nX-MS-Exchange-Transport-CrossTenantHeadersStamped: MA0P287MB1322\r\n</pre></body></html>"}, "sender": {"emailAddress": {"name": "Microsoft Outlook", "address": "<EMAIL>"}}, "from": {"emailAddress": {"name": "Microsoft Outlook", "address": "<EMAIL>"}}, "toRecipients": [{"emailAddress": {"name": "<EMAIL>", "address": "<EMAIL>"}}], "ccRecipients": [], "bccRecipients": [], "replyTo": [], "flag": {"flagStatus": "notFlagged"}}