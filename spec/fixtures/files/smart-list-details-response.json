[{"createdAt": "2023-09-26T03: 27: 53.609+0000", "updatedAt": "2023-09-26T03: 27: 53.609+0000", "createdBy": 3779, "updatedBy": 3779, "recordActions": {"read": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "deleteAll": false, "quotation": false, "reshare": false}, "metaData": null, "id": 108998, "entityType": "EMAIL", "name": "Today's Emails", "description": "Incoming emails received Today", "searchRequest": {"fields": null, "jsonRule": {"id": null, "field": null, "type": null, "input": null, "operator": null, "value": null, "data": null, "condition": "AND", "not": null, "rules": [{"id": "direction", "field": "direction", "type": "string", "input": "string", "operator": "equal", "value": "received", "data": null, "condition": null, "not": null, "rules": null, "group": false}, {"id": "createdAt", "field": "createdAt", "type": "date", "input": null, "operator": "today", "value": "", "data": null, "condition": null, "not": null, "rules": null, "timeZone": "Etc/GMT+12", "group": false}], "group": true}}, "systemDefault": true, "sort": "updatedAt,desc", "size": 10}]