{"createdAt": "2022-02-18T10:56:27.455+0000", "updatedAt": "2022-11-22T10:52:05.927+0000", "createdBy": null, "updatedBy": 4167, "recordActions": null, "metaData": {"idNameStore": {"updatedBy": {"4167": "<PERSON>"}, "createdBy": {}}}, "id": 123, "accountName": "<PERSON> Account", "industry": "COMPUTER_SOFTWARE", "timezone": "Asia/Calcutta", "dateFormat": "MMM D, YYYY [at] h:mm a", "language": "EN", "currency": "INR", "companyName": "Tony's QA Company", "website": "https://www.kylas.io", "logo": "qa/tenant-123/123__download-db12d83a148c124f502039a6d7fd5bc3.jpeg", "address": "Address Line 1", "taxIdentificationNumber": "ABCD1234XYZ789", "city": "Address City", "state": "Address State", "zip": "347378", "country": "IN", "active": true, "confirmed": true, "confirmedAt": "2022-02-18T10:56:45.265+0000", "planName": "elevate-annual", "hasLogo": true}