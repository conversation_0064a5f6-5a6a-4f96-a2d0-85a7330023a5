require 'ostruct'

class RecursiveOpenStruct < OpenStruct
  def initialize(hash = nil)
    @table = {}
    @hash_table = {}

    if hash
      hash.each do |k, v|
        @table[k.to_sym] = recursively_convert(v)
        @hash_table[k.to_sym] = v
      end
    end
  end

  private

  def recursively_convert(obj)
    case obj
    when Hash
      self.class.new(obj)
    when Array
      obj.map { |e| recursively_convert(e) }
    else
      obj
    end
  end
end
