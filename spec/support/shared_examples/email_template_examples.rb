RSpec.shared_examples "an update action" do
  it 'updates the email template' do
    @res = UpdateEmailTemplate.call(@params)
    @result = @res.result

    expect(@res.success?).to be_truthy
    expect(@result.name).to eq('test template')
    expect(@result.category).to eq('lead')
    expect(@result.subject).to eq('test {{lead.firstName}}')
    expect(@result.body).to eq('some {{lead.firstName}} text {{lead.lastName}}')
    expect(@result.active).to be_truthy
    expect(@result.template_attachments.count).to eq(1)
    expect(@result.template_attachments.ids).to match_array([77])
  end

  it 'receives call to delete attachement service with correct parameters' do
    expect(DeleteFileFromS3).to receive(:call).with([@file_2_name], S3_EMAIL_TEMPLATE_BUCKET).exactly(1).times
    @res = UpdateEmailTemplate.call(@params)
  end
end