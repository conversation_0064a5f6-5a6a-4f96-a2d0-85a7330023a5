module ControllerSpec<PERSON>el<PERSON>
  def token_generator auth_data
    build(:auth_token, token_data: auth_data.as_json).token
  end
  
  def invalid_token_generator auth_data
    build(:auth_token_invalid, token_data: auth_data.as_json).token
  end
  
  def expired_token_generator auth_data
    auth_data.expiry = (Time.current - 1.day).to_i
    build(:auth_token_expired, token_data: auth_data.as_json).token
  end
  
  def valid_headers(auth_data)
    {
      "Authorization" => auth_data.token,
      "Content-Type" => "application/json"
    }
  end
  
  def invalid_headers
    {
      "Authorization" => nil,
      "Content-Type" => "application/json"
    }
  end
end