FactoryBot.define do
  factory :email do
    to { [Faker::Internet.email, Faker::Internet.email] }
    cc { [Faker::Internet.email, Faker::Internet.email] }
    from { Faker::Internet.email }
    bcc { [Faker::Internet.email, Faker::Internet.email] }
    related_to { [] }
    subject { "MyText" }
    source_id { 'Source' }
    body { "Email body goes here" }
    owner
    read_by { [] }
    connected_account
    reply_to { Faker::Internet.email }
    tenant
    status { "sent" }
    email_thread
    deleted { false }
    sender { build(:look_up, tenant_id: tenant_id, name: 'test lookup') }
    direction { 'received' }
  end
end
