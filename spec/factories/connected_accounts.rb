require 'google/apis/gmail_v1'
FactoryBot.define do
  factory :connected_account do
    tenant
    user
    active { true }
    access_token { "9jAin708tQSTkBlaFxAumtnkRwYGj4p0wQU/d3IlkT0=" }
    refresh_token { "YYYYYYYYY" }
    expires_at { DateTime.current + 30.minutes }
    email { Faker::Internet.email }
    provider_name { 'GOOGLE' }

    # after(:build) do
    #   Google::Apis::GmailV1::GmailService.any_instance.stub(:watch_user).and_return(true)

    #   RegisterGmailWebhook.stub(:call).and_return true
    # end
  end
end
