FactoryBot.define do
  factory :auth_data, class: 'Auth::Data' do
    expires_in { 43199 }
    expiry { (Time.now + 1.day).to_i }
    token_type { 'Bear<PERSON>' }
    user_id { rand(1000) }
    access_token { SecureRandom.uuid }
    username { Faker::Internet.email }
    tenant_id { rand(100) }
    permissions { build_list(:auth_permission_email, 1)}

    trait :contact_with_read_email_access do
      permissions { build_list(:auth_permission_contact_read_email_true, 1)}
    end

    trait :email_template_with_read_access do
      permissions { build_list(:auth_permission_email_template_read_true, 1)}
    end

    trait :email_template_with_write_access do
      permissions { build_list(:auth_permission_email_template_write_true, 1)}
    end

    trait :email_template_without_read_access do
      permissions { build_list(:auth_permission_email_template_read_false, 1)}
    end

    trait :email_template_without_write_access do
      permissions { build_list(:auth_permission_email_template_write_false, 1)}
    end

    trait :email_template_without_update_access do
      permissions { build_list(:auth_permission_email_template_update_false, 1)}
    end

    trait :email_template_with_update_access do
      permissions { build_list(:auth_permission_email_template_update_true, 1)}
    end

    trait :email_template_without_update_all_access do
      permissions { build_list(:auth_permission_email_template_update_all_false, 1)}
    end

    trait :email_template_with_update_all_access do
      permissions { build_list(:auth_permission_email_template_update_all_true, 1)}
    end

    trait :email_with_delete_permission do
      permissions { build_list(:auth_permission_email_delete_true, 1)}
    end

    trait :email_without_delete_permission do
      permissions { build_list(:auth_permission_email_delete_false, 1)}
    end
  end
end
