FactoryBot.define do
  factory :look_up do
    entity_id { rand(100001) }
    entity_type { LOOKUP_TYPES[rand(5)] }
    entity { "#{entity_type}_#{entity_id}"}
    name { Faker::Name.name }
    tenant_id { (1..100).to_a.sample(1).first }
    email {Faker::Internet.email}
    owner_id { rand(100) }

    after(:build) do |l|
      if l.entity_id.blank?
        e = l.entity.split('_')
        l.entity_type = e[0]
        l.entity_id = e[1]
      end
    end
  end
end
