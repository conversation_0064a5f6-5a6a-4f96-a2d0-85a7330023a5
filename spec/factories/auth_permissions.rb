FactoryBot.define do
  factory :auth_permission, class: 'Auth::Permission' do
    id { rand(10000) }
    name { ['email'].sample }
    description { Faker::Lorem.sentence}
    limits { rand(1000)}
    units { 'count' }
    action { build(:auth_permission_action)}

    trait :email do
      name { 'email' }
    end

    trait :contact_with_read_email do
      name { 'contact' }
      action { build(:auth_permission_action, read: true, read_all: true, email: true)}
    end

    trait :email_template_with_read do
      name { 'email_template' }
      action { build(:auth_permission_action, read: true, read_all: true)}
    end

    trait :email_template_with_write do
      name { 'email_template' }
      action { build(:auth_permission_action, write: true)}
    end

    trait :email_template_without_read do
      name { 'email_template' }
      action { build(:auth_permission_action, read: false, read_all: false)}
    end

    trait :email_template_without_write do
      name { 'email_template' }
      action { build(:auth_permission_action, write: false)}
    end

    trait :email_template_without_update do
      name { 'email_template' }
      action { build(:auth_permission_action, update: false, update_all: false)}
    end

    trait :email_template_with_update do
      name { 'email_template' }
      action { build(:auth_permission_action, update: true, update_all: false)}
    end

    trait :email_template_without_update_all do
      name { 'email_template' }
      action { build(:auth_permission_action, update: true, update_all: false)}
    end

    trait :email_template_with_update_all do
      name { 'email_template' }
      action { build(:auth_permission_action, update: true, update_all: true)}
    end

    trait :email_delete_true do
      name { 'email' }
      action { build(:auth_permission_action, delete: true)}
    end

    trait :email_delete_false do
      name { 'email' }
      action { build(:auth_permission_action, delete: false)}
    end

    factory :auth_permission_email, traits: [:email]
    factory :auth_permission_contact_read_email_true, traits: [:contact_with_read_email]
    factory :auth_permission_email_template_read_true, traits: [:email_template_with_read]
    factory :auth_permission_email_template_read_false, traits: [:email_template_without_read]
    factory :auth_permission_email_template_write_true, traits: [:email_template_with_write]
    factory :auth_permission_email_template_write_false, traits: [:email_template_without_write]
    factory :auth_permission_email_template_update_true, traits: [:email_template_with_update]
    factory :auth_permission_email_template_update_false, traits: [:email_template_without_update]
    factory :auth_permission_email_template_update_all_true, traits: [:email_template_with_update_all]
    factory :auth_permission_email_template_update_all_false, traits: [:email_template_without_update_all]
    factory :auth_permission_email_delete_true, traits: [:email_delete_true]
    factory :auth_permission_email_delete_false, traits: [:email_delete_false]
  end
end
