FactoryBot.define do
  lead_permission = {
    "id" => 4,
    "name" => "lead",
    "description" => "has access to lead resource",
    "limits" => -1,
    "units" => "count",
    "action" => {
      "read" => true,
      "write" => true,
      "update" => true,
      "delete" => true,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => true,
      "note" => true,
      "readAll" => true,
      "updateAll" => true
    }
  }

  email_permission = {
    "id" => 7,
    "name" =>  "email",
    "description" =>  "has access to team resource",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "write" => true,
      "update" => true,
      "delete" => true,
      "email" => false,
      "call" => false,
      "sms" => false,
      "task" => false,
      "note" => false,
      "readAll" => false,
      "updateAll" => true
    }
  }

  email_template_permission = {
    "id": 31,
    "name": "email_template",
    "description": "has access to Email Templates",
    "limits": 5,
    "units": "count",
    "action": {
      "read": true,
      "write": true,
      "update": true,
      "delete": true,
      "email": false,
      "call": false,
      "sms": false,
      "task": false,
      "note": false,
      "meeting": false,
      "readAll": false,
      "updateAll": true
    }
  }

  email_template_read_all = {
    "id": 31,
    "name": "email_template",
    "description": "has access to Email Templates",
    "limits": 5,
    "units": "count",
    "action": {
      "read": true,
      "write": true,
      "update": true,
      "delete": true,
      "email": false,
      "call": false,
      "sms": false,
      "task": false,
      "note": false,
      "meeting": false,
      "readAll": true,
      "updateAll": true
    }
  }

  email_template_read_false = {
    "id": 31,
    "name": "email_template",
    "description": "has access to Email Templates",
    "limits": 5,
    "units": "count",
    "action": {
      "read": false,
      "write": true,
      "update": true,
      "delete": true,
      "email": false,
      "call": false,
      "sms": false,
      "task": false,
      "note": false,
      "meeting": false,
      "readAll": false,
      "updateAll": true
    }
  }

  email_template_update_false = {
    "id": 31,
    "name": "email_template",
    "description": "has access to Email Templates",
    "limits": 5,
    "units": "count",
    "action": {
      "read": false,
      "write": true,
      "update": false,
      "delete": true,
      "email": false,
      "call": false,
      "sms": false,
      "task": false,
      "note": false,
      "meeting": false,
      "readAll": false,
      "updateAll": false
    }
  }

  email_template_update_false_and_read_true = {
    "id": 31,
    "name": "email_template",
    "description": "has access to Email Templates",
    "limits": 5,
    "units": "count",
    "action": {
      "read": true,
      "write": true,
      "update": false,
      "delete": true,
      "email": false,
      "call": false,
      "sms": false,
      "task": false,
      "note": false,
      "meeting": false,
      "readAll": true,
      "updateAll": false
    }
  }

  email_delete_true_permission = {
    "id" => 30,
    "name" =>  "email",
    "description" =>  "has access to email",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "delete" => true,
      "deleteAll" => false
    }
  }

  email_delete_all_true_permission = {
    "id" => 30,
    "name" =>  "email",
    "description" =>  "has access to email",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "delete" => false,
      "deleteAll" => true
    }
  }

  email_delete_false_permission = {
    "id" => 30,
    "name" =>  "email",
    "description" =>  "has access to email",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "delete" => false,
      "deleteAll" => false
    }
  }


  factory :auth_token, class: 'Auth::Token' do
    transient do
      expiry { (Time.current + 1.hour).to_i }
      user_id { rand(1000) }
      tenant_id { rand(1000) }
      access_token { SecureRandom.uuid }
      username { '<EMAIL>'}
      token_data {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            email_permission,
            email_template_permission
          ],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_with_read_all {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            email_permission,
            email_template_read_all
          ],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_without_update_permission {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            email_permission,
            email_template_update_false
          ],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_without_update_and_with_read_permission {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            email_permission,
            email_template_update_false_and_read_true
          ],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_without_email_tempalte_read_permission {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [
            lead_permission,
            email_permission,
            email_template_read_false
          ],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_with_email_delete {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [email_delete_true_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_with_email_delete_all {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [email_delete_all_true_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }

      token_data_without_email_delete {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [email_delete_false_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id
        }
      }


      payload {
        {
          "iss" => "sell",
          "data" => token_data
        }
      }

      payload_with_email_delete_permission {
        {
          "iss" => "sell",
          "data" => token_data_with_email_delete
        }
      }

      payload_with_email_delete_all_permission {
        {
          "iss" => "sell",
          "data" => token_data_with_email_delete_all
        }
      }

      payload_without_email_delete_permission {
        {
          "iss" => "sell",
          "data" => token_data_without_email_delete
        }
      }

      payload_without_email_template_read_permission {
        {
          "iss" => "sell",
          "data" => token_data_without_email_tempalte_read_permission
        }
      }

      payload_with_email_template_read_all {
        {
          "iss" => "sell",
          "data" => token_data_with_read_all
        }
      }

      payload_without_email_template_update_permission {
        {
          "iss" => "sell",
          "data" => token_data_without_update_permission
        }
      }

      payload_without_email_template_update_and_with_read_permission {
        {
          "iss" => "sell",
          "data" => token_data_without_update_and_with_read_permission
        }
      }
    end

    token { JWT.encode payload, nil, 'none' }

    trait :without_email_template_read_permission do
      token { JWT.encode payload_without_email_template_read_permission, nil, 'none' }
    end

    trait :without_email_template_update_permission do
      token { JWT.encode payload_without_email_template_update_permission, nil, 'none' }
    end

    trait :without_email_template_update_and_with_read_permission do
      token { JWT.encode payload_without_email_template_update_and_with_read_permission, nil, 'none' }
    end

    trait :with_email_template_read_all_permission do
      token { JWT.encode payload_with_email_template_read_all, nil, 'none' }
    end

    trait :with_email_delete_permission do
      token { JWT.encode payload_with_email_delete_permission, nil, 'none' }
    end

    trait :with_email_delete_all_permission do
      token { JWT.encode payload_with_email_delete_all_permission, nil, 'none' }
    end

    trait :without_email_delete_permission do
      token { JWT.encode payload_without_email_delete_permission, nil, 'none' }
    end

    factory :auth_token_invalid do
      token { 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzZWxsIiwiZGF0YSI6eyJleHBpcmVzSW4iOjQzMTk5LCJleHBpcnkiOjE1NzY0OTM3MTAsInRva2VuVHlwZSI6ImJlYXJlciIsInBlcm1pc3Npb25zIjpbeyJpZCI6NCwibmFtZSI6ImxlYWQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZSwid3JpdGUiOnRydWUsInVwZGF0ZSI6dHJ1ZSwiZGVsZXRlIjp0cnVlLCJlbWFpbCI6ZmFsc2UsImNhbGwiOmZhbHNlLCJzbXMiOmZhbHNlLCJ0YXNrIjp0cnVlLCJub3RlIjp0cnVlLCJyZWFkQWxsIjp0cnVlLCJ1cGRhdGVBbGwiOnRydWV9fSx7ImlkIjo3LCJuYW1lIjoidGVhbSIsImRlc2NyaXB0aW9uIjoiaGFzIGFjY2VzcyB0byB0ZWFtIHJlc291cmNlIiwibGltaXRzIGlvbiI6eyJyZWFkIjp0cnVlLCJ3cml0ZSI6dHJ1ZSwidXBkYXRlIjp0cnVlLCJkZWxldGUiOnRydWUsImVtYWlsIjpmYWxzZSwiY2FsbCI6ZmFsc2UsInNtcyI6ZmFsc2UsInRhc2siOmZhbHNlLCJub3RlIjpmYWxzZSwicmVhZEFsbCI6dHJ1ZSwidXBkYXRlQWxsIjp0cnVlfX1dLCJ1c2VySWQiOiIxMiIsInVzZXJ_U3W9DSeom8' }
    end
    factory :auth_token_expired do
      expiry { (Time.now - 1.day).to_i}
      token { JWT.encode payload, nil, 'none' }
    end
  end
end
