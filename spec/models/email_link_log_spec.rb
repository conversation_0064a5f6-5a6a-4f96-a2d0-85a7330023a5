require 'rails_helper'

RSpec.describe EmailLinkLog, type: :model do
  describe 'db schema' do
    it do
      should have_db_column(:email_id).of_type(:integer)
    end

    it do
      should have_db_column(:tenant_id).of_type(:integer)
    end

    it do
      should have_db_column(:link_mapping_id).of_type(:uuid)
    end
  end

  describe 'associations' do
    it 'belongs to email' do
      expect(described_class.reflect_on_association(:email).macro).to eq(:belongs_to)
    end

    it 'belongs to tenant' do
      expect(described_class.reflect_on_association(:tenant).macro).to eq(:belongs_to)
    end

    it 'belongs to link mapping' do
      expect(described_class.reflect_on_association(:link_mapping).macro).to eq(:belongs_to)
    end
  end
end
