require 'rails_helper'

RSpec.describe Attachment, type: :model do
  describe 'db schema' do
    it do
      should have_db_column(:id).with_options(null: false, primary: true)
    end
    it do
      should have_db_column(:file_name).of_type(:string)
    end
    it do
      should have_db_column(:deleted).of_type(:boolean)
    end
  end

  describe 'associations' do
    it do
      should belong_to(:email)
    end
  end

  describe 'class methods' do
    before do
      @email = create(:email)
      @email1 = create(:email)
      create_list(:attachment, 5, email: @email, file_size: 10)
      create_list(:attachment, 3, email: @email1, file_size: 20)
      create_list(:attachment, 1, email: @email1, file_size: 30)
    end
    context 'usage_per_tenant' do
      it 'will return usage per tenant - when tenant id is not passed' do
        data = Attachment.usage_per_tenant
        expected_data = [{:tenantId=> @email.tenant_id, :count=>50, :usageEntity=>"STORAGE_EMAIL_ATTACHMENT"}, {:tenantId=>@email1.tenant_id, :count=>90, :usageEntity=>"STORAGE_EMAIL_ATTACHMENT"}]
        expect(data).to eql expected_data
      end

      it 'will return usage of the tenant - when tenant id is passed' do
        data = Attachment.usage_per_tenant(@email.tenant_id)
        expected_data = [{:tenantId=>@email.tenant_id, :count=>50, :usageEntity=>"STORAGE_EMAIL_ATTACHMENT"}]
        expect(data).to eql expected_data
      end

      it 'will return usage of the tenant - when tenant id is passed, returns 0' do
        data = Attachment.usage_per_tenant(143)
        expected_data = [{:tenantId=>143, :count=>0, :usageEntity=>"STORAGE_EMAIL_ATTACHMENT"}]
        expect(data).to eql expected_data
      end
    end

    context 'total_attachment_size_for_tenant' do
      it 'will return total file_size for given tenant' do
        expect(Attachment.total_attachment_size_for_tenant @email1.tenant_id).to eql 90
      end
    end
  end
end
