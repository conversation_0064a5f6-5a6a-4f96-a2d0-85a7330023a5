require 'rails_helper'

RSpec.describe TrackMapping, type: :model do
  describe 'db schema' do
    it do
      should have_db_column(:id).of_type(:uuid).with_options(null: false, primary: true)
    end

    it do
      should have_db_column(:email_id).of_type(:integer)
    end

    it do
      should have_db_column(:tenant_id).of_type(:integer)
    end
  end

  describe 'associations' do
    it 'belongs to email with optional true' do
      track_mapping_email_association = described_class.reflect_on_association(:email)
      expect(track_mapping_email_association.macro).to eq(:belongs_to)
      expect(track_mapping_email_association.options).to eq({ optional: true })
    end

    it 'belongs to tenant with optional true' do
      track_mapping_tenant_association = described_class.reflect_on_association(:tenant)
      expect(track_mapping_tenant_association.macro).to eq(:belongs_to)
      expect(track_mapping_tenant_association.options).to eq({ optional: true })
    end
  end
end
