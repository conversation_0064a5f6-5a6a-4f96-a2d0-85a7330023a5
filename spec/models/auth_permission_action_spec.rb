require 'rails_helper'

RSpec.describe Auth::PermissionAction.new({}), type: :model do
  describe 'entity definition' do
    describe 'validations' do
      it do
        should validate_presence_of(:read)
      end
      it do
        should validate_presence_of(:write)
      end
      it do
        should validate_presence_of(:update)
      end
      it do
        should validate_presence_of(:delete)
      end
      it do
        should validate_presence_of(:email)
      end
      it do
        should validate_presence_of(:call)
      end
      it do
        should validate_presence_of(:sms)
      end
      it do
        should validate_presence_of(:task)
      end
      it do
        should validate_presence_of(:note)
      end
      it do
        should validate_presence_of(:read_all)
      end
      it do
        should validate_presence_of(:update_all)
      end
    end
    describe '.initialize' do
      before do
        @permission_action = Auth::PermissionAction.new({
          "read"=>true,
          "write"=>true,
          "update"=>true,
          "delete"=>true,
          "email"=>false,
          "call"=>false,
          "sms"=>false,
          "task"=>true,
          "note"=>true,
          "read_all"=>true,
          "update_all"=>true
        })
      end
      it do
        @permission_action.should have_attributes(read: true) 
      end
      it do
        @permission_action.should have_attributes(write: true) 
      end
      it do
        @permission_action.should have_attributes(update: true) 
      end
      it do
        @permission_action.should have_attributes(delete: true) 
      end
      it do
        @permission_action.should have_attributes(email: false) 
      end
      it do
        @permission_action.should have_attributes(call: false) 
      end
      it do
        @permission_action.should have_attributes(task: true) 
      end
      it do
        @permission_action.should have_attributes(read_all: true)
      end
      it do
        @permission_action.should have_attributes(update_all: true) 
      end
    end
  end
end