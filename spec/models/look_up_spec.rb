require 'rails_helper'

RSpec.describe LookUp, type: :model do
  describe 'Entities' do
    describe 'db schema' do
      it do
        should have_db_column(:id).with_options(null: false, primary: true)
      end
      it do
        should have_db_column(:entity).of_type(:string)
      end
      it do
        should have_db_column(:email).of_type(:string)
      end
      it do
        should have_db_column(:tenant_id).of_type(:integer)
      end
      it do
        should have_db_column(:name).of_type(:string)
      end
      it do
        should have_db_column(:created_at).of_type(:datetime)
      end
      it do
        should have_db_column(:updated_at).of_type(:datetime)
      end
    end

    describe 'validations' do
      it do
        should allow_value('lead_1', 'deal_12', 'contact_13', 'company_14', 'timezone_25').for(:entity)
      end
      it do
        should_not allow_value('TEST_1212', 'LEAD_1000', 'DEAL_400', 'tesst', '1212112', 'timezone').for(:entity)
      end
      it do
        should validate_presence_of(:entity_type)
      end
      it do
        should_not validate_presence_of(:name)
      end
      it 'entity value is not modifiable once created' do
        look_up = create(:look_up)
        look_up.entity = 'LEAD_92888888'
        expect(look_up.save).to be false
      end
      it 'name is always modifyble' do
        look_up = create(:look_up)
        look_up.name = Faker::Name.name
        expect(look_up.save).to be true
      end
    end
    describe 'associations' do
      it do
        should have_many(:email_look_ups)
      end
    end
  end
end
