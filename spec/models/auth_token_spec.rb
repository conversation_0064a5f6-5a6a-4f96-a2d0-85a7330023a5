require 'rails_helper'

RSpec.describe Auth::Data.new({}), type: :model do
  describe 'entity definition' do
    describe 'validations' do
      it do
        should validate_presence_of(:user_id)
      end
      it do
        should validate_presence_of(:tenant_id)
      end
      it do
        should validate_presence_of(:permissions)
      end
    end
    describe '.initialize' do
      before do
        @expiry = Time.current + 1.day
        @auth_data = Auth::Data.new({
          "expiresIn" => 43199,
          "expiry" => @expiry,
          "tokenType" => "bearer",
          "permissions" => [
            {
              "id" => 4,
              "name" => "email",
              "description" => "has access to email resource",
              "limits" => -1,
              "units" => "count",
              "action" => {
                "read" => true,
                "write" => true,
                "update" => true,
                "delete" => true,
                "email" => false,
                "call" => false,
                "sms" => false,
                "task" => true,
                "note" => true,
                "readAll" => true,
                "updateAll" => true
              }
            }
          ],
          "userId" => "12",
          "username" => "<EMAIL>",
          "tenantId" => "14"
        })
      end
      it do
        @auth_data.should have_attributes(expires_in: 43199)
      end
      it do
        @auth_data.should have_attributes(expiry: @expiry)
      end
      it do
        @auth_data.should have_attributes(token_type: "bearer")
      end
      it do
        @auth_data.should have_attributes(user_id: 12)
      end
      it do
        @auth_data.should have_attributes(username: "<EMAIL>")
      end
      it do
        @auth_data.should have_attributes(tenant_id: "14")
      end

      it 'builds action object with nested attributes' do
        expect(@auth_data.permissions.first.to_json) == Auth::Permission.new({
          "id" => 4,
          "name" => "email",
          "description" => "has access to email resource",
          "limits" => -1,
          "units" => "count",
          "action" => {
            "read" => true,
            "write" => true,
            "update" => true,
            "delete" => true,
            "email" => false,
            "call" => false,
            "sms" => false,
            "task" => true,
            "note" => true,
            "readAll" => true,
            "updateAll" => true
          }
        }).to_json
      end
    end
  end
end
