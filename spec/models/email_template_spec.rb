require 'rails_helper'

RSpec.describe EmailTemplate, type: :model do
  subject { EmailTemplate.new(name: "template 1", category: 'lead', subject: 'sub 1', created_by: create(:user, id: 1, tenant: create(:tenant))) }
  describe 'db schema' do
    it do
      should have_db_column(:id).with_options(null: false, primary: true)
    end
    it do
      should have_db_column(:name).of_type(:string).with_options(null: false)
    end
    it do
      should have_db_column(:category).of_type(:string).with_options(null: false)
    end
    it do
      should have_db_column(:subject).of_type(:string)
    end
    it do
      should have_db_column(:body).of_type(:text)
    end
    it do
      should have_db_column(:active).of_type(:boolean)
    end
    it do
      should have_db_column(:created_by_id).of_type(:integer).with_options(null: false)
    end
    it do
      should have_db_column(:updated_by_id).of_type(:integer).with_options(null: false)
    end
    it do
      should have_db_column(:created_at).of_type(:datetime)
    end
    it do
      should have_db_column(:updated_at).of_type(:datetime)
    end
  end

  describe 'associations' do
    it do
      should belong_to(:created_by).class_name('User')
    end
    it do
      should belong_to(:updated_by).class_name('User')
    end
    it do
      should have_many(:template_attachments)
    end
  end

  describe 'class methods' do
    let(:tenant_one) { create(:tenant) }
    let(:tenant_two) { create(:tenant) }

    before do
      RSpec::Mocks.space.reset_all
      @tenant_publisher = class_double(TenantEmailTemplateUsagePublisher).as_stubbed_const
      allow(TenantEmailTemplateUsagePublisher).to receive(:publish).with(instance_of(Integer))
      user_1 = create(:user, tenant: tenant_one)
      user_2 = create(:user, tenant: tenant_two)
      create_list(:email_template, 3, tenant_id: user_1.tenant_id)
      create_list(:email_template, 2, active: false, tenant_id: user_1.tenant_id)
      create_list(:email_template, 3, tenant_id: user_2.tenant_id)
    end

    context '#usage per tenant' do
      it 'will return count of emails templates per tenant' do
        data = EmailTemplate.usage_per_tenant
        expected_response = [{ :tenantId => tenant_two.id, :count => 3, :usageEntity => "EMAIL_TEMPLATE" },
                             { :tenantId => tenant_one.id, :count => 3, :usageEntity => "EMAIL_TEMPLATE"}]
        expect(data).to match_array(expected_response)
      end

      it 'will return count of emails templates for particular tenant' do
        data = EmailTemplate.usage_per_tenant(tenant_two.id)
        expected_response = [{ :tenantId => tenant_two.id, :count => 3, :usageEntity => "EMAIL_TEMPLATE" }]
        expect(data).to match_array(expected_response)
      end
    end

    context '#publish_email_template_usage' do
      let(:tenant) { create(:tenant) }

      it 'should publish when email template is created' do
        expect(@tenant_publisher).to receive(:publish).with(tenant.id)
        create(:email_template, tenant_id: tenant.id)
      end

      it 'should publish if status is changed' do
        expect(@tenant_publisher).to receive(:publish).with(tenant_one.id)
        email_emplate = EmailTemplate.first
        email_emplate.update(active: false)
      end

      it 'should NOT publish if fields other than status are changed' do
        expect(@tenant_publisher).not_to receive(:publish)
        email_emplate = EmailTemplate.first
        email_emplate.update(name: 'newName')
      end
    end
  end
end

