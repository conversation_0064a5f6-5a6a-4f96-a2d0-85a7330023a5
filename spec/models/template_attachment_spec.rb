require 'rails_helper'

RSpec.describe TemplateAttachment, type: :model do
  before{ allow(TenantEmailTemplateUsagePublisher).to receive(:publish).with(instance_of(Integer)) }
  describe 'db schema' do
    it do
      should have_db_column(:id).with_options(null: false, primary: true)
    end
    it do
      should have_db_column(:url).of_type(:text)
    end
    it do
      should have_db_column(:file_name).of_type(:string)
    end
    it do
      should have_db_column(:file_size).of_type(:integer)
    end
    it do
      should have_db_column(:created_at).of_type(:datetime)
    end
    it do
      should have_db_column(:updated_at).of_type(:datetime)
    end
  end

  describe 'associations' do
    it do
      should belong_to(:email_template)
    end
  end

  describe 'class methods' do
    before do
      template_1 = create(:email_template, tenant_id: 14)
      template_2 = create(:email_template, tenant_id: 15)
      create_list(:template_attachment, 5, email_template: template_1, file_size: 10)
      create_list(:template_attachment, 3, email_template: template_2, file_size: 20)
      create_list(:template_attachment, 1, email_template: template_2, file_size: 30)
    end

    context 'usage_per_tenant' do
      it 'will return usage per tenant' do
        data = TemplateAttachment.usage_per_tenant
        expected_data = [{ :tenantId => 15, :count => 90, :usageEntity => "STORAGE_EMAIL_ATTACHMENT" },
                         { :tenantId => 14, :count => 50, :usageEntity => "STORAGE_EMAIL_ATTACHMENT" }]
        expect(data).to eql expected_data
      end

      it 'will return usage for particular tenant' do
        data = TemplateAttachment.usage_per_tenant(15)
        expected_data = [{ :tenantId => 15, :count => 90, :usageEntity => "STORAGE_EMAIL_ATTACHMENT" }]
        expect(data).to eql expected_data
      end
    end
    context 'total_attachment_size_for_tenant' do
      it 'will return total file_size for given tenant' do
        expect(TemplateAttachment.total_attachment_size_for_tenant 15).to eql 90
      end
    end
  end
end
