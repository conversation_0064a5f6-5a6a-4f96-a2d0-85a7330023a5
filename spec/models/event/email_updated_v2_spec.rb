# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::EmailUpdatedV2 do
  before do
    email.update(status: Email.statuses['opened'])
  end

  let!(:track_mapping) { create(:track_mapping) }
  let!(:email) { track_mapping.email }
  let!(:old_serialized_email_data) { EmailDetailsSerializer.call(email, false, nil, true, add_owner_id: true).result }

  let(:event) { described_class.new(email, old_serialized_email_data) }


  describe '#routing_key' do
    it 'returns event name' do
      expect(event.routing_key).to eq('email.updated.v2')
    end
  end

  describe '#to_json' do
    it 'returns event payload' do
      email_track_log = email.email_track_logs.create(tenant_id: email.tenant_id)

      event_payload = JSON.parse(event.to_json)

      sender = {
        "id" => old_serialized_email_data['sender']['id'],
        "entity" => old_serialized_email_data['sender']['entity'],
        "name" => old_serialized_email_data['sender']['name'],
        "ownerId" => old_serialized_email_data['sender']['ownerId']
      }

      sender['email'] = old_serialized_email_data['sender']['email'] if old_serialized_email_data['sender']['email'].present?

      expect(event_payload.keys).to match_array(%w[entity oldEntity metadata])
      expect(event_payload['oldEntity']).to eq(old_serialized_email_data)
      expect(event_payload['metadata']).to eq(
        {
          "tenantId" => email.tenant_id,
          "userId" => email.owner.id,
          "entityType" => "EMAIL",
          "entityId" => email.id,
          "entityAction" => "UPDATED",
          "executedWorkflows" => []
        }
      )

      # returns nil body for all events
      expect(event_payload['entity']).to eq(
        {
          "id" => email.id,
          "body" => nil,
          "sender" => sender,
          "relatedTo" => [],
          "toRecipients" => [],
          "ccRecipients" => [],
          "bccRecipients" => [],
          "subject" => "MyText",
          "sentAt" => email.created_at.iso8601(3),
          "status" => "opened",
          "trackingEnabled" => false,
          "tenantId" => email.tenant_id,
          "threadId" => email.email_thread_id,
          "owner" => {
            "id" => email.owner.id,
            "name" => email.owner.name
          },
          "direction" => "received",
          "attachments" => [],
          "linksClickedAt" => [],
          "openedAt" => [email_track_log.created_at.iso8601(3)],
          "globalMessageId" => email.global_message_id
        }
      )
    end

    context 'when email has executedWorkflows in metadata' do
      before do
        email.update(metadata: { 'executedWorkflows' => ['WF_123', 'WF_456'] })
      end

      it 'includes executedWorkflows in metadata' do
        event_payload = JSON.parse(event.to_json)
        expect(event_payload['metadata']['executedWorkflows']).to eq(['WF_123', 'WF_456'])
      end
    end
  end
end
