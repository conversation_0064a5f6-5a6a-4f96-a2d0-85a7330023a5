# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::EmailDeletedWorkflowV2 do
  let(:user) { create(:user) }
  let(:email_thread) { create(:email_thread, owner: user, tenant_id: user.tenant_id) }
  let(:email) { create(:email, owner: user, tenant_id: user.tenant_id, email_thread: email_thread) }
  let(:thread) { create(:email_thread, owner: user, tenant_id: user.tenant_id) }
  let(:emails) { create_list(:email, 3, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id) }
  let!(:deleted_serialized_email) { EmailDetailsSerializer.call(email, false, FetchUser.call(user.id, user.tenant_id).result, false, true, add_owner_id: true).result }
  let(:event) { described_class.new(deleted_serialized_email) }

  describe '#routing_key' do
    it 'returns event name' do
      expect(event.routing_key).to eq('email.deleted.workflow.v2')
    end
  end

  describe '#to_json' do
    it 'returns event payload' do
      event_payload = JSON.parse(event.to_json)

      expect(event_payload.keys).to match_array(%w[entity oldEntity metadata])
      expect(event_payload['entity']).to eq(nil)
      expect(event_payload['oldEntity']).to eq(deleted_serialized_email)
      expect(event_payload['metadata']).to eq(
        {
          "tenantId" => email.tenant_id,
          "userId" => email.owner.id,
          "entityType" => "EMAIL",
          "entityId" => email.id,
          "entityAction" => "DELETED"
        }
      )
    end
  end
end
