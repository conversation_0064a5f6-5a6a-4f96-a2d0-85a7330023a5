# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::EmailOpened do
  let!(:track_mapping) { create(:track_mapping) }
  let!(:email) { track_mapping.email }
  let!(:old_serialized_email_data) { EmailDetailsSerializer.call(email, false, nil, true, add_owner_id: true).result }

  let(:event) { described_class.new(email, old_serialized_email_data) }

  describe '#routing_key' do
    it 'returns event name' do
      expect(event.routing_key).to eq('email.opened')
    end
  end

  describe '#to_json' do
    it 'returns event payload' do
      event_payload = JSON.parse(event.to_json)

      expect(event_payload.keys).to match_array(%w[entity oldEntity metadata])
      expect(event_payload['oldEntity']).to eq(old_serialized_email_data)
      expect(event_payload['metadata']).to eq(
        {
          "tenantId" => email.tenant_id,
          "userId" => email.owner.id,
          "entityType" => "EMAIL",
          "entityId" => email.id,
          "entityAction" => "EMAIL_OPENED",
          "executedWorkflows" => []
        }
      )
    end

    context 'when email has executedWorkflows in metadata' do
      before do
        email.update(metadata: { 'executedWorkflows' => ['WF_123', 'WF_456'] })
      end

      it 'includes executedWorkflows in metadata' do
        event_payload = JSON.parse(event.to_json)
        expect(event_payload['metadata']['executedWorkflows']).to eq(['WF_123', 'WF_456'])
      end
    end

    context 'when campaign info is present on the email' do
      before do
        email.update(campaign_info: {
          "email"=>"<EMAIL>", 
          "entityId"=>"1", 
          "activityId"=>234, 
          "campaignId"=>123, 
          "entitytype"=>"lead"
        })
      end

      it 'includes campaign info in the event payload' do
        event_payload = JSON.parse(event.to_json)
        expect(event_payload['entity']['campaignInfo']).to eq(
          {
            "email"=>"<EMAIL>", 
            "entityId"=>"1", 
            "activityId"=>234, 
            "campaignId"=>123, 
            "entitytype"=>"lead"
          }
        )

        expect(event_payload['metadata']).to eq(
          {
            "tenantId" => email.tenant_id,
            "userId" => email.owner.id,
            "entityType" => "EMAIL",
            "entityId" => email.id,
            "entityAction" => "EMAIL_OPENED",
            "executedWorkflows" => []
          }
        )
      end
    end
  end
end
