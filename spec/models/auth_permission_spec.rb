require 'rails_helper'

RSpec.describe Auth::Permission.new({}), type: :model do
  describe 'entity definition' do
    describe 'validations' do
      it do
        should validate_presence_of(:id)
      end
      it do
        should validate_presence_of(:name)
      end
      it do
        should validate_presence_of(:description)
      end
      it do
        should validate_presence_of(:limits)
      end
      it do
        should validate_presence_of(:units)
      end
      it do
        should validate_presence_of(:action)
      end
    end
    describe '.initialize' do
      before do
        @permission = Auth::Permission.new({
          "id"=>4,
          "name"=>"lead",
          "description"=>"has access to lead resource",
          "limits"=>-1,
          "units"=>"count",
          "action"=>{
            "read"=>true,
            "write"=>true,
            "update"=>true,
            "delete"=>true,
            "email"=>false,
            "call"=>false,
            "sms"=>false,
            "task"=>true,
            "note"=>true,
            "read_all"=>true,
            "update_all"=>true
          }
        })
      end
      it do
        @permission.should have_attributes(id: 4) 
      end
      it do
        @permission.should have_attributes(name: "lead") 
      end
      it do
        @permission.should have_attributes(description: "has access to lead resource") 
      end
      it do
        @permission.should have_attributes(limits: -1) 
      end
      it do
        @permission.should have_attributes(units: "count") 
      end
      it 'builds action object with nested attributes' do
        expect(@permission.action.to_json) == Auth::PermissionAction.new({
          "read"=>true,
          "write"=>true,
          "update"=>true,
          "delete"=>true,
          "email"=>false,
          "call"=>false,
          "sms"=>false,
          "task"=>true,
          "note"=>true,
          "read_all"=>true,
          "update_all"=>true
        }).to_json
      end
    end
  end
end
