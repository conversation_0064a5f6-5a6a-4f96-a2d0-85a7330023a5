require 'rails_helper'

RSpec.describe ConnectedAccount, type: :model do
  describe 'Entity definitions' do
    describe 'db schema' do
      it do
        should have_db_column(:id).with_options(null: false, primary: true)
      end
      it do
        should have_db_column(:tenant_id).of_type(:integer).with_options(null: false)
      end
      it do
        should have_db_column(:access_token).of_type(:string)
      end
      it do
        should have_db_column(:refresh_token).of_type(:string)
      end
      it do
        should have_db_column(:expires_at).of_type(:integer)
      end
      it do
        should have_db_column(:email).of_type(:string)
      end
      it do
        should have_db_column(:created_at).of_type(:datetime)
      end
      it do
        should have_db_column(:updated_at).of_type(:datetime)
      end
    end

    describe 'fields & validations' do
      subject { ConnectedAccount.new(tenant_id: 1, access_token: 'token', provider_name: 'GOOGLE', user_id: 11, expires_at: Time.now) }
      it do
        should validate_presence_of(:tenant_id)
      end
      it do
        should validate_presence_of(:access_token)
      end
      it do
        should validate_presence_of(:provider_name)
      end
      it do
        should validate_presence_of(:expires_at)
      end
      it do
        should validate_uniqueness_of(:email).scoped_to(:active)
      end
    end
  end
end
