require 'rails_helper'

RSpec.describe EmailLookUp, type: :model do
  subject { EmailLookUp.new(look_up: build(:look_up, entity_type: LOOKUP_USER, entity_id: 9, tenant_id: 99), email: build(:email, owner_id: 9, tenant_id: 99, email_thread_id: 1))}

  describe 'db schema' do
    it do
      should have_db_column(:id).with_options(null: false, primary: true)
    end
    it do
      should have_db_column(:related).of_type(:boolean)
    end
    it do
      should have_db_column(:email_id).
        of_type(:integer).
        with_options(null: false)
    end
    it do
      should have_db_column(:look_up_id).
        of_type(:integer).
        with_options(null: false)
    end
    it do
      should have_db_column(:created_at).of_type(:datetime)
    end
    it do
      should have_db_column(:updated_at).of_type(:datetime)
    end
  end

  describe 'associations' do
    it do
      should belong_to(:email).without_validating_presence
    end
    it do
      should belong_to(:look_up).without_validating_presence
    end
  end
end
