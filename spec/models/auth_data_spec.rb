require 'rails_helper'

RSpec.describe Auth::Data.new({}), type: :model do
  describe 'entity definition' do
    describe 'validations' do
      it 'checks that expiry is always in future' do
        auth_data = Auth::Data.new({
          "expires_in"=>43199,
          "expiry"=>1576493710,
          "token_type"=>"bearer",
          "user_id"=>"12",
          "username"=>"<EMAIL>",
          "tenant_id"=>"14"
        })
        expect(auth_data).to_not be_valid
      end
      it do
        should validate_presence_of(:user_id)
      end
      it do
        should validate_presence_of(:tenant_id)
      end
      it do
        should validate_presence_of(:permissions)
      end

    end
    describe '.initialize' do
      before do
        @auth_data = Auth::Data.new({
          "expires_in"=>43199,
          "expiry"=>1576493710,
          "token_type"=>"bearer",
          "user_id"=>"12",
          "username"=>"<EMAIL>",
          "tenant_id"=>"14",
          "permissions"=>[
            {
              "id"=>4,
              "name"=>"lead",
              "description"=>"has access to lead resource",
              "limits"=>-1,
              "units"=>"count",
              "action"=>{
                "read"=>true,
                "write"=>true,
                "update"=>true,
                "delete"=>true,
                "email"=>false,
                "call"=>false,
                "sms"=>false,
                "task"=>true,
                "note"=>true,
                "readAll"=>true,
                "updateAll"=>true
              }
            },{
              "id"=>7,
              "name"=>"team",
              "description"=>"has access to team resource",
              "limits"=>-1,
              "units"=>"count",
              "action"=>{
                "read"=>true,
                "write"=>true,
                "update"=>true,
                "delete"=>true,
                "email"=>false,
                "call"=>false,
                "sms"=>false,
                "task"=>false,
                "note"=>false,
                "readAll"=>true,
                "updateAll"=>true
              }
            }
          ]
        })
      end
      it do
        expect(@auth_data).to have_attributes(expires_in: 43199)
      end
      it do
        expect(@auth_data).to have_attributes(expiry: 1576493710)
      end
      it do
        expect(@auth_data).to have_attributes(token_type: "bearer")
      end
      it do
        expect(@auth_data).to have_attributes(user_id: 12)
      end
      it do
        expect(@auth_data).to have_attributes(username: "<EMAIL>")
      end
      it do
        expect(@auth_data).to have_attributes(tenant_id: "14")
      end
      it 'builds permission object with nest attributes' do
        expect(@auth_data.permissions[0].to_json) == Auth::Permission.new({
          "id"=>4,
          "name"=>"lead",
          "description"=>"has access to lead resource",
          "limits"=>-1,
          "units"=>"count",
          "action"=>{
            "read"=>true,
            "write"=>true,
            "update"=>true,
            "delete"=>true,
            "email"=>false,
            "call"=>false,
            "sms"=>false,
            "task"=>true,
            "note"=>true,
            "readAll"=>true,
            "updateAll"=>true
          }
        }).to_json
      end
    end
  end

  describe '#can_access?' do
    before do
      @auth_data = Auth::Data.new({
        "expires_in"=>43199,
        "expiry"=>1576493710,
        "token_type"=>"bearer",
        "user_id"=>"12",
        "username"=>"<EMAIL>",
        "tenant_id"=>"14",
        "permissions"=>[
          {
            "id"=>4,
            "name"=>"lead",
            "description"=>"has access to lead resource",
            "limits"=>-1,
            "units"=>"count",
            "action"=>{
              "read"=>true,
              "write"=>true,
              "update"=>true,
              "delete"=>false,
              "email"=>false,
              "call"=>false,
              "sms"=>false,
              "task"=>true,
              "note"=>true,
              "readAll"=>true,
              "updateAll"=>true
            }
          },{
            "id"=>7,
            "name"=>"team",
            "description"=>"has access to team resource",
            "limits"=>-1,
            "units"=>"count",
            "action"=>{
              "read"=>true,
              "write"=>true,
              "update"=>true,
              "delete"=>true,
              "email"=>false,
              "call"=>false,
              "sms"=>false,
              "task"=>false,
              "note"=>false,
              "readAll"=>true,
              "updateAll"=>true
            }
          }
        ]
      })
    end
    context 'for valid' do
      it 'returns true for permission & default action' do
        expect(@auth_data.can_access?('lead')).to be true
      end
      it 'returns true for permission & specific action' do
        expect(@auth_data.can_access?('lead', 'update')).to be true
      end
    end
    context 'for invalid' do
      it 'returns false for missing permission' do
        expect(@auth_data.can_access?('jarvis')).to be false
      end
      it 'returns false for valid permission & invalid action' do
        expect(@auth_data.can_access?('lead', 'delete')).to be false
      end

      it 'returns false for incorrect permission' do
        expect(@auth_data.can_access?(nil)).to be false
      end
    end
  end
end
