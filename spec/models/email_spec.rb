require 'rails_helper'

RSpec.describe Email, type: :model do
  subject { Email.new(sender: create(:look_up, entity_type: LOOKUP_USER, id: 12, tenant_id: 14)) }
  describe 'db schema' do
    it do
      should have_db_column(:id).with_options(null: false, primary: true)
    end
    it do
      should have_db_column(:to).of_type(:string).with_options(array: true)
    end
    it do
      should have_db_column(:owner_id).
        of_type(:integer).
        with_options(null: false)
    end
    it do
      should have_db_column(:connected_account_id).
        of_type(:integer).
        with_options(null: false)
    end
    it do
      should have_db_column(:sender_id).of_type(:integer)
    end
    it do
      should have_db_column(:created_at).of_type(:datetime)
    end
    it do
      should have_db_column(:updated_at).of_type(:datetime)
    end
    it do
      should have_db_column(:deleted).of_type(:boolean)
    end
    it do
      should have_db_column(:bounce_type).of_type(:integer)
    end
    it do
      should have_db_column(:failed_reason).of_type(:text)
    end
  end

  describe 'associations' do
    it do
      should belong_to(:owner)
    end
    it do
      should belong_to(:sender).without_validating_presence
    end
    it do
      should belong_to(:connected_account)
    end
    it do
      should have_many(:email_look_ups).
        conditions(related: true).
        class_name('EmailLookUp')
    end
    it do
      should have_many(:related_to).
        source('look_up').
        through('email_look_ups')
    end
    it do
      should have_many(:attachments)
    end
    it do
      should have_one(:track_mapping)
    end
    it do
      should have_many(:email_track_logs)
    end
  end

  # TODO: Enable this once validation is fixed
  describe 'validations' do
    xit 'should not allow sender to mark email as read' do
      tenant = create(:tenant, id: 14)
      @user = create(:user, id: 12, tenant: tenant)
      @user_look_up = create(:look_up, entity_type: LOOKUP_USER, entity_id: @user.id, tenant: tenant )
      connected_account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: @user)
      thread = create(:email_thread, owner: @user, tenant_id: @user.tenant_id)
      @email = create(:email, owner: @user, sender: @user_look_up, tenant_id: @user.tenant_id, connected_account: connected_account, email_thread_id: thread.id)
      @email.read_by << @user.id
      expect { @email.save! }.to raise_exception(ActiveRecord::RecordInvalid).with_message('Validation failed: Sender can not mark email as read or unread');
    end
  end

  describe 'instance methods' do
    before do
      tenant = create(:tenant, id: 15)
      @user = create(:user, id: 12, tenant: tenant )
      @user_look_up = create(:look_up, entity_type: LOOKUP_USER, id: 12, tenant_id: tenant.id )
      @another_user = create(:user, id: 15, tenant: tenant )
      auth_data = build(:auth_data, user_id: @another_user.id, tenant_id: @user.tenant_id, username: @another_user.name)
      thread = Thread.current
      thread[:auth] = auth_data
      connected_account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: @user)
      email_thread = create(:email_thread, owner: @user, tenant_id: @user.tenant_id)
      @email = create(:email, owner: @user, sender: @user_look_up, tenant_id: @user.tenant_id, connected_account: connected_account, email_thread_id: email_thread.id)
      @email.read_by << @another_user.id
      @email.save!
    end
    context 'User associated with email' do
      it 'should return email read status for current user' do
        expect(@email.read?).to be_truthy
      end
    end
  end

  describe 'callbacks' do
    context 'before_create' do
      context 'shall store body summary' do
        it 'will store first 100 characters of plain text' do
          email = create(:email, body: "<html><head>\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><style>\r\n<!--\r\n*\r\n\t{padding:0;\r\n\tmargin:0px auto}\r\nul li\r\n\t{list-style-position:inside;\r\n\tline-height:24px;\r\n\tfont-size:15px}\r\nhtml, body\r\n\t{margin:0;\r\n\tpadding:0}\r\n@media only screen and (max-width: 700px) {\r\nbody, table, td, p, a, li, blockquote\r\n\t{}\r\ntable\r\n\t{width:100%!important;\r\n\tmargin:0px 0%!important}\r\n.responsive-image img\r\n\t{height:auto!important;\r\n\tmax-width:100%!important;\r\n\twidth:100%!important}\r\ntr\r\n\t{padding:0px!important}\r\n.mob-img\r\n\t{width:50px!important}\r\n\r\n\t}\r\n-->\r\n</style></head><body align=\"center\" style=\"font-family:'Rubik',sans-serif; text-align:center; font-size:16px; line-height:26px; color:#2E384D\"><table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" style=\"background-image:url('https://assets.kylas.io/images/line.png'); background-repeat:no-repeat; background-size:100% 100%; background-position:center; border:1px solid #ddd; border-collapse:collapse; font-family:'Rubik',sans-serif\"><tbody><tr><td align=\"left\" style=\"padding:4% 3% 2%\"><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tbody><tr><td style=\"text-align:center; width:50%\"><a href=\"http://app-qa.sling-dev.com/\" target=\"_blank\"><img src=\"https://assets.kylas.io/images/logo.png\" style=\"width:89px; height:24px\"></a> </td></tr></tbody></table></td></tr><tr><td align=\"left\" style=\"padding:2% 3% 5%\"><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-image:url('https://assets.kylas.io/images/white-line.png'); background-repeat:no-repeat; background-size:100% 100%; background-position:center; border-radius:5px; padding:4% 3% 2%\"><tbody><tr><td><p style=\"margin:0; font-size:12px; line-height:18px; padding-bottom:3%; color:#2E384D; font-weight:400\">Hi Rohit Jagtap,</p><p style=\"margin:0; font-size:12px; line-height:18px; padding-bottom:3%; color:#2E384D; font-weight:400\">Your meeting for all enityt has been scheduled for 07-Aug-2023 at 17:45 (IST).</p></td></tr><tr><td align=\"left\"><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"border:1px solid #eee; border-radius:5px\"><tbody><tr style=\"background-color:#F4F6FC\"><td align=\"left\" style=\"width:25%; padding:2% 3%; vertical-align:top\"><p style=\"font-size:10px; line-height:18px; margin:0; font-weight:bold\">When</p></td><td align=\"left\" style=\"width:75%; padding:2% 3%\"><p style=\"font-size:10px; line-height:18px; margin:0\">07-Aug-2023 17:45-18:00 (IST)</p></td></tr><tr style=\"background-color:#F4F6FC\"><td align=\"left\" style=\"width:25%; padding:2% 3%; vertical-align:top\"><p style=\"font-size:10px; line-height:18px; margin:0; font-weight:bold\">Invitees</p></td><td align=\"left\" style=\"width:75%; padding:2% 3%\"><p style=\"font-size:10px; line-height:18px; margin:0\">Walter White &lt;<EMAIL>&gt;, gunjan user2 &lt;<EMAIL>&gt;, gunjan test user &lt;<EMAIL>&gt;, Virat Kohli &lt;<EMAIL>&gt;</p></td></tr></tbody></table></td></tr><tr><td><br><p style=\"margin:0; font-size:12px; line-height:18px; padding-bottom:3%; color:#2E384D; font-weight:400\">Remember you can always use Notes to help you create your minutes in an effective manner.</p><p style=\"margin:0; font-size:12px; line-height:18px; padding-bottom:3%; color:#2E384D; font-weight:400\">Have a great day,<br>Team Kylas</p></td></tr><tr><td align=\"left\" style=\"padding:5% 0 0\"><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tbody><tr><td align=\"left\" style=\"width:50%\"><a href=\"http://app-qa.sling-dev.com/\" target=\"_blank\"><img src=\"https://assets.kylas.io/images/blue-logo.png\" width=\"72px\" height=\"20px\" class=\"mob-img\" style=\"border:0; outline:0; min-width:72px; padding-left:5px\"></a> </td><td align=\"right\" style=\"width:50%\"><div style=\"margin:1% 0 0; font-size:10px; line-height:12px; color:#8798AD; padding:0 2%; text-align:right\"><a href=\"https://support.kylas.io\" target=\"_blank\" style=\"color:#8798AD; margin:0; text-decoration:none; padding-right:2%\">Support</a> | <a href=\"https://www.kylas.io/privacy\" target=\"_blank\" style=\"color:#8798AD; margin:0; text-decoration:none; padding-left:2%\">Privacy policy</a></div></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr><td style=\"padding:2% 0% 2%\"><div style=\"margin:0; font-size:10px; line-height:0; color:#8798AD; padding:0 2%; text-align:center\"><a href=\"http://email.qa1.kylasapp.com/u/eJwEwEGygyAMANDTyE4mMIHggt333yMEqbQqjtKZ9vZ9OSYgAFJrXDxbSbkIhYkQszdL4oReiJMsYlWNAZxFj-S0c9r4yaABBBuCGRDuPErb9_dRhXttxxgchVIK5mm8zs92qiteba1dP_nR-RwQXt-Nb12b6vF_nv9-AQAA__8mfSpl\" style=\"color:#8798AD; margin:0; text-decoration:none\">Unsubscribe</a> </div></td></tr></tbody></table></body></html>")
            
            expect(email.body_summary).to eq "Hi Rohit Jagtap,Your meeting for all enityt has been scheduled for 07-Aug-2023 at 17:45 (IST).When07-"
        end
      end

      context 'for recieved email' do
        it 'should set status as recieved' do
          email = create(:email)
          expect(email.status).to eq "received"
        end
      end
    end
  end

  describe 'class methods' do
    before do
      tenant = create(:tenant)
      @user = create(:user, tenant: tenant)
      @user1 = create(:user, tenant: create(:tenant))
      create_list(:email, 5, tenant: @user.tenant)
      create_list(:email, 3, tenant: @user1.tenant)
    end

    context '#usage per tenant' do
      it 'will return count of emails per tenant' do
        data = Email.usage_per_tenant
        expected_response = [{:tenantId=> @user.tenant_id, :count=>5, :usageEntity=>"EMAIL"}, {:tenantId=> @user1.tenant_id, :count=>3, :usageEntity=>"EMAIL"}]
        expect(data).to match_array(expected_response)
      end

      it 'will return count of emails for particular tenant' do
        data = Email.usage_per_tenant(@user.tenant_id)
        expected_response = [{:tenantId=> @user.tenant_id, :count=>5, :usageEntity=>"EMAIL"}]
        expect(data).to eql expected_response
      end

      it 'will return usage for tenant - will return 0' do
        data = Email.usage_per_tenant(134)
        expected_response = [{:tenantId=>134, :count=>0, :usageEntity=>"EMAIL"}]
        expect(data).to eql expected_response
      end
    end
  end
end

