# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GlobalMessage, type: :model do
  describe 'db schema' do
    it do
      should have_db_column(:id).with_options(null: false, primary: true)
    end
    it do
      should have_db_column(:message_id).of_type(:string).with_options(null: false)
    end
  end

  describe 'validations' do
    subject { GlobalMessage.new(message_id: 'test_message_id') }
    it do
      should validate_uniqueness_of(:message_id)
    end
  end
end
