require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'Entity definitions' do
    describe 'db schema' do
      it do
        should have_db_column(:id).
          with_options(null: false, primary: true)
      end
      it do
        should have_db_column(:name).
          of_type(:string).
          with_options(null: false)
      end
      it do
        should have_db_column(:tenant_id).
          of_type(:integer).with_options(null: false)
      end
      it do
        should have_db_column(:created_at).
          of_type(:datetime)
      end
      it do
        should have_db_column(:updated_at).
          of_type(:datetime)
      end
    end
    describe 'fields & validations' do
      it do
        should validate_presence_of(:name)
      end
      it do
        should validate_presence_of(:tenant_id)
      end
    end
  end
end
