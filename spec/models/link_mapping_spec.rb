require 'rails_helper'

RSpec.describe LinkMapping, type: :model do
  describe 'db schema' do
    it do
      should have_db_column(:id).of_type(:uuid).with_options(null: false, primary: true)
    end

    it do
      should have_db_column(:email_id).of_type(:integer)
    end

    it do
      should have_db_column(:tenant_id).of_type(:integer)
    end

    it do
      should have_db_column(:url).of_type(:text)
    end
  end

  describe 'associations' do
    it 'belongs to email' do
      expect(described_class.reflect_on_association(:email).macro).to eq(:belongs_to)
    end

    it 'belongs to tenant' do
      expect(described_class.reflect_on_association(:tenant).macro).to eq(:belongs_to)
    end

    it 'has many to email_link_logs' do
      expect(described_class.reflect_on_association(:email_link_logs).macro).to eq(:has_many)
    end
  end
end
