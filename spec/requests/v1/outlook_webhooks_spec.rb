require 'swagger_helper'
require 'bunny-mock'

RSpec.describe 'Outlook Webhook API', type: :request do
  path '/v1/outlook_webhooks' do
    post 'creates webhook' do
      tags 'Outlook webhook'
      consumes 'application/json'
      parameter name: :message, in: :body, type: :json

      response '200', 'webhook creation' do
        before { allow(PublishEvent).to receive(:call).and_return(nil) }

        run_test!
      end
    end
  end
end
