# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Layout API', type: :request do
  let(:user)              { create(:user)}
  let(:valid_auth_token)  { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)         { ParseToken.call(valid_auth_token.token).result }
  let(:Authorization)     { valid_auth_token.token }

  path '/v1/emails/layout/list' do
    get 'Layout List' do
      tags 'Layouts'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Layout List API' do
        run_test!
      end

      response '401', 'Authentication Failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end
    end
  end
end
