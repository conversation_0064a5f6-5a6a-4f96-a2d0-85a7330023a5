require 'rails_helper'

RSpec.describe 'Email Templates Management', type: :request do
  let(:user)                      { create(:user)}
  let(:another_user)              { create(:user, tenant_id: user.tenant_id)}
  let(:valid_auth_token)          { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:headers)                   { valid_headers(valid_auth_token) }
  let(:email_template)            { build(:email_template) }
  let(:invalid_email_template)    { build(:email_template, name: nil) }

  before do
    stub_entity_labels_api
    allow(TenantEmailTemplateUsagePublisher).to receive(:publish).with(instance_of(Integer))
  end

  describe '#create' do
    before do
      stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?custom-only=false&entityType=lead&page=0&size=100&sort=createdAt,asc").
        with(
          headers: {
            'Authorization' => "Bearer #{ valid_auth_token.token }"
          }).
          to_return(status: 200, body: file_fixture('lead-fields.json'), headers: {})
    end

    context 'with valid parameters' do
      it 'creates a new Email template' do
        expect {
          post '/v1/email-templates/',
          params: email_template, headers: headers, as: :json
        }.to change(EmailTemplate, :count).by(1)
      end

      it 'renders a JSON response with the new email template' do
        post '/v1/email-templates/',
          params: email_template, headers: headers, as: :json
        expect(response).to have_http_status(:created)
        expect(response.content_type).to eq('application/json; charset=utf-8')
      end

      context 'when duplicate template name is given' do
        let(:email_template) { build(:email_template, name: 't_1', body: "This is test body for first name: {{My Lead - First Name1 }}", tenant_id: user.tenant_id) }

        before { create(:email_template, name: 'T_1', body: "This is test body", tenant_id: user.tenant_id) }

        it "throws error for duplicate template name" do
          post '/v1/email-templates/',
            params: email_template, headers: headers, as: :json
          expect(response.parsed_body['errorCode']).to match(ErrorCode.duplicate_template)
        end
      end

      context 'when valid variable name is given in the body' do
        let(:email_template) { build(:email_template, body: "This is test body for first name: {{My Lead - First Name1 }}") }

        it "replaces the 'displayName' with 'internalName' for variable in email body" do
          post '/v1/email-templates/',
            params: email_template, headers: headers, as: :json
          expect(EmailTemplate.first.body).to include('{{lead.firstName}}')
        end
      end

      context 'when invalid variable name is given in template body' do
        let(:email_template)   { build(:email_template, body: "This is test body {{My Lead - test}}") }

        it "throws invalid variable error" do
          post '/v1/email-templates/',
            params: email_template, headers: headers, as: :json
          expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_variable)
        end
      end

      context 'when valid conditional variable name is given in the body' do
        let(:email_template) { build(:email_template, body: "<p><span style='color:hsl(210,75%,60%);'>&nbsp;{{My Lead - First Name1 }, {if missing: &lt;free text&gt;}} {{My Lead - Last Name }, {if missing: &lt;free text&gt;}} {{My Lead - Salutation=1}, {if missing: &lt;free text&gt;}}</span></p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>--</p><p>&nbsp;</p>") }

        it "replaces the 'displayName' with 'internalName' for variable in email body" do
          post '/v1/email-templates/',
            params: email_template, headers: headers, as: :json
          expect(EmailTemplate.first.body).to eq("<p><span style='color:hsl(210,75%,60%);'>&nbsp;{{lead.firstName}, {if missing: &lt;free text&gt;}} {{lead.lastName}, {if missing: &lt;free text&gt;}} {{lead.salutation}, {if missing: &lt;free text&gt;}}</span></p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>--</p><p>&nbsp;</p>")
        end
      end

      context 'when invalid conditional variable name is given in the body' do
        let(:email_template) { build(:email_template, body: "This is test body for first name: {{test}, {if missing: test}}") }

        it "replaces the 'displayName' with 'internalName' for variable in email body" do
          post '/v1/email-templates/',
            params: email_template, headers: headers, as: :json
          expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_variable)
        end
      end

      context 'when valid variable name is given in the subject' do
        let(:email_template) { build(:email_template, subject: "This is test subject for: {{My Lead - First Name1 }} ") }

        it "replaces the 'displayName' with 'internalName' for variable in email subject" do
          post '/v1/email-templates/',
            params: email_template, headers: headers, as: :json
          expect(EmailTemplate.first.subject).to include('{{lead.firstName}}')
        end
      end

      context 'when invalid variable name is given in template subject' do
        let(:email_template) { build(:email_template, subject: "This is test subject {{test}}") }

        it "throws invalid variable error" do
          post '/v1/email-templates/',
            params: email_template, headers: headers, as: :json
          expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_variable)
        end
      end

      context 'when valid conditional variable name is given in the subject' do
        let(:email_template) { build(:email_template, subject: "This is test subject for: {{My Lead - First Name1 }, {if missing: test}}") }

        it "replaces the 'displayName' with 'internalName' for variable in email subject" do
          post '/v1/email-templates/',
            params: email_template, headers: headers, as: :json
          expect(EmailTemplate.first.subject).to include("This is test subject for: {{lead.firstName}, {if missing: test}}")
        end
      end

      context 'when invalid conditional variable name is given in template subject' do
        let(:email_template) { build(:email_template, subject: "This is test subject {{test}, {if missing: test}}") }

        it "throws invalid variable error" do
          post '/v1/email-templates/',
            params: email_template, headers: headers, as: :json
          expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_variable)
        end
      end
    end

    context 'with invalid parameters' do
      it 'does not create a new Email template' do
        expect {
          post '/v1/email-templates/',
          params: email_template, headers: headers, as: :json
        }.to change(Email, :count).by(0)
      end

      it 'renders a JSON response with errors for the new email' do
        post '/v1/email-templates/', params: invalid_email_template, headers: headers, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.content_type).to eq('application/json; charset=utf-8')
      end
    end
  end

  describe '#update' do
    before do
      @email_template = create(:email_template, name: 't_1', body: "This is test body", tenant_id: user.tenant_id, created_by: user)

      old_attachment1 = create(:template_attachment, id: 77, email_template: @email_template, file_name: 'tenant_99/user_11/22_old_file.jpg')


      @existing_files = [
        {
          'id' => 77,
          'fileName' => 'old_file.jpg',
          'oldAttachment' => old_attachment1
        }
      ]

      @email_template.name = 't_2'
      @email_template.subject = 'Test Subject'
      @email_template.body = 'This is test body for t_2'
      @email_template.active = true
      template_params = @email_template.as_json.merge(attachments: @existing_files)

      stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?entityType=lead&custom-only=false&sort=createdAt,asc&page=0&size=100").
        with(
          headers: {
            "Authorization" => "Bearer #{ valid_auth_token.token }"
          }).
          to_return(status: 200, body: file_fixture('lead-fields.json'), headers: {})

          allow(DeleteFileFromS3).to receive(:call).and_return(nil)

          put "/v1/email-templates/#{@email_template.id}", params: template_params, headers: headers, as: :json
    end


    context 'when update permission is present on the email template' do
      it 'updates the temaplate details' do
        template = response.parsed_body
        expect(template['name']).to eq(@email_template.name)
        expect(template['subject']).to eq(@email_template.subject)
        expect(template['body']).to eq(@email_template.body)
        expect(template['status']).to eq('Active')
      end
    end

    context 'when update permission is not present on the email template' do
      let(:valid_auth_token) { build(:auth_token, :without_email_template_update_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }

      it "throws the error" do
        expect(response.parsed_body['errorCode']).to match(ErrorCode.update_email_template_not_allowed)
      end
    end
  end

  describe '#search' do
    context 'when only email template read permission is present' do
      before do
        @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
        @email_templates << create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: another_user)
        @email_templates.flatten!
        post '/v1/email-templates/search', params: {}, headers: headers, as: :json
      end

      it "returns only templates created by user" do
        content = response.parsed_body['content']
        expect(content.count).to eq(3)
        expect(content.map{|t| t['id']}).to match_array(@email_templates.first(3).map(&:id))
      end
    end

    context 'when email template read all permissions are present' do
      let(:valid_auth_token) { build(:auth_token, :with_email_template_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }

      context 'with valid parameters' do
        context "when sort parameters are not specified" do
          before do
            @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
            post '/v1/email-templates/search', params: {}, headers: headers, as: :json
          end

          it 'returns correct number of templates' do
            content = response.parsed_body['content']
            expect(content.count).to eq(3)
          end

          it 'returns all the expected fields in the response' do
            content = response.parsed_body['content']
            expect(content.first.keys).to match_array(['id', 'name', 'category', 'createdBy', 'status', 'createdAt', 'updatedAt', 'updatedBy', 'recordActions'])
          end

          it 'returns correct emails templates details in the response' do
            content = response.parsed_body['content']
            email_template = content.first
            template = @email_templates.first
            status = template.active ? 'Active' : 'Inactive'

            expect(email_template['name']).to eq(template.name)
            expect(email_template['category']).to eq(template.category)
            expect(email_template['status']).to eq(status)
            expect(email_template['createdBy']['id']).to eq(user.id)
            expect(email_template['createdBy']['name']).to eq(user.name)
          end

          it 'returns all the expected fields in the response' do
            content = response.parsed_body['content']
            expect(content.first.keys).to match_array(['id', 'name', 'category', 'createdBy', 'status', 'createdAt', 'updatedAt', 'updatedBy', 'recordActions'])
          end
        end

        context 'when valid sort column is specified in parameters' do
          before do
            @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
            post '/v1/email-templates/search?sort=name,asc', params: {}, headers: headers, as: :json
          end

          it 'returns email templates in the correct order' do
            expect(response.parsed_body['content'].map{|e| e['name']}).to match_array(@email_templates.map(&:name).sort)
          end
        end

        context 'when invalid sort column is specified is parameters' do
          before do
            @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
            post '/v1/email-templates/search?sort=test,asc', params: {}, headers: headers, as: :json
          end

          it 'throws invalid data error' do
            expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid)
          end
        end

        context 'when pagination is applied' do
          context 'when page number is 1' do
            let(:page) { 1 }

            before do
              @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
              post "/v1/email-templates/search?page=#{page}&size=#{size}", params: {}, headers: headers, as: :json
            end

            context 'and number of templates does not exceeds the page size' do
              let(:size) { 3 }

              it 'returns correct number of templates in the response' do
                expect(response.parsed_body['content'].count).to eq(size)
              end

              it 'returns correct templates in the response' do
                content = response.parsed_body['content']
                expect(content.map{|t| t['id']}).to match_array(@email_templates.first(size).map(&:id))
              end

              it 'returns correct pagination details in the reponse' do
                json = response.parsed_body
                expect(json['page']['no']).to eq(page)
                expect(json['page']['size']).to eq(size)

                expect(json['totalElements']).to eq(3)
                expect(json['totalPages']).to eq(1)
                expect(json['first']).to eq(true)
                expect(json['last']).to eq(true)
              end
            end

            context 'and number of templates exceeds the page size' do
              let(:size) { 2 }

              it 'returns correct number of templates in the response' do
                expect(response.parsed_body['content'].count).to eq(size)
              end

              it 'returns correct templates in the response' do
                content = response.parsed_body['content']
                expect(content.map{|t| t['id']}).to match_array(@email_templates.first(size).map(&:id))
              end

              it 'returns correct pagination details in the reponse' do
                json = response.parsed_body
                expect(json['page']['no']).to eq(page)
                expect(json['page']['size']).to eq(size)

                expect(json['totalElements']).to eq(3)
                expect(json['totalPages']).to eq(2)
                expect(json['first']).to eq(true)
                expect(json['last']).to eq(false)
              end
            end
          end

          context 'when page number is other than 1' do
            let(:page) { 2 }

            context 'and number of templates does not exceed the page size' do
              let(:size) { 2 }

              before do
                @email_templates = create_list(:email_template, 4, tenant_id: user.tenant_id, created_by: user)
                post "/v1/email-templates/search?page=#{page}&size=#{size}", params: {}, headers: headers, as: :json
              end

              it 'returns correct number of templates in the response' do
                expect(response.parsed_body['content'].count).to eq(size)
              end

              it 'returns correct templates in the response' do
                content = response.parsed_body['content']
                expect(content.map{|t| t['id']}).to match_array([@email_templates[2].id, @email_templates[3].id])
              end

              it 'returns correct pagination details in the reponse' do
                json = response.parsed_body
                expect(json['page']['no']).to eq(page)
                expect(json['page']['size']).to eq(size)

                expect(json['totalElements']).to eq(4)
                expect(json['totalPages']).to eq(2)
                expect(json['first']).to eq(false)
                expect(json['last']).to eq(true)
              end
            end

            context 'and number of templates exceeds the page size' do
              let(:size) { 2 }

              before do
                @email_templates = create_list(:email_template, 5, tenant_id: user.tenant_id, created_by: user)
                post "/v1/email-templates/search?page=#{page}&size=#{size}", params: {}, headers: headers, as: :json
              end

              it 'returns correct number of templates in the response' do
                expect(response.parsed_body['content'].count).to eq(size)
              end

              it 'returns correct templates in the response' do
                content = response.parsed_body['content']
                expect(content.map{|t| t['id']}).to match_array([@email_templates[2].id, @email_templates[3].id])
              end

              it 'returns correct pagination details in the reponse' do
                json = response.parsed_body
                expect(json['page']['no']).to eq(page)
                expect(json['page']['size']).to eq(size)

                expect(json['totalElements']).to eq(5)
                expect(json['totalPages']).to eq(3)
                expect(json['first']).to eq(false)
                expect(json['last']).to eq(false)
              end
            end
          end
        end

        context 'when filter parameters are applied' do
          context 'when "category" filter is applied' do
            let(:valid_rule)     { { 'type': 'string', 'field': 'category', 'operator': 'equal', 'value': 'lead'} }
            let(:request_params) { { "jsonRule": { "condition": 'AND', "rules": [valid_rule] }} }

            before do
              @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
              @email_templates << create_list(:email_template, 3, category: 'contact', tenant_id: user.tenant_id, created_by: user)
              @email_templates.flatten!
              post "/v1/email-templates/search?", params: request_params, headers: headers, as: :json
            end

            it 'returns correct email templates in the response' do
              expect(response.parsed_body['content'].count).to eq(3)
              expect(response.parsed_body['content'].map{|t| t['category']}.uniq).to match_array(['lead'])
            end
          end

          context 'when "name" filter is applied' do
            let(:valid_rule)     { { 'type': 'string', 'field': 'name', 'operator': 'begins_with', 'value': 'welc'} }
            let(:request_params) { { "jsonRule": { "condition": 'AND', "rules": [valid_rule] }} }

            before do
              @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
              @email_templates << create_list(:email_template, 1, name: 'Welcome template', tenant_id: user.tenant_id, created_by: user)
              @email_templates.flatten!
              post "/v1/email-templates/search?", params: request_params, headers: headers, as: :json
            end

            it 'returns correct email templates in the response' do
              expect(response.parsed_body['content'].count).to eq(1)
              expect(response.parsed_body['content'].first['id']).to eq(@email_templates.last.id)
            end
          end

          context 'when "status" filter is applied' do
            let(:valid_rule)     { { 'type': 'boolean', 'field': 'active', 'operator': 'equal', 'value': false} }
            let(:request_params) { { "jsonRule": { "condition": 'AND', "rules": [valid_rule] }} }

            before do
              @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
              @email_templates << create_list(:email_template, 1, active: false, tenant_id: user.tenant_id, created_by: user)
              @email_templates.flatten!
              post "/v1/email-templates/search?", params: request_params, headers: headers, as: :json
            end

            it 'returns correct email templates in the response' do
              expect(response.parsed_body['content'].count).to eq(1)
              expect(response.parsed_body['content'].first['id']).to eq(@email_templates.last.id)
            end
          end

          context 'when "status" and "name" filters are applied' do
            let(:valid_rule_1)     { { 'type': 'boolean', 'field': 'active', 'operator': 'equal', 'value': false} }
            let(:valid_rule_2)     { { 'type': 'string', 'field': 'name', 'operator': 'begins_with', 'value': 'welc'} }
            let(:request_params) { { "jsonRule": { "condition": 'AND', "rules": [valid_rule_1, valid_rule_2] }} }

            before do
              @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
              @email_templates << create(:email_template, active: false, name: 'Welcome template', tenant_id: user.tenant_id, created_by: user)
              post "/v1/email-templates/search?", params: request_params, headers: headers, as: :json
            end

            it 'returns correct email templates in the response' do
              expect(response.parsed_body['content'].count).to eq(1)
              expect(response.parsed_body['content'].first['id']).to eq(@email_templates.last.id)
            end
          end
        end
      end
    end

    context 'when email template read permission are not present' do
      let(:valid_auth_token) { build(:auth_token, :without_email_template_read_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }

      before do
        @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
        post '/v1/email-templates/search', params: {}, headers: headers, as: :json
      end

      it 'returns error code in response' do
        expect(response.parsed_body['errorCode']).to match(ErrorCode.read_email_template_not_allowed)
      end
    end

    context "when email template update permission is not present" do
      let(:valid_auth_token) { build(:auth_token, :without_email_template_update_and_with_read_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }

      before do
        @email_templates = create_list(:email_template, 2, tenant_id: user.tenant_id, created_by: user)
        post '/v1/email-templates/search', params: {}, headers: headers, as: :json
      end

      it 'returns all the expected fields and values in the response' do
        content = response.parsed_body['content']
        expect(content.first.keys).to match_array(['id', 'name', 'category', 'createdBy', 'status', 'createdAt', 'updatedAt', 'updatedBy', 'recordActions'])
        expect(content.first['recordActions']).to eq({"update"=>false, "read"=>true})
        expect(content.second['recordActions']).to eq({"update"=>false, "read"=>true})
      end
    end
  end

  describe '#variables' do
    context 'with valid parameters' do

      before do
        stub_request(:get, SERVICE_DEAL + "/v1/deals/fields?custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
              "Authorization" => "Bearer #{ valid_auth_token.token }"
            }).
            to_return(status: 200, body: file_fixture('deal-fields.json'), headers: {})
            get '/v1/email-templates/variables?category=deal', headers: headers, as: :json
      end

      it 'returns data for deal' do
        content = response.parsed_body['variables']
        res = content.select { |field| field['entity'] == 'deal' }.first
        expect(res["displayName"]).to eq("Name")
        expect(res["internalName"]).to eq("name")
        expect(res["standard"]).to eq true
      end
    end
  end

  describe '#show' do
    context 'with valid parameters' do
      let(:valid_auth_token) { build(:auth_token, :with_email_template_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
      before do
        @email_template = create(:email_template, created_by: user, tenant_id: user.tenant_id, category: 'lead')
        stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?custom-only=false&entityType=lead&page=0&size=100&sort=createdAt,asc").
          with(
            headers: {
              'Authorization' => "Bearer #{ valid_auth_token.token }"
            }).
            to_return(status: 200, body: file_fixture('lead-fields.json'), headers: {})

            get "/v1/email-templates/#{@email_template.id}", headers: headers, as: :json
      end

      it 'returns data for template' do
        template = response.parsed_body
        expect(template["id"]).to eq(@email_template.id)
      end
    end
  end

  describe '#details_with_entity' do
    context 'with valid parameters' do
      let!(:valid_auth_token) { build(:auth_token, :with_email_template_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
      let(:user_data){ file_fixture('user-profile-response.json').read }
      let(:lead_fields){ file_fixture('lead-fields.json').read }
      let(:standard_picklist) { file_fixture('standard-picklist.json').read }

      before do
        @email_template = create(:email_template, subject: "This is {{firstName}}", body: "This is {{firstName}} in body", created_by: user, tenant_id: user.tenant_id)

        stub_request(:get, SERVICE_SALES + "/v1/leads/1").
          with(
            headers: {
              "Authorization" => "Bearer #{valid_auth_token.token}"
            }).
          to_return(status: 200, body: { "firstName": 'name', 'conditionalVar': 'data'}.to_json, headers: {})

        stub_request(:get, SERVICE_IAM + "/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{valid_auth_token.token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }).
          to_return(status: 200, body: user_data, headers: {})

        stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?entityType=lead&custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
            "Authorization" => "Bearer #{valid_auth_token.token}"
            }).
          to_return(status: 200, body: lead_fields, headers: {})

        stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
          with(
            headers: {
            "Authorization" => "Bearer #{valid_auth_token.token}"
            }).
          to_return(status: 200, body: standard_picklist, headers: {})

        get "/v1/email-templates/#{@email_template.id}/lead/1", headers: headers, as: :json
      end

      it 'returns data for template' do
        template = response.parsed_body
        expect(template["id"]).to eq(@email_template.id)
        expect(template["subject"]).to eq "This is name"
        expect(template["body"]).to eq "This is name in body"
      end
    end
  end

  describe '#activates template' do
    let(:valid_auth_token) { build(:auth_token, :with_email_template_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    context 'with valid parameters' do
      before do
        @email_template = create(:email_template, active: false, created_by: user, tenant_id: user.tenant_id)
        post "/v1/email-templates/#{@email_template.id}/activate", headers: headers, as: :json
      end

      it 'returns data for template' do
        expect(response.status).to eq(204)
        expect(@email_template.reload.active).to eq true
        expect(@email_template.updated_by).to eq(user)
      end
    end
  end

  describe '#deactivates template' do
    context 'with valid parameters' do
      let(:valid_auth_token) { build(:auth_token, :with_email_template_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
      before do
        @email_template = create(:email_template, active: true, created_by: user, tenant_id: user.tenant_id)
        post "/v1/email-templates/#{@email_template.id}/deactivate", headers: headers, as: :json
      end

      it 'returns data for template' do
        expect(response.status).to eq(204)
        expect(@email_template.reload.active).to eq false
      end
    end
  end

  describe '#download_attachment' do
    before do
      @resource = instance_double(Aws::S3::Resource)
      @bucket = instance_double(Aws::S3::Bucket)
      @obj = instance_double(Aws::S3::Object)
      @file_name = 'tenant_14/user_12/121_old_file_123123123.jpg'

      allow(Aws::S3::Resource).to receive(:new).and_return(@resource)
      allow(@resource).to receive(:bucket).with(S3_EMAIL_TEMPLATE_BUCKET).and_return(@bucket)
      allow(@bucket).to receive(:object).with(@file_name).and_return(@obj)
      allow(@obj).to receive(:presigned_url).and_return('https://www.aws.com/files/78682793829.jpg')

      @email_template = create(:email_template, created_by: user, tenant_id: user.tenant_id)
      @attachment = create(:template_attachment, id: 77, email_template: @email_template, file_name: @file_name)
    end

    context 'When attachment exists' do
      it 'should get presigned URL of attachment' do
        get "/v1/email-templates/#{@email_template.id}/attachments/#{@attachment.id}", headers: headers, as: :json
        expect(response.status).to eq(200)
        expect(JSON.parse(response.body)).to eq({ "file_name" => 'old_file.jpg', "url" => 'https://www.aws.com/files/78682793829.jpg' })
      end
    end

    context 'Attachment does not exists' do
      it 'should raise an exception for attachment not found' do
        get "/v1/email-templates/#{@email_template.id}/attachments/11", headers: headers, as: :json
        expect(response.status).to eq(404)
        expect(response.parsed_body['errorCode']).to match(ErrorCode.not_found)
      end
    end
  end

   describe '#lookup' do
    context 'when only email template read permission is present' do
      before do
        @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
        @email_templates << create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: another_user)
        @email_templates.flatten!
        get '/v1/email-templates/lookup', headers: headers, as: :json
      end

      it "returns only templates created by user" do
        content = response.parsed_body['content']
        expect(content.count).to eq(3)
        expect(content.map{|t| t['id']}).to match_array(@email_templates.first(3).map(&:id))
      end
    end

    context 'when email template read all permissions are present' do
      let(:valid_auth_token) { build(:auth_token, :with_email_template_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }

      context 'with valid parameters' do
        context "when sort parameters are not specified" do
          before do
            @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
            get '/v1/email-templates/lookup', headers: headers, as: :json
          end

          it 'returns correct number of templates' do
            content = response.parsed_body['content']
            expect(content.count).to eq(3)
          end

          it 'returns all the expected fields in the response' do
            content = response.parsed_body['content']
            expect(content.first.keys).to match_array(['id', 'name'])
          end

          it 'returns correct emails templates details in the response' do
            content = response.parsed_body['content']
            email_template = content.first
            template = @email_templates.first

            expect(email_template['name']).to eq(template.name)
            expect(email_template['id']).to eq(template.id)
          end
        end

        context 'when query parameters are present' do
          context 'when "category" params is present' do
            before do
              @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
              @email_templates << create_list(:email_template, 3, category: 'contact', tenant_id: user.tenant_id, created_by: user)
              @email_templates.flatten!
              get "/v1/email-templates/lookup?category=lead", headers: headers, as: :json
            end

            it 'returns correct email templates in the response' do
              expect(response.parsed_body['content'].count).to eq(3)
            end
          end

          context 'when "q" is present in query params' do
            before do
              @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
              @email_templates << create_list(:email_template, 1, name: 'Welcome template', tenant_id: user.tenant_id, created_by: user)
              @email_templates.flatten!
              get "/v1/email-templates/lookup?q=welc", headers: headers, as: :json
            end

            it 'returns correct email templates in the response' do
              expect(response.parsed_body['content'].count).to eq(1)
              expect(response.parsed_body['content'].first['id']).to eq(@email_templates.last.id)
            end
          end
        end
      end
    end

    context 'when email template read permission are not present' do
      let(:valid_auth_token) { build(:auth_token, :without_email_template_read_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }

      before do
        @email_templates = create_list(:email_template, 3, tenant_id: user.tenant_id, created_by: user)
        get '/v1/email-templates/lookup', headers: headers, as: :json
      end

      it 'returns error code in response' do
        expect(response.parsed_body['errorCode']).to match(ErrorCode.read_email_template_not_allowed)
      end
    end

    context "when email template update permission is not present" do
      let(:valid_auth_token) { build(:auth_token, :without_email_template_update_and_with_read_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }

      before do
        @email_templates = create_list(:email_template, 2, tenant_id: user.tenant_id, created_by: user)
        get '/v1/email-templates/lookup', headers: headers, as: :json
      end

      it 'returns all the expected fields and values in the response' do
        content = response.parsed_body['content']
        expect(content.first.keys).to match_array(['id', 'name'])
      end
    end
  end
end
