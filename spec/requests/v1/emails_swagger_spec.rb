require 'swagger_helper'
require 'google/apis/gmail_v1'
require 'bunny-mock'

RSpec.describe 'Email API', type: :request do
  let(:user)              { create(:user)}
  let(:valid_auth_token)  { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)         { ParseToken.call(valid_auth_token.token).result }
  let(:Authorization)     { valid_auth_token.token }
  let(:connected_account) { create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: user) }
  let(:thread)            { create(:email_thread, owner: user, tenant_id: user.tenant_id) }
  let(:email)             { build(:email, connected_account: connected_account, email_thread_id: thread.id, tenant_id: user.tenant_id, owner: user) }

  path '/v1/emails' do
    post 'Creates an Email' do
      tags 'Emails'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :body
      parameter name: :email, in: :body, schema: {
        type: :object,
        required: %w[to relatedTo],
        properties: {
          body: {
            type: :string
          },
          subject: {
            type: :string
          },
          to: {
            type: :array,
            items: {
              type: :object,
              properties: {
                entity: {
                  type: :string
                },
                id: {
                  type: :string
                },
                name: {
                  type: :string
                },
                email: {
                  type: :string
                },
              },
              required: [:entity, :id, :name, :email]
            }
          },
          cc: {
            type: :array,
            items: {
              type: :object,
              properties: {
                entity: {
                  type: :string
                },
                id: {
                  type: :string
                },
                name: {
                  type: :string
                },
                email: {
                  type: :string
                },
              },
              required: [:entity, :id, :name, :email]
            }
          },
          bcc: {
            type: :array,
            items: {
              type: :object,
              properties: {
                entity: {
                  type: :string
                },
                id: {
                  type: :string
                },
                name: {
                  type: :string
                },
                email: {
                  type: :string
                },
              },
              required: [:entity, :id, :name, :email]
            }
          },
          relatedTo: { type: :object }
        }
      }

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      before do
        connection = BunnyMock.new
        @channel = connection.start.channel
        @exchange = @channel.topic EMAIL_EXCHANGE
        @queue = @channel.queue EMAIL_CREATED
        @queue.bind @exchange, routing_key: EMAIL_CREATED
        allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)
      end

      response '201', 'email created' do
        before do
          header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
          payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
          return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
          expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
          expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
          setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
          allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', connected_account.email).and_return(setting)
          user_lookup = build(:look_up, entity_type: LOOKUP_USER, entity_id: user.id, tenant_id: user.tenant_id, name: user.name)
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            user_lookup
          ])
        end

        run_test!
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end
    end
  end

  path '/v1/emails/{id}/mark_as_read' do
    post 'Mark email as read' do
      tags 'Emails'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :id, in: :path, type: :string
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '200', 'Email marked as read' do
        let(:id) { email.save; email.id }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { email.save; email.id }

        run_test!
      end

      response '404', 'Email Not Found' do
        let(:id) { email.save; (email.id  + 1) }

        run_test!
      end

      response '422', 'Invalid Request' do
        let(:id) { email.save; email.id }

        before do
          id
          expect_any_instance_of(Email).to receive(:save).and_return(false)
        end

        run_test!
      end
    end
  end

  path '/v1/emails/{id}/mark_as_unread' do
    post 'Mark email as un-read' do
      tags 'Emails'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :id, in: :path, type: :string
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '200', 'Email mark as unread' do
        let(:id) { email.save; email.id }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { email.save; email.id }

        run_test!
      end

      response '404', 'Email Not Found' do
        let(:id) { email.save; (email.id  + 1) }

        run_test!
      end

      response '422', 'Invalid Request' do
        let(:id) { email.save; email.id }

        before do
          id
          expect_any_instance_of(Email).to receive(:save).and_return(false)
        end

        run_test!
      end
    end
  end

  path '/v1/emails/{id}/forward' do
    post 'Mark email as read' do
      tags 'Emails'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :id, in: :path, type: :string
      parameter name: :body
      parameter name: :email, in: :body, schema: {
          type: :object,
          required: %w[to relatedTo],
          properties: {
              body: {
                  type: :string
              },
              subject: {
                  type: :string
              },
              to: {
                  type: :array,
                  items: {
                      type: :object,
                      properties: {
                          entity: {
                              type: :string
                          },
                          id: {
                              type: :string
                          },
                          name: {
                              type: :string
                          },
                          email: {
                              type: :string
                          },
                      },
                      required: [:entity, :id, :name, :email]
                  }
              },
              cc: {
                  type: :array,
                  items: {
                      type: :object,
                      properties: {
                          entity: {
                              type: :string
                          },
                          id: {
                              type: :string
                          },
                          name: {
                              type: :string
                          },
                          email: {
                              type: :string
                          },
                      },
                      required: [:entity, :id, :name, :email]
                  }
              },
              bcc: {
                  type: :array,
                  items: {
                      type: :object,
                      properties: {
                          entity: {
                              type: :string
                          },
                          id: {
                              type: :string
                          },
                          name: {
                              type: :string
                          },
                          email: {
                              type: :string
                          },
                      },
                      required: [:entity, :id, :name, :email]
                  }
              },
              relatedTo: { type: :object }
          }
      }

      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'email created' do
        before do
          header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
          payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
          return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID')
          expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
          expect_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
          user_lookup = build(:look_up, entity_type: LOOKUP_USER, entity_id: user.id, tenant_id: user.tenant_id, name: user.name)
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
                                                                                       user_lookup
                                                                                   ])
        end

        pending('intentional')
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        pending('intentional')
      end
    end
  end

  path '/v1/emails/{id}/attachments/{attachment_id}' do
    get 'Get attachment url' do
      tags 'Emails'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :id, in: :path, type: :string
      parameter name: :attachment_id, in: :path, type: :string
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'Attachment' do
        pending('intentional')
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        pending('intentional')
      end
    end
  end

  path '/v1/emails/{id}' do
    delete 'Delete Email' do
      tags 'Emails'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :string
      parameter(
        {
          in: :header,
          type: :string,
          name: :Authorization,
          required: true,
          description: 'Client token'
        }
      )

      let(:email_thread) { create(:email_thread, tenant_id: user.tenant_id) }
      let(:id) { create(:email, owner: user, tenant_id: user.tenant_id, email_thread: email_thread).id }

      response '200', 'Email Deleted' do
        before do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction))
          expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailDeletedWorkflowV2))

          expect(PublishUsageJob).to receive(:perform_later).with(user.tenant_id)
        end

        run_test!
      end

      response '401', 'Unauthorized' do
        before { expect_any_instance_of(Auth::Data).to receive(:can_delete_emails?).and_return(false) }

        run_test!
      end

      response '404', 'Email Not Found' do
        let(:id) { 10000 }

        run_test!
      end

      response '422', 'Invalid Request' do
        before { allow_any_instance_of(Email).to receive(:destroy!).and_raise(StandardError) }

        run_test!
      end
    end
  end

  path '/v1/email-threads/{id}/emails' do
    get 'Get attachment url' do
      tags 'Emails'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :id, in: :path, type: :string
      parameter name: :entity_id, in: :query, type: :string
      parameter name: :entity_type, in: :query, type: :string
      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'Attachment' do
        pending('intentional')
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        pending('intentional')
      end
    end
  end

  path '/v1/emails/search/smart-list/count' do
    get 'Get records count of smart-list' do
      tags 'Emails'
      consumes 'application/json'
      security [bearerAuth: []]

      let(:id) { '123456' }

      parameter name: :id, in: :query, type: :string
      parameter(
        {
          in: :header,
          type: :string,
          name: :Authorization,
          required: true,
          description: 'Client token'
        }
      )

      response '200', 'Smart List Records Count' do
        let(:smart_list_response) do
          [
            {
              "createdAt": '2023-09-26T03:27:53.609+0000',
              "updatedAt": '2023-09-26T03:27:53.609+0000',
              "createdBy": 3779,
              "updatedBy": 3779,
              "recordActions": {
                "read": true, "update": true, "delete": true, "email": false, "call": false,
                "sms": false, "task": false, "note": false, "meeting": false,
                "document": false, "deleteAll": false, "quotation": false, "reshare": false
              },
              "metaData": nil,
              "id": 108_998,
              "entityType": 'EMAIL',
              "name": 'Today’s Emails',
              "description": 'Incoming emails received Today',
              "searchRequest": {
                "fields": nil,
                "jsonRule": {
                  "id": nil, "field": nil, "type": nil, "input": nil, "operator": nil,
                  "value": nil, "data": nil, "condition": 'AND', "not": nil,
                  "rules": [
                    {
                      "id": 'direction', "field": 'direction', "type": 'string',
                      "input": 'string', "operator": 'equal', "value": 'received', "data": nil,
                      "condition": nil, "not": nil, "rules": nil, "group": false
                    },
                    {
                      "id": 'createdAt', "field": 'createdAt', "type": 'date', "input": nil,
                      "operator": 'today', "value": '', "data": nil, "condition": nil, "not": nil,
                      "rules": nil, "timeZone": 'Etc/GMT+12', "group": false
                    }
                  ],
                  "group": true
                }
              },
              "systemDefault": true, "sort": 'updatedAt,desc', "size": 10
            }
          ]
        end

        before do
          stub_request(:get, "#{SERVICE_SEARCH}/v1/search-lists/email?id=123456")
            .with(headers: { 'Authorization' => "Bearer #{valid_auth_token.token}" })
            .to_return(status: 200, body: smart_list_response.to_json, headers: {})

          [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity|
            stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/EMAIL")
              .with(
                headers: { Authorization: "Bearer #{valid_auth_token.token}" }
              )
              .to_return(
                status: 200,
                body: {
                  accessByOwners: { '3779' => { email: true } },
                  accessByRecords: { '3779' => { read: true, email: true } }
                }.to_json
              )
          end
        end

        run_test!
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end
    end
  end
end
