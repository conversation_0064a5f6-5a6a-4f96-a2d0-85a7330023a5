# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::FieldsController, type: :request do
  let(:user)             { create(:user, tenant: create(:tenant) )}
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let(:headers)          { valid_headers(valid_auth_token) }

  describe '#index' do
    context 'email fields json' do
      let(:email_fields) { file_fixture('email-fields.json').read }

      before { get '/v1/emails/fields', headers: headers }

      it 'returns all fields as json' do
        fields  = response.parsed_body

        expect(fields).to eq(JSON.parse(email_fields))
      end
    end
  end
end
