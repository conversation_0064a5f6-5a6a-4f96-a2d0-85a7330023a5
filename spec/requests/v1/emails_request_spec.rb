require 'rails_helper'
require 'google/apis/gmail_v1'
require 'ostruct'
require 'bunny-mock'

RSpec.describe 'Emails Management', type: :request do
  let!(:user)             { create(:user, tenant: create(:tenant) )}
  let!(:another_user)     { create(:user, tenant_id: user.tenant_id)}
  let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let!(:headers)          { valid_headers(valid_auth_token) }
  let(:connected_account) { create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: user, tenant: user.tenant) }
  let(:thread)            { create(:email_thread, owner: user, tenant_id: user.tenant_id) }
  let(:email)             { build(:email, tenant_id: user.tenant_id, owner: user, connected_account: connected_account, read_by: [], email_thread_id: thread.id) }
  let(:invalid_email)     { build(:email, connected_account: nil, email_thread_id: thread.id) }

  before do
    connection = BunnyMock.new
    channel = connection.start.channel
    exchange = channel.topic EMAIL_EXCHANGE

    queue = channel.queue "email.update.lead.metaInfo"
    queue.bind exchange, routing_key: "email.update.lead.metaInfo"

    queue = channel.queue "email.update.deal.metaInfo"
    queue.bind exchange, routing_key: "email.update.deal.metaInfo"
    allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(queue)
  end

  describe '#create' do
    before do
      user_lookup = build(:look_up, entity_type: LOOKUP_USER, entity_id: user.id, tenant_id: user.tenant_id, name: user.name)
      allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
        user_lookup
      ])
      header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
      payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
      return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
      return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID')
      Google::Apis::GmailV1::GmailService.any_instance.stub(:send_user_message).and_return(return_message)
    end

    context 'with valid parameters' do
      before do
        setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
        allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', connected_account.email).and_return(setting)
      end

      it 'creates a new Email' do
        expect {
          post '/v1/emails',
          params: { email: email }, headers: headers, as: :json
        }.to change(Email, :count).by(1)
      end

      it 'renders a JSON response with the new email' do
        post '/v1/emails',
          params: { email: email }, headers: headers, as: :json
        expect(response).to have_http_status(:created)
        expect(response.content_type).to eq('application/json; charset=utf-8')
      end
    end

    context 'with invalid parameters' do
      it 'does not create a new Email' do
        expect {
          post '/v1/emails',
          params: { email: invalid_email }, headers: headers, as: :json
        }.to change(Email, :count).by(0)
      end

      it 'renders a JSON response with errors for the new email' do
        post '/v1/emails',
          params: { email: invalid_email }, headers: headers, as: :json
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.content_type).to eq('application/json; charset=utf-8')
      end
    end
  end

  describe '#mark_as_read' do
    before do
      user_lookup = build(:look_up, entity_type: LOOKUP_USER, entity_id: user.id, tenant_id: user.tenant_id, name: user.name)
      thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
      @email = create(:email, owner: user, tenant_id: user.tenant_id, connected_account: connected_account, email_thread_id: thread.id)
      @email.related_to << user_lookup
      @email.save!
    end

    it 'should mark email as read' do
      post "/v1/emails/#{@email.id}/mark_as_read", headers: headers, as: :json
      expect(response.status).to eq(200)
      expect(@email.reload.read?).to be_truthy
    end

    it 'should return response with thread read true' do
      post "/v1/emails/#{@email.id}/mark_as_read", headers: headers, as: :json

      expect(response.status).to eq(200)
      expect(response.parsed_body['isThreadMarkedAsRead']).to be_truthy
    end

    it 'should mark email as unread' do
      post "/v1/emails/#{@email.id}/mark_as_unread", headers: headers, as: :json
      expect(response.status).to eq(200)
      expect(@email.read?).to be_falsey
    end

    it 'should return response with thread read false' do
      post "/v1/emails/#{@email.id}/mark_as_unread", headers: headers, as: :json

      expect(response.status).to eq(200)
      expect(response.parsed_body['isThreadMarkedAsRead']).to be_falsey
    end
  end

  describe '#index' do
    before do
      allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => {} })
    end

    context 'when request is invalid' do
      before { get '/v1/email-threads/1/emails', headers: invalid_headers }

      it 'returns a failure message' do
        expect(response.parsed_body['errorCode']).to match('********')
      end
    end

    context 'when the request is valid' do
      before do
        @thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
        @emails = create_list(:email, 3, owner: another_user, tenant_id: user.tenant_id, email_thread_id: @thread.id, connected_account: connected_account) do |email, index|
          email.created_at = (10 - index).days.ago
          email.read_by << user.id
          email.save!
        end
        @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: user.id)
        @cc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)
        @bcc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)

        @emails.each { |e| e.to_recipients << @to_lookup }
        @emails.each { |e| e.cc_recipients << @cc_lookup }
        @emails.each { |e| e.bcc_recipients << @bcc_lookup }

        @tracked_email = @emails.first
        create_list(:email_track_log, 3, email: @tracked_email)
        link = create(:link_mapping, url: 'google.com', email: @tracked_email)
        create_list(:email_link_log, 3, link_mapping: link, email: @tracked_email)

        @emails.each do |e|
          e.related_to << @to_lookup
          e.related_to << @cc_lookup
          e.related_to << @bcc_lookup
        end
      end

      context 'when user has access to the thread' do
        context 'when email thread id is invalid' do
          before { get "/v1/email-threads/#{@thread.id+1}/emails", headers: headers }

          it 'returns error code in the response' do
            expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid)
          end
        end

        context 'when email thread id is valid' do
          before do
            @deal_lookup = build(:look_up, entity_type: LOOKUP_DEAL, entity_id: 1000, tenant_id: user.tenant_id)
            @emails.each { |e| e.related_to << @deal_lookup }
            get "/v1/email-threads/#{@thread.id}/emails", headers: headers
          end

          it 'returns the emails associated with the thread' do
            expect(response.parsed_body['content'].count).to eq(3)
          end

          it 'returns the correct fields in the response' do
            emails = response.parsed_body['content']
            expect(emails.first.keys).to match_array(%w[messageId from to cc bcc associatedTo read subject body sentAt attachments ownerId status trackingEnabled openedAt linksClickedAt recordActions])
          end

          it 'returns emails in the correct sequence' do
            emails = response.parsed_body['content']
            expect(emails.map{|e| e['messageId']}).to eq(@emails.map(&:id).reverse)
          end

          it 'returns correct data in the "to", "cc" and "bcc" in the response' do
            emails = response.parsed_body['content']

            expect(emails.first['to'].count).to eq(1)
            expect(emails.first['to'].first['email']).to eq(@to_lookup.email)

            expect(emails.first['cc'].count).to eq(1)
            expect(emails.first['cc'].first['email']).to eq(@cc_lookup.email)

            expect(emails.first['bcc'].count).to eq(1)
            expect(emails.first['bcc'].first['email']).to eq(@bcc_lookup.email)
          end

          it 'returns data of tracking' do
            emails = response.parsed_body['content']
            email = emails.last
            expect(email['messageId']).to eq @tracked_email.id
            expect(email['openedAt'].count).to eq 3
          end

          it 'returns data of link clicks' do
            emails = response.parsed_body['content']
            email = emails.last
            expect(email['messageId']).to eq @tracked_email.id
            details = email['linksClickedAt'][0]
            expect(details['url']).to eq 'google.com'
            expect(details['clickedAt'].count).to eq 3
          end

          it 'returns associated entities in response' do
            emails = response.parsed_body['content']

            emails.each do |email|
              expect(email['associatedTo']).to match_array(
                [@to_lookup, @cc_lookup, @bcc_lookup, @deal_lookup].map do |look_up|
                  hash = {
                    'id' => look_up.entity_id,
                    'name' => look_up.name,
                    'entity' => look_up.entity_type
                  }

                  if [LOOKUP_LEAD, LOOKUP_CONTACT].include?(look_up.entity_type)
                    hash.merge('email' => look_up.email)
                  else
                    hash
                  end
                end
              )
            end
          end

          it 'returns source thread id in the response' do
            emails_response = response.parsed_body
            expect(emails_response['sourceThreadId']).to be_present
          end
        end

        context 'record actions' do
          before do
            @to_lookup.update(owner_id: another_user.id)
          end

          context 'when user can read email' do
            context 'when user is email owner & sender' do
              before do
                @to_lookup.update(owner_id: user.id)
                @emails.each do |email|
                  email.update(owner: user)
                  email.sender.update(entity: "#{LOOKUP_USER}_#{user.id}")
                  email.update(to: email.to + [connected_account.email])
                end
                get "/v1/email-threads/#{@thread.id}/emails", headers: headers
              end

              it 'returns true on all record actions' do
                email = response.parsed_body['content'].first

                expect(email['recordActions']).to eq(
                  {
                    read: true,
                    reply: true,
                    forward: true,
                    delete: true
                  }.with_indifferent_access
                )
              end
            end

            context 'when user is conversation owner & sender' do
              before do
                @to_lookup.update(owner_id: user.id)
                @emails.each do |email|
                  email.email_thread.update(owner: user)
                  email.sender.update(entity: "#{LOOKUP_USER}_#{user.id}")
                  email.update(to: email.to + [connected_account.email])
                end
                get "/v1/email-threads/#{@thread.id}/emails", headers: headers
              end

              it 'returns true on read' do
                email = response.parsed_body['content'].first

                expect(email['recordActions']).to eq(
                  {
                    read: true,
                    reply: true,
                    forward: true,
                    delete: true
                  }.with_indifferent_access
                )
              end
            end

            context 'when user is participant in email' do
              before do
                @to_lookup.update(owner_id: user.id)
                @emails.each do |email|
                  email.sender.update(entity: "#{LOOKUP_USER}_#{user.id}")
                  email.update(cc: email.cc + [connected_account.email])
                end
                get "/v1/email-threads/#{@thread.id}/emails", headers: headers
              end

              it 'returns true on read' do
                email = response.parsed_body['content'].first

                expect(email['recordActions']).to eq(
                  {
                    read: true,
                    reply: true,
                    forward: true,
                    delete: false
                  }.with_indifferent_access
                )
              end
            end

            context 'when user is one of the entity owner' do
              before do
                @to_lookup.update(owner_id: user.id)
                get "/v1/email-threads/#{@thread.id}/emails", headers: headers
              end

              it 'returns true on read' do
                email = response.parsed_body['content'].first

                expect(email['recordActions']).to eq(
                  {
                    read: true,
                    reply: false,
                    forward: true,
                    delete: false
                  }.with_indifferent_access
                )
              end
            end

            context 'when entity is shared with user' do
              before do
                expect_any_instance_of(SharedAccess).to receive_message_chain(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => { @to_lookup.entity_id => {} } })
                get "/v1/email-threads/#{@thread.id}/emails", headers: headers
              end

              it 'returns true on read' do
                email = response.parsed_body['content'].first

                expect(email['recordActions']).to eq(
                  {
                    read: true,
                    reply: false,
                    forward: true,
                    delete: false
                  }.with_indifferent_access
                )
              end
            end
          end

          context 'when user cannot reply to email' do
            before do
              @to_lookup.update(owner_id: user.id)
              @emails.each do |email|
                email.update(owner: user)
              end
              get "/v1/email-threads/#{@thread.id}/emails", headers: headers
            end

            it 'returns false on reply action' do
              email = response.parsed_body['content'].first

              expect(email['recordActions']).to eq(
                {
                  read: true,
                  reply: false,
                  forward: true,
                  delete: true
                }.with_indifferent_access
              )
            end
          end

          context 'when user cannot forward email' do
            before do
              @to_lookup.update(owner_id: another_user.id)
              allow_any_instance_of(Auth::Data).to receive(:can_read_all_emails?).and_return(true)
              get "/v1/email-threads/#{@thread.id}/emails", headers: headers
            end

            it 'returns false on forward action' do
              email = response.parsed_body['content'].first

              expect(email['recordActions']).to eq(
                {
                  read: true,
                  reply: false,
                  forward: false,
                  delete: false
                }.with_indifferent_access
              )
            end
          end

          context 'when user cannot delete email' do
            before do
              @to_lookup.update(owner_id: user.id)
              @emails.each do |email|
                email.sender.update(entity: "#{LOOKUP_USER}_#{user.id}")
                email.update(bcc: email.bcc + [connected_account.email])
              end
              get "/v1/email-threads/#{@thread.id}/emails", headers: headers
            end

            it 'returns false on delete action' do
              email = response.parsed_body['content'].first

              expect(email['recordActions']).to eq(
                {
                  read: true,
                  reply: true,
                  forward: true,
                  delete: false
                }.with_indifferent_access
              )
            end
          end
        end

        context 'when pagination is applied' do
          context 'when page number is 1' do
            before { get "/v1/email-threads/#{@thread.id}/emails?#{page_params}", headers: headers }

            context 'and the number of emails does not exceeds the page size' do
              let(:page_params) { 'page=1&size=3' }

              it 'returns correct number of records' do
                expect(response.parsed_body['content'].count).to eq(3)
              end

              it 'returns the emails associated with the thread' do
                emails = response.parsed_body['content']
                expect(emails.map{|e| e['messageId']}).to eq(@emails.map(&:id).reverse)
              end

              it 'returns correct pagination details in the response' do
                json = response.parsed_body
                expect(json['page']['no']).to eq(1)
                expect(json['page']['size']).to eq(3)

                expect(json['totalElements']).to eq(3)
                expect(json['totalPages']).to eq(1)
                expect(json['first']).to eq(true)
                expect(json['last']).to eq(true)
              end
            end

            context 'and number of emails exceeds the page size' do
              let(:page_params) { 'page=1&size=2' }

              it 'returns correct number of records' do
                expect(response.parsed_body['content'].count).to eq(2)
              end

              it 'returns the emails associated with the thread' do
                emails = response.parsed_body['content']
                expect(emails.map{|e| e['messageId']}).to eq(@emails.last(2).map(&:id).reverse)
              end

              it 'returns correct pagination details in the response' do
                json = response.parsed_body
                expect(json['page']['no']).to eq(1)
                expect(json['page']['size']).to eq(2)

                expect(json['totalElements']).to eq(3)
                expect(json['totalPages']).to eq(2)
                expect(json['first']).to eq(true)
                expect(json['last']).to eq(false)
              end
            end
          end

          context 'when page number is other than 1' do
            context 'and the number of emails does not exceeds the page size' do
              let(:page_params) { 'page=2&size=2' }

              before { get "/v1/email-threads/#{@thread.id}/emails?#{page_params}", headers: headers }

              it 'returns correct number of records' do
                expect(response.parsed_body['content'].count).to eq(1)
              end

              it 'returns the emails associated with the thread' do
                emails = response.parsed_body['content']
                expect(emails.map{|e| e['messageId']}).to eq([@emails[0].id])
              end

              it 'returns correct pagination details in the response' do
                json = response.parsed_body
                expect(json['page']['no']).to eq(2)
                expect(json['page']['size']).to eq(2)

                expect(json['totalElements']).to eq(3)
                expect(json['totalPages']).to eq(2)
                expect(json['first']).to eq(false)
                expect(json['last']).to eq(true)
              end
            end

            context 'and number of elements exceeds the page size' do
              let(:page_params) { 'page=2&size=2' }

              before do
                emails = create_list(:email, 3, owner: another_user, tenant_id: user.tenant_id, email_thread_id: @thread.id) do |email, index|
                  email.created_at = (5 - index).days.ago
                  email.read_by << user.id
                  email.save!
                end

                emails.each { |e| e.to_recipients << @to_lookup }
                emails.each { |e| e.related_to << @to_lookup }
                @emails << emails
                @emails.flatten!

                get "/v1/email-threads/#{@thread.id}/emails?#{page_params}", headers: headers
              end

              it 'returns correct number of records' do
                expect(response.parsed_body['content'].count).to eq(2)
              end

              it 'returns the emails associated with the thread' do
                emails = response.parsed_body['content']
                expect(emails.map{|e| e['messageId']}).to eq([@emails[3].id, @emails[2].id])
              end

              it 'returns correct pagination details in the response' do
                json = response.parsed_body
                expect(json['page']['no']).to eq(2)
                expect(json['page']['size']).to eq(2)

                expect(json['totalElements']).to eq(6)
                expect(json['totalPages']).to eq(3)
                expect(json['first']).to eq(false)
                expect(json['last']).to eq(false)
              end
            end
          end
        end
      end

      context 'when user does not have access to the thread' do
        before do
          @to_lookup.update(owner_id: another_user.id)
        end

        it 'returns error' do
          expect(Rails.logger).to receive(:error).with("Unauthorised: User does not have read on thread. User id #{user.id} Thread id #{@thread.id}")
          get "/v1/email-threads/#{@thread.id}/emails", headers: headers

          expect(response.status).to be(401)
          expect(response.parsed_body['errorCode']).to eq('********')
        end
      end
    end
  end

  describe '#forward' do
    before do
      header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
      payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
      return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)

      email.save
      email.related_to << build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1000, owner_id: user.id, tenant_id: user.tenant_id)
      @email = build(:email, owner: user, tenant_id: user.tenant_id, connected_account: connected_account, email_thread: thread)
    end

    context 'When current user is owner of thread' do
      before do
        setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
        allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', connected_account.email).and_return(setting)
        stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=1000&sort=updatedAt,desc").
          with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}"
            },
            body: {
              fields: %w[id firstName lastName emails ownerId],
              jsonRule:{
                condition: 'AND',
                rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: '9' }],
                valid: true
              }
            }
          ).
          to_return(status: 200, body: file_fixture('lead-search-response.json').read, headers: {})
          allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => {} })
      end

      it 'should forward email with thread id ' do
        email_params = @email.as_json
        email_params[:to] = [{"id": 9,"name":'<EMAIL>',"entity":'lead',"email":'<EMAIL>'}]
        expect {
          post "/v1/emails/#{email.id}/forward",
          params: email_params, headers: headers, as: :json
        }.to change(Email, :count).by(1)
        expect(Email.order(created_at: :desc).first.email_thread_id).to eq(thread.id)
      end
    end

    context 'When current user is NOT owner of thread' do
      before do
        account = create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: another_user)
        setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
        allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', account.email).and_return(setting)
        auth_token = build(:auth_token, user_id: another_user.id, tenant_id: user.tenant_id, username: another_user.name)
        @new_header = valid_headers(auth_token)
        stub_request(:post, "http://localhost:8083/v1/search/lead?page=0&size=1000&sort=updatedAt,desc").
          with(
            headers: {
              Authorization: "Bearer #{auth_token.token}"
            },
            body: {
              fields: %w[id firstName lastName emails ownerId],
              jsonRule:{
                condition: 'AND',
                rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: '9' }],
                valid: true
              }
            }
          ).
          to_return(status: 200, body: file_fixture('lead-search-response.json').read, headers: {})
          allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => { 1000 => {} } })
      end

      it 'should forward email on new Thread' do
        email_params = @email.as_json
        email_params[:to] = [{"id": 9,"name":'<EMAIL>',"entity":'lead',"email":'<EMAIL>'}]
        expect {
          post "/v1/emails/#{email.id}/forward",
          params: email_params, headers: @new_header, as: :json
        }.to change(Email, :count).by(1)
        expect(Email.order(created_at: :desc).first.email_thread_id).to_not eq(thread.id)
      end
    end
  end

  describe '#download_attachment' do
    before do
      @resource = instance_double(Aws::S3::Resource)
      @bucket = instance_double(Aws::S3::Bucket)
      @obj = instance_double(Aws::S3::Object)
      @file_name = 'tenant_14/user_12/121_old_file_123123123.jpg'

      allow(Aws::S3::Resource).to receive(:new).and_return(@resource)
      allow(@resource).to receive(:bucket).with(S3_EMAIL_BUCKET).and_return(@bucket)
      allow(@bucket).to receive(:object).with(@file_name).and_return(@obj)
      allow(@obj).to receive(:presigned_url).and_return('https://www.aws.com/files/***********.jpg')

      user_lookup = build(:look_up, entity_type: LOOKUP_USER, entity_id: user.id, tenant_id: user.tenant_id, name: user.name)
      thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
      @email = create(:email, owner: user, tenant_id: user.tenant_id, connected_account: connected_account, email_thread_id: thread.id)
      @email.related_to << user_lookup
      @email.save!
      @attachment = create(:attachment, id: 77, email: @email, file_name: @file_name)
    end

    context 'When attachment exists' do
      it 'should get presigned URL of attachment' do
        get "/v1/emails/#{@email.id}/attachments/#{@attachment.id}", headers: headers, as: :json
        expect(response.status).to eq(200)
        expect(JSON.parse(response.body)).to eq({ "file_name" => 'old_file.jpg', "url" => 'https://www.aws.com/files/***********.jpg' })
      end
    end

    context 'Attachment does not exists' do
      it 'should raise an exception for attachment not found' do
        get "/v1/emails/#{@email.id}/attachments/11", headers: headers, as: :json
        expect(response.status).to eq(404)
        expect(response.parsed_body['errorCode']).to match(ErrorCode.not_found)
      end
    end
  end

  describe '#destroy' do
    before do
      email.save
    end

    context 'when user is email owner and has delete permission' do
      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailDeletedWorkflowV2))
        expect(PublishUsageJob).to receive(:perform_later).with(email.email_thread.tenant_id)
      end

      it 'deletes email' do
        delete "/v1/emails/#{email.id}", headers: headers

        expect(response.status).to eq(200)
      end
    end

    context 'when invalid email' do
      it 'returns email not found' do
        delete "/v1/emails/#{email.id + 1}", headers: headers

        expect(response.status).to eq(404)
        expect(response.parsed_body['errorCode']).to eq('********')
      end
    end

    context 'when user does not have permission to delete' do
      before do
        expect_any_instance_of(Auth::Data).to receive(:can_delete_emails?).and_return(false)
      end

      it 'returns unauthorized' do
        delete "/v1/emails/#{email.id}", headers: headers

        expect(response.status).to eq(401)
        expect(response.parsed_body['errorCode']).to eq('********')
      end
    end

    context 'when invalid request' do
      it 'returns error' do
        delete "/v1/emails/#{email.id}", headers: invalid_headers

        expect(response.status).to eq(401)
        expect(response.parsed_body['errorCode']).to eq('********')
      end
    end
  end

  describe '#send_email' do
    before do
      stub_entity_labels_api
      header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
      payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
      return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
      @email_template = create(:email_template, tenant_id: user.tenant_id, created_by: user, body: 'Lead {{lead.salutation}} {{lead.firstName}} {{lead.lastName}} created at {{lead.createdAt}} has owner {{lead.ownerId}}({{ownerId.profileId}})')
      @connected_account  = create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: user, tenant_id: user.tenant_id)
      setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
      allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', @connected_account.email).and_return(setting)
    end

    let(:payload) {
      {
        subject: 'Test subject',
        body: 'Test Email Body',
        emailTemplateId: @email_template.id,
        to: [
          {
            entity: 'lead',
            id: '1',
            email: '<EMAIL>',
            name: 'Sample Lead'
          }
        ],
        relatedTo: {
          entity: 'lead',
          id: '1',
          email: '<EMAIL>',
          name: 'Sample Lead'
        },
        cc:[
          {
            entity: 'user',
            id: '1',
            name: 'User Name',
            email: '<EMAIL>'
          }
        ],
        bcc:[
          {
            entity: 'user',
            id: '2',
            name: 'Other User Name',
            email: '<EMAIL>'
          }
        ],
        attachments:[]
      }.with_indifferent_access
    }

    context 'valid' do
      before do
        expect(PublishEvent).to receive(:call).exactly(4).times
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailCreatedWorkflowV2)).once
        stub_request(:get, SERVICE_IAM + "/v1/users/me").
          with(
            headers: {
              "Authorization" => "Bearer #{valid_auth_token.token}",
              'Content-Type'=>'application/json'
            }
          ).to_return(status: 200, body: file_fixture('user-profile-response.json').read, headers: {})

        stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?entityType=lead&custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
              "Authorization" => "Bearer #{valid_auth_token.token}"
            }
          ).to_return(status: 200, body: file_fixture('lead-fields.json').read, headers: {})
        stub_request(:get, SERVICE_SALES + "/v1/leads/1").
          with(
            headers: {
              "Authorization" => "Bearer #{valid_auth_token.token}"
            }
          ).to_return(status: 200, body: file_fixture('lead-data.json').read, headers: {})
        stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
          with(
            headers: {
              "Authorization" => "Bearer #{valid_auth_token.token}"
            }
          ).to_return(status: 200, body: file_fixture('standard-picklist.json').read, headers: {})
        stub_request(:post, SERVICE_IAM + "/v1/users/search?sort=updatedAt,desc&page=0&size=100")
          .to_return(status: 200, body: file_fixture('user-response.json'), headers: {})
      end

      context 'when email template id is present' do
        it 'should create email from email template' do
          expect { post '/v1/emails/send-email', params: payload.to_json, headers: headers }.to change { Email.count }.by(1)

          email = Email.last
          expect(email.subject).to eq('Test Subject')
          expect(email.body).to eq('Lead Miss shweta kylas created at Apr 01,2022 at 3:06 pm IST has owner Siby P()')
          expect(email.from).to eq(user.connected_accounts.first.email)
          expect(email.tenant_id).to eq(user.tenant_id)
        end
      end

      context 'when email template id is not present' do
        it 'should create email from params' do
          expect { post '/v1/emails/send-email', params: payload.merge(emailTemplateId: nil).to_json, headers: headers }.to change { Email.count }.by(1)

          email = Email.last
          expect(email.subject).to eq('Test subject')
          expect(email.body).to eq('Test Email Body')
          expect(email.from).to eq(user.connected_accounts.first.email)
          expect(email.tenant_id).to eq(user.tenant_id)
        end
      end
    end

    context 'invalid' do
      context 'when email template is not present' do
        it 'should return proper error code' do
          post '/v1/emails/send-email', params: payload.merge(emailTemplateId: (@email_template.id + 1)).to_json, headers: headers

          expect(response.status).to be(404)
          expect(response.parsed_body['errorCode']).to eq('********')
        end
      end

      context 'when email template is inactive' do
        before { @email_template.update!(active: false) }

        it 'should return proper error code' do
          post '/v1/emails/send-email', params: payload.to_json, headers: headers

          expect(response.status).to be(422)
          expect(response.parsed_body['errorCode']).to eq('********')
        end
      end

      context 'when custom provider returns error' do
        def stub_invalid_request(token, status)
          stub_request(:post, "#{SERVICE_EMAIL_ENGINE}/v1/account/#{token}/submit").
            to_return(status: status, body: '', headers: {})
        end

        before do
          stub_request(:get, SERVICE_IAM + "/v1/users/me").
            with(
              headers: {
                "Authorization" => "Bearer #{valid_auth_token.token}",
                'Content-Type'=>'application/json'
              }).
              to_return(status: 200, body: file_fixture('user-profile-response.json').read, headers: {})
              stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?entityType=lead&custom-only=false&sort=createdAt,asc&page=0&size=100").
                with(
                  headers: {
                    "Authorization" => "Bearer #{valid_auth_token.token}"
                  }).
                  to_return(status: 200, body: file_fixture('lead-fields.json').read, headers: {})
                  stub_request(:get, SERVICE_SALES + "/v1/leads/1").
                    with(
                      headers: {
                        "Authorization" => "Bearer #{valid_auth_token.token}"
                      }).
                      to_return(status: 200, body: file_fixture('lead-data.json').read, headers: {})
                      stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
                        with(
                          headers: {
                            "Authorization" => "Bearer #{valid_auth_token.token}"
                          }).
                          to_return(status: 200, body: file_fixture('standard-picklist.json').read, headers: {})
                          stub_request(:post, SERVICE_IAM + "/v1/users/search?sort=updatedAt,desc&page=0&size=100")
                            .to_return(status: 200, body: file_fixture('user-response.json'), headers: {})
                          @connected_account.update!(provider_name: CUSTOM_PROVIDER)
        end

        context 'when authorization error is raised' do
          before { stub_invalid_request(@connected_account.access_token, 401) }

          it 'should return smtp unauthorized error code' do
            post '/v1/emails/send-email', params: payload.to_json, headers: headers
            expect(response.status).to be(401)
            expect(response.parsed_body['errorCode']).to eq('********')
          end
        end

        context 'when not found is raised' do
          before { stub_invalid_request(@connected_account.access_token, 404) }

          it 'should return not found error code' do
            post '/v1/emails/send-email', params: payload.to_json, headers: headers
            expect(response.status).to be(422)
            expect(response.parsed_body['errorCode']).to eq('********')
          end
        end

        context 'when unsupported media type raised' do
          before { stub_invalid_request(@connected_account.access_token, 415) }

          it 'should return unsupported media type error code' do
            post '/v1/emails/send-email', params: payload.to_json, headers: headers
            expect(response.status).to be(422)
            expect(response.parsed_body['errorCode']).to eq('********')
          end
        end

        context 'when bad request is raised' do
          before { stub_invalid_request(@connected_account.access_token, 400) }

          it 'should return invalid code' do
            post '/v1/emails/send-email', params: payload.to_json, headers: headers
            expect(response.status).to be(422)
            expect(response.parsed_body['errorCode']).to eq('********')
          end
        end

        context 'when too many requests is raised' do
          before { stub_invalid_request(@connected_account.access_token, 429) }

          it 'should return rate limit error code' do
            post '/v1/emails/send-email', params: payload.to_json, headers: headers
            expect(response.status).to be(422)
            expect(response.parsed_body['errorCode']).to eq('********')
          end
        end

        context 'when internal error is raised' do
          before { stub_invalid_request(@connected_account.access_token, 500) }

          it 'should return internal error code' do
            post '/v1/emails/send-email', params: payload.to_json, headers: headers
            expect(response.status).to be(500)
            expect(response.parsed_body['errorCode']).to eq('********')
          end
        end

        context 'when not implemented is raised' do
          before { stub_invalid_request(@connected_account.access_token, 501) }

          it 'should return not implemented error code' do
            post '/v1/emails/send-email', params: payload.to_json, headers: headers
            expect(response.status).to be(422)
            expect(response.parsed_body['errorCode']).to eq('********')
          end
        end

        context 'when bad gateway is raised' do
          before { stub_invalid_request(@connected_account.access_token, 502) }

          it 'should return bad gateway error code' do
            post '/v1/emails/send-email', params: payload.to_json, headers: headers
            expect(response.status).to be(422)
            expect(response.parsed_body['errorCode']).to eq('********')
          end
        end

        context 'when service unavailable is raised' do
          before { stub_invalid_request(@connected_account.access_token, 503) }

          it 'should return service unavailable error code' do
            post '/v1/emails/send-email', params: payload.to_json, headers: headers
            expect(response.status).to be(422)
            expect(response.parsed_body['errorCode']).to eq('********')
          end
        end

        context 'when gateway timeout is raised' do
          before { stub_invalid_request(@connected_account.access_token, 504) }

          it 'should return gateway timeout error code' do
            post '/v1/emails/send-email', params: payload.to_json, headers: headers
            expect(response.status).to be(422)
            expect(response.parsed_body['errorCode']).to eq('********')
          end
        end
      end
    end
  end

  describe '#smart_list_records_count' do
    before do
      allow_any_instance_of(SharedAccess).to receive(:fetch)
        .and_return(
          {
            accessByOwners: { '3779' => { email: true } },
            accessByRecords: { '3779' => { read: true, email: true } }
          }.with_indifferent_access
        )
    end

    context 'when request is invalid' do
      before { get '/v1/emails/search/smart-list/count?id=108998', headers: invalid_headers }

      it 'returns a failure message' do
        expect(response.parsed_body['errorCode']).to match('********')
      end
    end

    context 'when request is valid' do
      context 'when smart-list id is invalid' do
        before do
          stub_request(:get, "#{SERVICE_SEARCH}/v1/search-lists/email?id=108998").and_raise(RestClient::BadRequest)

          get '/v1/emails/search/smart-list/count?id=108998', headers: headers
        end

        it 'returns error code in the response' do
          expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid)
        end
      end

      context 'when smart-list id is valid' do
        let(:expected_smart_list_details_response) do
          JSON.parse(file_fixture('smart-list-details-response.json').read)
        end

        before do
          stub_request(:get, "#{SERVICE_SEARCH}/v1/search-lists/email?id=108998")
            .and_return(status: 200, body: expected_smart_list_details_response.to_json)
        end

        context 'when emails records are not found' do
          it 'returns the records count zero with smart-list id and name' do
            get '/v1/emails/search/smart-list/count?id=108998', headers: headers

            expect(response.parsed_body).to match_array(
              [
                { 'id' => 108_998, 'name' => "Today's Emails", 'count' => 0 }
              ]
            )
          end
        end

        context 'when emails records are found' do
          before(:each) do
            @emails = create_list(:email, 3, owner: user, tenant_id: user.tenant_id) do |email|
              email.created_at = (Time.zone.now.in_time_zone('Etc/GMT+12').beginning_of_day + 1.hours).utc
              email.read_by << user.id
              email.save!
            end
            @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: user.id)
            @cc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)
            @bcc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)

            @emails.each do |e|
              e.to_recipients << @to_lookup
              e.cc_recipients << @cc_lookup
              e.bcc_recipients << @bcc_lookup
            end
          end

          it 'returns the records count with smart-list id and name' do
            get '/v1/emails/search/smart-list/count?id=108998', headers: headers
            expect(response.parsed_body).to match_array(
              [
                { 'id' => 108_998, 'name' => "Today's Emails", 'count' => 3 }
              ]
            )
          end
        end
      end
    end
  end

  describe '#history' do

    before do
      allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => {} })
    end

    context 'when request is invalid' do
      before { get '/v1/emails/1/history', headers: invalid_headers }

      it 'returns a failure message' do
        expect(response.parsed_body['errorCode']).to match('********')
      end
    end

    context 'when the request is valid' do
      before do
        @thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
        body = "<div> text </div>"
        @emails = create_list(:email, 3, body: body,owner: another_user, tenant_id: user.tenant_id, email_thread_id: @thread.id, connected_account: connected_account) do |email, index|
          email.created_at = (10 - index).days.ago
          email.read_by << user.id
          email.save!
        end
        @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: user.id)
        @cc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)
        @bcc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)

        @emails.each { |e| e.to_recipients << @to_lookup }
        @emails.each { |e| e.cc_recipients << @cc_lookup }
        @emails.each { |e| e.bcc_recipients << @bcc_lookup }

        @emails.each do |e|
          e.related_to << @to_lookup
          e.related_to << @cc_lookup
          e.related_to << @bcc_lookup
        end
      end

      context 'when user has access to the thread' do
        context 'when email id is invalid' do
          before { get "/v1/emails/9999999/history", headers: headers }

          it 'returns error code in the response' do
            expect(response.parsed_body['errorCode']).to match(ErrorCode.not_found)
          end
        end

        context 'when email id is valid' do
          before do
            @deal_lookup = build(:look_up, entity_type: LOOKUP_DEAL, entity_id: 1000, tenant_id: user.tenant_id)
            @emails.each { |e| e.related_to << @deal_lookup }
            get "/v1/emails/#{@emails.last(2).first.id}/history", headers: headers
          end

          it 'returns the emails history' do
            expect(response.parsed_body['content'].count).to eq(2)
          end

          it 'returns the correct fields in the response' do
            emails = response.parsed_body['content']
            expect(emails.first.keys).to match_array(%w[id from to cc bcc subject body sentAt direction])
          end

          it 'returns emails in the correct sequence' do
            emails = response.parsed_body['content']
            expect(emails.map{|e| e['id']}).to eq(@emails.first(2).map(&:id).reverse)
          end

          it 'returns body in the plain text' do
            emails = response.parsed_body['content']
            expect(emails.first['body']).to eq 'text'
          end
        end

        context 'when pagination is applied' do
          context 'when page number is 1' do
            before { get "/v1/emails/#{@emails.last(2).first.id}/history?#{page_params}", headers: headers }

            context 'and the number of emails does not exceeds the page size' do
              let(:page_params) { 'page=1&size=5' }

              it 'returns correct number of records' do
                expect(response.parsed_body['content'].count).to eq(2)
              end

              it 'returns correct pagination details in the response' do
                json = response.parsed_body
                expect(json['page']['no']).to eq(1)
                expect(json['page']['size']).to eq(5)

                expect(json['totalElements']).to eq(2)
                expect(json['totalPages']).to eq(1)
                expect(json['first']).to eq(true)
                expect(json['last']).to eq(true)
              end
            end
          end
        end
      end

      context 'when user does not have access to the thread' do
        before do
          @to_lookup.update(owner_id: another_user.id)
        end

        it 'returns error' do
          expect(Rails.logger).to receive(:error).with("Unauthorised: User does not have read on thread. User id #{user.id} Thread id #{@thread.id}")
          get "/v1/emails/#{@emails.last.id}/history", headers: headers

          expect(response.status).to be(401)
          expect(response.parsed_body['errorCode']).to eq('********')
        end
      end
    end
  end
end
