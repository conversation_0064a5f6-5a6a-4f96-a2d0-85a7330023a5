require 'swagger_helper'
require 'bunny-mock'

RSpec.describe 'Gmail Webhook API', type: :request do
  path '/v1/gmail_webhooks' do
    post 'creates webhook' do
      tags 'Gmail webhook'
      consumes 'application/json'
      parameter name: :message, in: :body, type: :json

      before do
        connection = BunnyMock.new
        @channel = connection.start.channel
        @exchange = @channel.topic EMAIL_EXCHANGE
        @queue = @channel.queue GMAIL_WEBHOOK_EVENT
        @queue.bind @exchange, routing_key: GMAIL_WEBHOOK_EVENT
        allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)
      end

      response '200', 'webhook creation' do
        let(:message) { { message: { data: 'message-data' } } }

        run_test!
      end
    end
  end
end
