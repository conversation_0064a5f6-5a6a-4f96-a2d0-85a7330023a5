# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::LayoutController, type: :request do
  let(:user)             { create(:user, tenant: create(:tenant) )}
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let(:headers)          { valid_headers(valid_auth_token) }

  describe '#list' do
    context 'layout list json' do
      before { get '/v1/emails/layout/list', headers: headers }

      it 'returns leftNav, pageConfig, defaultConfig' do
        layout_list_json  = response.parsed_body

        expect(layout_list_json.keys).to match_array(%w[leftNav pageConfig defaultConfig])
      end

      it 'returns actionConfig and tableConfig in pageConfig' do
        page_config_json = response.parsed_body['pageConfig']

        expect(page_config_json.keys).to match_array(%w[actionConfig tableConfig])
      end

      it 'returns 14 fields in table config' do
        fields = response.parsed_body['pageConfig']['tableConfig']['columns']

        expect(fields.map { |f| f['id'] }).to match_array(%w[associatedContacts associatedDeals associatedLeads createdAt receivedBy sentBy subject user direction status userFields sentByFields receivedByFields attachments read openedAt clickedAt relatedTo])
        expect(fields.map { |f| f['header'] }).to match_array(["Associated Contacts", "Associated Deals", "Associated Leads", "Mailed At", "Received By", "Sent By", "Subject", "User", "Email Type", 'Status', 'User Fields', 'Sent By Fields', 'Received By Fields', 'Attachments', 'Read', 'Opened At', 'Clicked At', 'Related To'])
        expect(fields.map { |f| f['fieldType'] }).to match_array(%w[LOOK_UP LOOK_UP LOOK_UP LOOK_UP DATETIME_PICKER LOOK_UP LOOK_UP TEXT_FIELD PICK_LIST PICK_LIST ENTITY_FIELDS ENTITY_FIELDS ENTITY_FIELDS FILE_PICKER TOGGLE DATETIME_PICKER DATETIME_PICKER ENTITY_LOOKUP])
      end

      it 'returns 12 filterable fields' do
        filterable_fields = response.parsed_body['pageConfig']['tableConfig']['columns'].select { |f| f['isFilterable'] }

        expect(filterable_fields.map { |f| f['id'] }).to match_array(%w[associatedContacts associatedDeals associatedLeads createdAt receivedBy sentBy subject user direction userFields sentByFields receivedByFields attachments read openedAt clickedAt status])
      end

      it 'returns 5 look up fields' do
        lookup_fields = response.parsed_body.with_indifferent_access['pageConfig']['tableConfig']['columns'].select { |f| f['fieldType'] == 'LOOK_UP' }

        expect(lookup_fields.map { |f| f['lookup'] }).to match_array(
          [
            {
              'entity' => 'USER',
              'lookupUrl' => '/users/lookup?q=name:'
            },
            {
              'entity' => 'USER',
              'lookupUrl' => '/users/lookup?q=name:'
            },
            {
              'entity' => 'USER',
              'lookupUrl' => '/users/lookup?q=name:'
            },
            {
              'entity': 'LEAD',
              'lookupUrl': '/search/lead/lookup?q=firstName:'
            },
            {
              'entity': 'DEAL',
              'lookupUrl': '/search/deal/lookup?q=name:'
            },
            {
              'entity': 'CONTACT',
              'lookupUrl': '/search/contact/lookup?q=firstName:'
            }
          ]
        )
      end
    end
  end
end
