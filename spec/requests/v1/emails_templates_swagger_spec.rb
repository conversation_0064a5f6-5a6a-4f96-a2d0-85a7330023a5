require 'swagger_helper'

RSpec.describe 'Email Template API', type: :request do
  let(:user)              { create(:user)}
  let(:valid_auth_token)  { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)         { ParseToken.call(valid_auth_token.token).result }
  let(:Authorization)     { valid_auth_token.token }
  let(:email_template)    { build(:email_template, tenant_id: user.tenant_id) }

  before do
    stub_entity_labels_api
    allow(TenantEmailTemplateUsagePublisher).to receive(:publish).with(instance_of(Integer))
  end

  path '/v1/email-templates' do
    post 'Creates an Email template' do
      tags 'Email Template'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :body
      parameter name: :email_template, in: :body, schema: {
        type: :object,
        required: %w[name category],
        properties: {
          name: {
            type: String
          },
          category: {
            type: String
          },
          subject: {
            type: String
          },
          body: {
            type: String
          }
        }
      }

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '201', 'email template created' do
        before do
          stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?custom-only=false&entityType=lead&page=0&size=100&sort=createdAt,asc").
          with(
            headers: {
            'Authorization' => "Bearer #{ valid_auth_token.token }"
            }).
          to_return(status: 200, body: file_fixture('lead-fields.json'), headers: {})
        end

        run_test!
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end
    end
  end

  path '/v1/email-templates/{id}' do
    put 'Updates an Email template' do
      tags 'Email Template'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :body
      parameter name: :email_template, in: :body, schema: {
          type: :object,
          required: %w[name category],
          properties: {
              name: {
                  type: String
              },
              category: {
                  type: String
              },
              subject: {
                  type: String
              },
              body: {
                  type: String
              }
          }
      }

      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'email template updated' do
        pending('intentional')
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        pending('intentional')
      end
    end
  end

  path '/v1/email-templates/search' do
    post 'Search an Email template' do
      tags 'Email Template'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string
      parameter name: :jsonRule, in: :body, schema: {
          type: :object,
          properties: {
              jsonRule: {
                  condition: { type: :string },
                  rules: { type: :array, items: { type: 'string' } }
              }
          }
      }
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'email template Search' do
        before do
          stub_request(:get, SERVICE_CONFIG + "/v1/entities/lead/fields?custom-only=false&entityType=lead&page=0&size=100&sort=createdAt,asc").
              with(
                  headers: {
                      'Authorization' => "Bearer #{ valid_auth_token.token }"
                  }).
              to_return(status: 200, body: [{"id":1,"displayName":"First Name","name":"firstName","standard":true},{"id":2,"displayName":"Last Name","name":"lastName","standard":true}].to_json, headers: {})
        end

        pending('intentional')
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        pending('intentional')
      end
    end
  end

  path '/v1/email-templates/variables' do
    get 'Get Email template variables' do
      tags 'Email Template'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :category, in: :query, type: :string
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'email template variables for category' do
        pending('intentional')
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        pending('intentional')
      end
    end
  end

  path '/v1/email-templates/{id}' do
    get 'Get Email template ' do
      tags 'Email Template'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :id, in: :path, type: :string
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'email template' do
        pending('intentional')
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        pending('intentional')
      end
    end
  end

  path '/v1/email-templates/{id}/{entity_type}/{entity_id}' do
    get 'Get details with entity' do
      tags 'Email Template'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :id, in: :path, type: :string
      parameter name: :entity_type, in: :path, type: :string
      parameter name: :entity_id, in: :path, type: :string
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'email template with entity' do
        pending('intentional')
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        pending('intentional')
      end
    end
  end

  path '/v1/email-templates/{id}/activate' do
    post 'Activate Email template ' do
      tags 'Email Template'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :id, in: :path, type: :string
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'email template activated' do
        pending('intentional')
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        pending('intentional')
      end
    end
  end

  path '/v1/email-templates/{id}/deactivate' do
    post 'Deactivate Email template ' do
      tags 'Email Template'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :id, in: :path, type: :string
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'email template deactivated' do
        pending('intentional')
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        pending('intentional')
      end
    end
  end

  path '/v1/email-templates/:id/attachments/:attachment_id' do
    get 'Download Email template attachment' do
      tags 'Email Template'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :id, in: :path, type: :string
      parameter name: :attachment_id, in: :path, type: :string
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'email template attachment url' do
        pending('intentional')
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        pending('intentional')
      end
    end
  end

  path '/v2/email-templates/variables' do
    get 'Get Email template variables' do
      tags 'Email Template'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :category, in: :query, type: :string
      parameter({
                    :in => :header,
                    :type => :string,
                    :name => :Authorization,
                    :required => true,
                    :description => 'Client token'
                })

      response '201', 'email template variables for category' do
        pending('intentional')
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        pending('intentional')
      end
    end
  end

  path '/v1/email-templates/lookup' do
    get 'Email Template Lookup' do
      tags 'Email Template'
      security [ bearerAuth: [] ]

      parameter name: :category, in: :query, type: :string
      parameter name: :q, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      response '200', 'Email Templates fetched successfully' do
        let(:category) { 'lead' }
        let(:q) { 'template' }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:category) { 'lead' }
        let(:q) { 'template' }

        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end
    end
  end
end
