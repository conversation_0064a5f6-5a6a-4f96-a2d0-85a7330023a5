require 'swagger_helper'
require 'google/apis/gmail_v1'
require 'bunny-mock'

RSpec.describe 'Email API', type: :request do
  let(:user)              { create(:user)}
  let(:valid_auth_token)  { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)         { ParseToken.call(valid_auth_token.token).result }
  let(:Authorization)     { valid_auth_token.token }
  let(:connected_account) { create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: user, tenant: user.tenant) }
  let(:thread)            { create(:email_thread, id: 11,owner: user, tenant_id: user.tenant_id) }
  let(:email)             { build(:email, connected_account: connected_account, email_thread_id: thread.id, to:[connected_account.email]) }

  path '/v1/email-threads' do
    post 'Common email-threads url for gateway' do
      tags 'Email-threads'
      consumes 'application/json'
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Emails Thread controller' do
        let(:Authorization) { valid_auth_token.token }
        pending('intentional')
      end
    end
  end

  path '/v1/email-threads/search' do
    post 'Searches emails' do
      tags 'Emails'
      consumes 'application/json'
      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      parameter name: :jsonRule, in: :body, schema: {
        type: :object,
        properties: {
          jsonRule: {
            condition: { type: :string },
            rules: { type: :array, items: { type: 'string' } }
          }
        }
      }

      response '200', 'Emails Searched' do
        let(:page) { '1' }
        let(:size) { '2' }
        let(:Authorization) { valid_auth_token.token }
        let(:jsonRule)      { { jsonRule: { condition: "AND", rules: [{ operator: "equal", type: "related_lookup",
                                                                        field: "related_to", value: { id: 10, "entity": LOOKUP_LEAD } }]}}}

        before do
          to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 10, tenant_id: user.tenant_id)
          stub_request(:get, SERVICE_SALES + "/v1/leads/#{to_lookup.entity_id}").
            with(
              headers: {
                "Authorization" => "Bearer #{ valid_auth_token.token }"
              }).
              to_return(status: 200, body: {"id": to_lookup.entity_id, "ownerId": user.id, "recordActions": { "read": true, "update": true, "delete": true, "email": true, "call": false, "sms": false, "task": true, "note": true, "meeting": true }}.to_json, headers: {})

              [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity|
                stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/EMAIL")
                  .with(
                    headers: {
                      Authorization: "Bearer #{valid_auth_token.token}"
                    }
                  )
                    .to_return(status: 200, body: { accessByOwners: { "4167" => { email: true } }, accessByRecords: { "123" => { read: true, email: true } } }.to_json)
              end
        end
        run_test!
      end

      response '401', 'authentication failed' do
        let(:page) { '1' }
        let(:size) { '2' }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:jsonRule)      { { jsonRule: { condition: "AND", rules: [{ operator: "equal", type: "look_up",
                                                                        field: "related", value: { id: 10, "entity": "lead" } }]}}}

        run_test!
      end
    end
  end

  path '/v1/email-threads/{id}/mark_as_read' do
    post 'Marks all email in the thread as read' do
      tags 'Email Thread'
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      before do
        @new_thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
        create(:email,owner: user, tenant_id: user.tenant_id, connected_account: connected_account, email_thread_id: @new_thread.id)
        create(:email,owner: user, tenant_id: user.tenant_id, connected_account: connected_account, email_thread_id: @new_thread.id, read_by: [user.id, 344])
      end

      response '204', 'Emails Thread marked as read' do
        let(:Authorization) { valid_auth_token.token }
        let(:id)            { @new_thread.id }

        before do
          to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 10, tenant_id: user.tenant_id)
          stub_request(:get, SERVICE_SALES + "/v1/leads/#{to_lookup.entity_id}").
            with(
              headers: {
                "Authorization" => "Bearer #{valid_auth_token.token}"
              }
            ).
            to_return(status: 204, headers: {})
            allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => {} })
        end

        run_test!
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id)            { 1 }

        run_test!
      end
    end
  end

  path '/v1/email-threads/{id}/mark_as_unread' do
    post 'Marks all email in the thread as unread' do
      tags 'Email Thread'
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      before do
        @new_thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
        create(:email,owner: user, tenant_id: user.tenant_id, connected_account: connected_account, email_thread_id: @new_thread.id)
        create(:email,owner: user, tenant_id: user.tenant_id, connected_account: connected_account, email_thread_id: @new_thread.id, read_by: [user.id, 344])
      end

      response '204', 'Emails Thread marked as unread' do
        let(:Authorization) { valid_auth_token.token }
        let(:id)            { @new_thread.id }

        before do
          to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 10, tenant_id: user.tenant_id)
          stub_request(:get, SERVICE_SALES + "/v1/leads/#{to_lookup.entity_id}").
            with(
              headers: {
                "Authorization" => "Bearer #{ valid_auth_token.token }"
              }
            ).
            to_return(status: 204, headers: {})
            allow_any_instance_of(SharedAccess).to receive(:fetch).and_return({ 'accessByOwners' => {}, 'accessByRecords' => {} })
        end

        run_test!
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id)            { 1 }

        run_test!
      end
    end
  end

  path '/v1/email-threads/{id}/reply' do
    post 'Reply on thread' do
      tags 'Reply on email Thread'
      consumes 'application/json'

      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      parameter name: :email_body, in: :body, schema: {
        type: :object,
        required: %w[to relatedTo],
        properties: {
          body: {
            type: :string
          },
          subject: {
            type: :string
          },
          to: {
            type: :array,
            items: {
              type: :object,
              properties: {
                entity: {
                  type: :string
                },
                id: {
                  type: :string
                },
                name: {
                  type: :string
                },
                email: {
                  type: :string
                },
              },
              required: [:entity, :id, :name, :email]
            }
          },
          cc: {
            type: :array,
            items: {
              type: :object,
              properties: {
                entity: {
                  type: :string
                },
                id: {
                  type: :string
                },
                name: {
                  type: :string
                },
                email: {
                  type: :string
                },
              },
              required: [:entity, :id, :name, :email]
            }
          },
          bcc: {
            type: :array,
            items: {
              type: :object,
              properties: {
                entity: {
                  type: :string
                },
                id: {
                  type: :string
                },
                name: {
                  type: :string
                },
                email: {
                  type: :string
                },
              },
              required: [:entity, :id, :name, :email]
            }
          },
          relatedTo: { type: :object },
          replyTo: { type: :integer }
        }
      }

      response '201', 'Reply on email thread' do
        let(:Authorization) { valid_auth_token.token }
        let(:id)            { 11 }
        let(:email_body)    { email.as_json.merge(replyTo: @new_email.id) }

        before do
          connection = BunnyMock.new
          @channel = connection.start.channel
          @exchange = @channel.topic EMAIL_EXCHANGE
          @queue = @channel.queue EMAIL_CREATED
          @queue.bind @exchange, routing_key: EMAIL_CREATED
          allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(@queue)

          header = Google::Apis::GmailV1::MessagePartHeader.new(name: 'Message-Id', value: '<<EMAIL>>')
          payload = Google::Apis::GmailV1::MessagePart.new(headers: [header])
          return_message = Google::Apis::GmailV1::Message.new(id: 'SOURCE_ID', thread_id: 'THREAD_ID', payload: payload)
          allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:send_user_message).and_return(return_message)
          allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_message).and_return(return_message)
          setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
          allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', connected_account.email).and_return(setting)
          user_lookup = build(:look_up, entity_type: LOOKUP_USER, entity_id: user.id, tenant_id: user.tenant_id, name: user.name)
          allow(ValidateUsers).to receive_message_chain(:call, :result).and_return([
            user_lookup
          ])

          @new_email = create(:email, connected_account: connected_account, tenant_id: user.tenant_id, email_thread_id: thread.id, to:[connected_account.email], sender: user_lookup)
          @new_email.related_to << build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1000, owner_id: user.id, tenant_id: user.tenant_id)
        end

        run_test!
      end

      response '401', 'authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id)            { 11 }
        let(:email_body)    { email }

        run_test!
      end
    end
  end

  path '/v1/email-threads/{id}' do
    let(:id) { FactoryBot.create(:email_thread, owner: user, tenant_id: user.tenant_id).id }

    delete 'Delete an Email thread' do
      tags 'Email Thread'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })
      response '200', 'Email thread delete success' do
        let!(:valid_auth_token) { build(:auth_token, :with_email_delete_permission, user_id: user.id, tenant_id: user.tenant_id) }
        let!(:auth_data)        { ParseToken.call(valid_auth_token.token).result }
        let!(:Authorization)    { valid_auth_token.token }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:email_thread)      { build(:email_thread) }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { FactoryBot.create(:email_thread, owner: user).id }
        run_test!
      end
    end
  end

  path '/v1/email-threads/{id}/associate-with-deals' do
    let(:id) { FactoryBot.create(:email_thread, owner: user, tenant_id: user.tenant_id).id }
    let(:deals) {{ deals: [id: 9, name: 'test']}}
    let(:deal_search_response) { file_fixture('deal-search-response.json').read }

    before do
       stub_request(:post, "http://localhost:8083/v1/search/deal?page=0&size=1000&sort=updatedAt,desc").
        with(
          headers: {
            Authorization: "Bearer #{valid_auth_token.token}"
          },
          body: {
            fields: %w[id name ownedBy ownerId],
            jsonRule:{
              condition: 'AND',
              rules: [{ operator: 'in', id: 'id', field: 'id', type: 'double', value: "9" }],
              valid: true
            }
          }
        ).
        to_return(status: 200, body: deal_search_response, headers: {})
    end

    post 'Associate email with thread' do
      tags 'Email Thread'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string
      parameter name: :deals, in: :body, type: :array

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })
      response '200', 'Email thread association with deal' do
        let!(:valid_auth_token) { build(:auth_token, :with_email_delete_permission, user_id: user.id, tenant_id: user.tenant_id) }
        let!(:auth_data)        { ParseToken.call(valid_auth_token.token).result }
        let!(:Authorization)    { valid_auth_token.token }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:email_thread)      { build(:email_thread) }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id) { FactoryBot.create(:email_thread, owner: user).id }
        run_test!
      end
    end
  end
end
