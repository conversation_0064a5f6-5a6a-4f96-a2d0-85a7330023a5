require 'swagger_helper'

RSpec.describe 'Health API', type: :request do

  path '/v90f80607226e15c2/emails/health' do
    get 'Email from database' do
      tags 'Emails'

      response '200', 'Database is up' do
        before do
          tenant = create(:tenant)
          expect(ENV).to receive(:[]).with('TENANT_ID').and_return(tenant.id)
          create(:email, tenant: tenant)
        end

        run_test!
      end

      response '404', 'Entity not present' do
        run_test!
      end

      response '503', 'Database is down' do
        before do
          expect(Email).to receive(:find_by).and_raise(PG::ConnectionBad)
        end

        run_test!
      end
    end
  end
end
