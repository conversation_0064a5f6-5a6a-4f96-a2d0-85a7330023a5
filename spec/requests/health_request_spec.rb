# frozen_string_literal: true

require 'rails_helper'

RSpec.describe "Health controller", type: :request do
  describe "#status" do
    let(:tenant) { create(:tenant) }

    context "when email is present" do
      before do
        expect(ENV).to receive(:[]).with('TENANT_ID').and_return(tenant.id)
        create(:email, tenant_id: tenant.id)
      end

      it "should give response 200" do
        get '/v90f80607226e15c2/emails/health'
        expect(response.status).to eq(200)
      end
    end

    context "when email is not present" do
      it "should give response 404" do
        get '/v90f80607226e15c2/emails/health'
        expect(response.status).to eq(404)
      end
    end

    context "when connection not established" do
      it "should give response 503" do
        allow(Email).to receive(:find_by).and_raise(ActiveRecord::ConnectionNotEstablished)
        get '/v90f80607226e15c2/emails/health'
        expect(response.status).to eq(503)
      end
    end

    context "when unable to connect to database using existing connection" do
      it "should give response 503" do
        allow(Email).to receive(:find_by).and_raise(PG::ConnectionBad)
        get '/v90f80607226e15c2/emails/health'
        expect(response.status).to eq(503)
      end
    end
  end
end
