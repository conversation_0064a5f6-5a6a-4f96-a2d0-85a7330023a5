require 'swagger_helper'
require 'google/apis/gmail_v1'
require 'bunny-mock'

RSpec.describe 'V2 Email API', type: :request do
  let(:user)              { create(:user)}
  let(:valid_auth_token)  { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)         { ParseToken.call(valid_auth_token.token).result }
  let(:Authorization)     { valid_auth_token.token }
  let(:connected_account) { create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: user) }
  let(:thread)            { create(:email_thread, id: 11,owner: user, tenant_id: user.tenant_id) }
  let(:email)             { build(:email, connected_account: connected_account, email_thread_id: thread.id, to:[connected_account.email]) }

  path '/v2/email-threads/search' do
    post 'Searches emails' do
      tags 'Emails'
      consumes 'application/json'
      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      parameter name: :jsonRule, in: :body, schema: {
        type: :object,
        properties: {
          jsonRule: {
            condition: { type: :string },
            rules: { type: :array, items: { type: 'string' } }
          }
        }
      }

      response '200', 'Emails Searched' do
        let(:page) { '1' }
        let(:size) { '2' }
        let(:Authorization) { valid_auth_token.token }
        let(:jsonRule)      { { jsonRule: { condition: "AND", rules: [{ operator: "equal", type: "related_lookup",
                                                                        field: "related_to", value: { id: 10, "entity": LOOKUP_LEAD } }]}}}

        before do
          to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 10, tenant_id: user.tenant_id)
          stub_request(:get, SERVICE_SALES + "/v1/leads/#{to_lookup.entity_id}").
            with(
              headers: {
                "Authorization" => "Bearer #{ valid_auth_token.token }"
              }
            ).
            to_return(status: 200, body: {"id": to_lookup.entity_id, "ownerId": user.id, "recordActions": { "read": true, "update": true, "delete": true, "email": true, "call": false, "sms": false, "task": true, "note": true, "meeting": true }}.to_json, headers: {})

          [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity|
            stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/EMAIL")
              .with(
                headers: {
                  Authorization: "Bearer #{valid_auth_token.token}"
                }
              )
              .to_return(status: 200, body: { accessByOwners: { "4167" => { email: true } }, accessByRecords: { "123" => { read: true, email: true } } }.to_json)
          end
        end
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:page) { '1' }
        let(:size) { '2' }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:jsonRule)      { { jsonRule: { condition: "AND", rules: [{ operator: "equal", type: "look_up",
                                                                        field: "related", value: { id: 10, "entity": "lead" } }]}}}

        run_test!
      end
    end
  end
end
