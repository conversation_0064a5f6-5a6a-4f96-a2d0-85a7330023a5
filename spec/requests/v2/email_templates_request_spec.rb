# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Email Templates Management', type: :request do
  let(:user)                      { create(:user, tenant: create(:tenant, id:1))}
  let(:valid_auth_token)          { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:headers)                   { valid_headers(valid_auth_token) }

  describe '#variables' do
    context 'with valid parameters' do

      before do
        stub_entity_labels_api
        stub_request(:get, SERVICE_DEAL + "/v1/deals/fields?custom-only=false&sort=createdAt,asc&page=0&size=100").
          with(
            headers: {
              "Authorization" => "Bearer #{ valid_auth_token.token }"
            }).
            to_return(status: 200, body: file_fixture('deal-fields.json'), headers: {})
            get '/v2/email-templates/variables?category=deal', headers: headers, as: :json
      end

      it 'returns data for deal' do
        content = response.parsed_body['variables']
        res = content.select { |field| field['entity'] == 'deal' }.first
        expect(res["displayName"]).to eq("Deal - Name")
        expect(res["internalName"]).to eq("name")
        expect(res["standard"]).to eq true
      end
    end
  end
end
