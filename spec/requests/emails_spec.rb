require 'rails_helper'

# This spec was generated by rspec-rails when you ran the scaffold generator.
# It demonstrates how one might use RSpec to test the controller code that
# was generated by <PERSON>s when you ran the scaffold generator.
#
# It assumes that the implementation code is generated by the rails scaffold
# generator. If you are using any extension libraries to generate different
# controller code, this generated spec may or may not pass.
#
# It only uses APIs available in rails and/or rspec-rails. There are a number
# of tools you can use to make these specs even more expressive, but we're
# sticking to rails and rspec-rails APIs to keep things simple and stable.

RSpec.describe "/emails", type: :request do
  # This should return the minimal set of attributes required to create a valid
  # Email. As you add validations to Email, be sure to
  # adjust the attributes here as well.
  let(:valid_attributes) {
    skip("Add a hash of attributes valid for your model")
  }

  let(:invalid_attributes) {
    skip("Add a hash of attributes invalid for your model")
  }

  # This should return the minimal set of values that should be in the headers
  # in order to pass any filters (e.g. authentication) defined in
  # EmailsController, or in your router and rack
  # middleware. Be sure to keep this updated too.
  let(:valid_headers) {
    {}
  }

  xdescribe "GET /index" do
    it "renders a successful response" do
      Email.create! valid_attributes
      get emails_url, headers: valid_headers, as: :json
      expect(response).to be_successful
    end
  end

  xdescribe "GET /show" do
    it "renders a successful response" do
      email = Email.create! valid_attributes
      get email_url(email), as: :json
      expect(response).to be_successful
    end
  end

  xdescribe "PATCH /update" do
    context "with valid parameters" do
      let(:new_attributes) {
        skip("Add a hash of attributes valid for your model")
      }

      it "updates the requested email" do
        email = Email.create! valid_attributes
        patch email_url(email),
              params: { email: invalid_attributes }, headers: valid_headers, as: :json
        email.reload
        skip("Add assertions for updated state")
      end

      it "renders a JSON response with the email" do
        email = Email.create! valid_attributes
        patch email_url(email),
              params: { email: invalid_attributes }, headers: valid_headers, as: :json
        expect(response).to have_http_status(:ok)
        expect(response.content_type).to eq("application/json")
      end
    end

    context "with invalid parameters" do
      it "renders a JSON response with errors for the email" do
        email = Email.create! valid_attributes
        patch email_url(email),
              params: { email: invalid_attributes }, headers: valid_headers, as: :json
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.content_type).to eq("application/json")
      end
    end
  end

  xdescribe "DELETE /destroy" do
    it "destroys the requested email" do
      email = Email.create! valid_attributes
      expect {
        delete email_url(email), headers: valid_headers, as: :json
      }.to change(Email, :count).by(-1)
    end
  end
end
