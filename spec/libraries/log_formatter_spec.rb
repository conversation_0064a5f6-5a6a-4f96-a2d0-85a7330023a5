require 'rails_helper'

RSpec.describe LogFormatter do
  describe '#call' do
    let(:time)           { Time.now }
    let(:formatted_time) { time.strftime("%Y-%m-%d %H:%M:%S.%L") }

    before(:each) { @result = subject.call("INFO", time, '', log_message) }

    context 'when log message is empty' do
      let(:log_message) { '' }

      it 'returns correct log output'do
        expect(@result).to be_empty
      end
    end

    context 'when log message is in simple string format' do
      let(:log_message) { 'This is test message' }

      it 'returns correct log output'do
        formatted_log = { "context":"default", "timestamp":formatted_time, "level":"INFO",
                         "progname":"", "message":"This is test message" }.to_json

        expect(@result).to eq("#{formatted_log}\n")
      end
    end

    context 'when log message is in JSON object format' do
      let(:log_message) { { "message": "This is test message", "class_name": "MeetingsController" }.to_json }

      it 'returns correct log output'do
        formatted_log = { "context":"default", "timestamp":formatted_time, "level":"INFO",
                         "progname":"", "message":"This is test message", "class_name":"MeetingsController" }.to_json

        expect(@result).to eq("#{formatted_log}\n")
      end
    end
  end
end
