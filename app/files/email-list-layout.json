{"leftNav": false, "pageConfig": {"actionConfig": {"search": true, "filter": true, "create": true}, "tableConfig": {"recordClickAction": "VIEW", "columns": [{"id": "user", "header": "User", "fieldType": "LOOK_UP", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": {"entity": "USER", "lookupUrl": "/users/lookup?q=name:"}, "picklist": null, "primaryField": null}, {"id": "sentBy", "header": "<PERSON><PERSON>", "fieldType": "LOOK_UP", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": {"entity": "USER", "lookupUrl": "/users/lookup?q=name:"}, "picklist": null, "primaryField": null}, {"id": "receivedBy", "header": "Received By", "fieldType": "LOOK_UP", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": {"entity": "USER", "lookupUrl": "/users/lookup?q=name:"}, "picklist": null, "primaryField": null}, {"id": "createdAt", "header": "Mailed At", "fieldType": "DATETIME_PICKER", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": null, "primaryField": null}, {"id": "subject", "header": "Subject", "fieldType": "TEXT_FIELD", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": null, "primaryField": null}, {"id": "associatedLeads", "header": "Associated Leads", "fieldType": "LOOK_UP", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "active": true, "showDefaultOptions": true, "lookup": {"entity": "LEAD", "lookupUrl": "/search/lead/lookup?q=firstName:"}, "picklist": null, "primaryField": null}, {"id": "associatedDeals", "header": "Associated Deals", "fieldType": "LOOK_UP", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "active": true, "showDefaultOptions": true, "lookup": {"entity": "DEAL", "lookupUrl": "/search/deal/lookup?q=name:"}, "picklist": null, "primaryField": null}, {"id": "associatedContacts", "header": "Associated Contacts", "fieldType": "LOOK_UP", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "active": true, "showDefaultOptions": true, "lookup": {"entity": "CONTACT", "lookupUrl": "/search/contact/lookup?q=firstName:"}, "picklist": null, "primaryField": null}, {"id": "direction", "header": "Email Type", "fieldType": "PICK_LIST", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": true, "isRequired": true, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": {"id": 1, "displayName": "Email Type Picklist", "picklistValues": [{"id": 1, "displayName": "Received", "name": "received", "systemDefault": true}, {"id": 2, "displayName": "<PERSON><PERSON>", "name": "sent", "systemDefault": true}]}, "primaryField": null}, {"id": "status", "header": "Status", "fieldType": "PICK_LIST", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": true, "isRequired": true, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": {"id": 1, "displayName": "Status Picklist", "picklistValues": [{"id": 1, "displayName": "Draft", "name": "draft", "systemDefault": true}, {"id": 2, "displayName": "<PERSON><PERSON>", "name": "sent", "systemDefault": true}, {"id": 3, "displayName": "Opened", "name": "opened", "systemDefault": true}, {"id": 4, "displayName": "Sending", "name": "sending", "systemDefault": true}, {"id": 5, "displayName": "Failed", "name": "failed", "systemDefault": true}, {"id": 6, "displayName": "Received", "name": "received", "systemDefault": true}]}, "primaryField": null}, {"id": "userFields", "header": "User Fields", "fieldType": "ENTITY_FIELDS", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "active": true, "showDefaultOptions": true, "lookup": {"entity": "TEAM", "lookupUrl": "/teams/lookup?q=name:"}, "picklist": null, "primaryField": "user"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "header": "<PERSON><PERSON>", "fieldType": "ENTITY_FIELDS", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "active": true, "showDefaultOptions": true, "lookup": {"entity": "TEAM", "lookupUrl": "/teams/lookup?q=name:"}, "picklist": null, "primaryField": "sentBy"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "header": "Received By <PERSON>", "fieldType": "ENTITY_FIELDS", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "active": true, "showDefaultOptions": true, "lookup": {"entity": "TEAM", "lookupUrl": "/teams/lookup?q=name:"}, "picklist": null, "primaryField": "receivedBy"}, {"id": "attachments", "header": "Attachments", "fieldType": "FILE_PICKER", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": false, "active": true, "showDefaultOptions": false, "lookup": null, "picklist": null, "primaryField": null}, {"id": "read", "header": "Read", "fieldType": "TOGGLE", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": false, "active": true, "showDefaultOptions": false, "lookup": null, "picklist": null, "primaryField": null}, {"id": "openedAt", "header": "Opened At", "fieldType": "DATETIME_PICKER", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": false, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": null, "primaryField": null}, {"id": "clickedAt", "header": "Clicked At", "fieldType": "DATETIME_PICKER", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": false, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": null, "primaryField": null}, {"id": "relatedTo", "header": "Related To", "fieldType": "ENTITY_LOOKUP", "isStandard": true, "isSortable": false, "isFilterable": false, "isInternal": false, "isRequired": false, "active": true, "lookup": null, "picklist": {"id": 1, "displayName": "Entity Picklist", "picklistValues": [{"id": 0, "displayName": "LEAD", "name": "LEAD", "lookupUrl": "/search/lead/lookup?converted=false&q=firstName:", "systemDefault": false, "disabled": false}, {"id": 1, "displayName": "DEAL", "name": "DEAL", "lookupUrl": "/deals/lookup?view=task&q=name:", "systemDefault": false, "disabled": false}, {"id": 2, "displayName": "CONTACT", "name": "CONTACT", "lookupUrl": "/search/contact/lookup?q=firstName:", "systemDefault": false, "disabled": false}]}}]}}, "defaultConfig": {"fields": []}}