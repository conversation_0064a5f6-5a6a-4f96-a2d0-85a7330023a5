class Email < ApplicationRecord
  belongs_to :owner, class_name: 'User'
  belongs_to :sender, class_name: 'LookU<PERSON>'
  belongs_to :connected_account
  belongs_to :email_thread, optional: true
  belongs_to :tenant

  has_many :email_look_ups, -> { where related: true }, dependent: :destroy
  has_many :to_email_look_ups, -> { where recipient_type: :to }, class_name: '<PERSON><PERSON><PERSON>ookUp', dependent: :destroy
  has_many :cc_email_look_ups, -> { where recipient_type: :cc }, class_name: '<PERSON><PERSON><PERSON>ookUp', dependent: :destroy
  has_many :bcc_email_look_ups, -> { where recipient_type: :bcc }, class_name: 'EmailLookUp', dependent: :destroy

  has_many :related_to, through: :email_look_ups, source: :look_up
  has_many :to_recipients, through: :to_email_look_ups, source: :look_up
  has_many :cc_recipients, through: :cc_email_look_ups, source: :look_up
  has_many :bcc_recipients, through: :bcc_email_look_ups, source: :look_up

  has_many :attachments, dependent: :destroy
  has_one :track_mapping, dependent: :destroy
  has_many :email_track_logs, dependent: :destroy
  has_many :email_link_logs, dependent: :destroy
  has_many :link_mappings, dependent: :destroy

  default_scope { where(deleted: false) }

  enum status: { draft: 0, sent: 1, opened: 2, sending:3, failed: 4, received: 5 }
  enum direction: { received: 0, sent: 1 }, _prefix: true
  enum bounce_type: { hard: 0, soft: 1 }, _prefix: true

  # TODO: Fix this validation
  # validate :sender_cannot_mark_read

  before_create do
    self.direction = connected_account.email == from ? SENT : RECEIVED
    self.status = Email.statuses['received'] if self.direction === RECEIVED
    body_summary = ActionView::Base.full_sanitizer.sanitize(self.body)
    self.body_summary = Nokogiri::HTML(body_summary).text.slice(0..100).squish
  end

  def read?
    auth_data = GetSecurityContext.call.result
    read_by.include?(auth_data.user_id)
  end

  def self.usage_per_tenant(tenant_id = nil)
    email_data = group(:tenant_id).select("tenant_id, count(*)")
    email_data = email_data.where(tenant_id: tenant_id) if tenant_id
    email_data = email_data.as_json
    data = []
    email_data.each do |d|
      data << { tenantId: d['tenant_id'], count: d['count'], usageEntity: 'EMAIL'}
    end
    data << { tenantId: tenant_id, count: 0, usageEntity: 'EMAIL'} if (data.blank? && tenant_id.present?)
    data
  end

  private

  def sender_cannot_mark_read
    if read_by.include?(sender.entity_id) && sender.entity_type == LOOKUP_USER && !self.new_record? && self.changes.keys.include?(:read_by)
      errors.add(:base, 'Sender can not mark email as read or unread')
    end
  end
end
