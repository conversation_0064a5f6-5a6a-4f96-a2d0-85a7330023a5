module MessageProcessor
  private

  def save_related_entities email, entities, related_to
    all_look_ups = entities[:related_look_ups]
    save_recipient_lookups('to', email.to, all_look_ups, email)
    save_recipient_lookups('cc', email.cc, all_look_ups, email)
    save_recipient_lookups('bcc', email.bcc, all_look_ups, email)
    save_related_deal_lookups(all_look_ups[:matched], email)
    # save_recipient_lookups('sender', [email.from], all_look_ups, email)
    save_related_to email
  end

  def associated_entities(emails, message, from_email_address: nil)
    email_thread = get_email_thread message
    all_look_ups = get_related_lookups(emails, from_email_address)
    return { email_thread: email_thread, related_look_ups: all_look_ups }
  end

  def get_related_lookups(emails, from_email_address)
    result = { matched: [], unmatched: [] }
    search_leads = { matched: [], unmatched: [] }
    search_contact = { matched: [], unmatched: [] }
    search_user = { matched: [], unmatched: []}
    associated_deal_look_ups = { matched: [], unmatched: [] }

    existing_look_ups = LookUp.where(email: emails, tenant_id: @connected_account.tenant_id).where(entity_type: [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_USER])
    existing_emails = existing_look_ups.map(&:email)
    if existing_look_ups.present?
      result[:matched] = existing_look_ups.map { |look_up| { entity: look_up.entity, email: look_up.email, name: look_up.name, tenant_id: look_up.tenant_id } }
      existing_contact_lookups = existing_look_ups.select { |look_up| look_up.is_a_contact? }
      existing_contact_search_response =
        SearchContactsByIds.call(
          existing_contact_lookups.map { |c| [c.entity_id, c.email] }.to_h,
          @connected_account.tenant_id
        ).result
      if existing_contact_search_response.present? && existing_contact_search_response.count == 1
        matched_contact = existing_contact_search_response.first
        if matched_contact[:email] == from_email_address && matched_contact[:associated_deals].count == 1
          associated_deal_look_ups[:matched] += SearchDeals.call(matched_contact[:associated_deals], @connected_account.tenant_id).result
        end
      elsif existing_contact_search_response.count.positive?
        matched_contact_from_email = existing_contact_search_response.find { |c| c[:email] == from_email_address }
        if matched_contact_from_email.present? && matched_contact_from_email[:associated_deals].try(:count) == 1
          if existing_contact_search_response.all? { |c| c[:associated_deals] == matched_contact_from_email[:associated_deals] }
            associated_deal_look_ups[:matched] += SearchDeals.call(matched_contact_from_email[:associated_deals], @connected_account.tenant_id).result
          end
        end
      end
    end
    email_ids_to_search = emails.reject{ |email_id| existing_emails.include?(email_id) }

    if email_ids_to_search.present?
      search_contact = SearchContacts.call(email_ids_to_search, @connected_account.tenant_id).result
      if search_contact[:matched].count == 1
        matched_contact = search_contact[:matched].first
        if matched_contact[:email] == from_email_address && matched_contact[:associated_deals].count == 1
          associated_deal_look_ups[:matched] += SearchDeals.call(matched_contact[:associated_deals], @connected_account.tenant_id).result
        end
      elsif search_contact[:matched].count.positive?
        matched_contact_from_email = search_contact[:matched].find { |c| c[:email] == from_email_address }
        if matched_contact_from_email.present? && matched_contact_from_email[:associated_deals].try(:count) == 1
          if search_contact[:matched].all? { |c| c[:associated_deals] == matched_contact_from_email[:associated_deals] }
            associated_deal_look_ups[:matched] += SearchDeals.call(matched_contact_from_email[:associated_deals], @connected_account.tenant_id).result
          end
        end
      end
      search_contact[:matched].each { |contact_lookup_hash| contact_lookup_hash.delete(:associated_deals) }

      if search_contact[:unmatched].present?
        search_leads = SearchLeads.call(search_contact[:unmatched].map{|c| c[:email]}, @connected_account.tenant_id).result
        search_contact[:unmatched] = []
        if search_leads[:unmatched].present?
          search_user = SearchUsers.call(search_leads[:unmatched].map{|c| c[:email]}, @connected_account.tenant_id).result
          search_leads[:unmatched] = []
        end
      end
      search_contact.merge!(search_leads) { |result, val1, val2|  (val1 + val2).uniq }
      search_contact.merge!(search_user) { |result, val1, val2|  (val1 + val2).uniq }
      result.merge!(search_contact){|result,val1,val2|  (val1 + val2).uniq }
    end
    result.merge!(associated_deal_look_ups){|result,val1,val2|  (val1 + val2).uniq }
    Rails.logger.info "Result: Final result after merge #{result.inspect}"
    result
  end

  def add_token_in_current_thread
    thread = Thread.current
    thread[:token] = GenerateToken.call(@connected_account.user.id, @connected_account.user.tenant_id).result
  end

  def save_recipient_lookups(type, emails, look_ups, email_record)
    return if emails.blank?

    look_ups = look_ups[:unmatched] + look_ups[:matched]

    emails.each do |email|
      look_ups.each do |look_up|
        if look_up[:email] == email
          look_up = LookUp.find_or_create_by(look_up)
          email_record.send("#{type}_recipients") << look_up

          email_record.related_to << look_up if(look_up.entity.starts_with?('lead') || look_up.entity.starts_with?('contact'))
        end
      end
    end
  end

  def save_related_deal_lookups(look_ups_array, email_record)
    look_ups_array.select { |look_up_item| look_up_item[:entity].starts_with?(LOOKUP_DEAL) }.each do |look_up_item|
      email_record.related_to << LookUp.find_or_create_by(look_up_item)
    end
  end
end
