# frozen_string_literal: true

module UserAccess

  def email_owner?(email)
    user_id.eql?(email.owner_id)
  end

  def conversation_owner?(thread)
    user_id.eql?(thread&.owner_id)
  end

  def user_is_participant_in?(email)
    associated_entities(email).map(&:entity).include?("#{LOOKUP_USER}_#{user_id}")
  end

  def includes_connected_account_email_address?(connected_account, email)
    ([connected_account.try(:email)].compact & [email.to, email.cc, email.bcc, email.from].flatten.compact.reject(&:blank?)).present?
  end

  def user_has_read_all_with_email_on_associated_entity?(email)
    (associated_entities(email).map(&:entity_type).uniq & [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT])
      .any? { |entity_type| can_access?(entity_type, 'read_all') && can_access?(entity_type, 'email') }
  end

  def related_entity_owner?(email)
    associated_entities(email).map(&:owner_id).include?(user_id)
  end

  def shared_entity_owner?(email)
    load_shared_entities
    (
      (associated_entities(email).map(&:owner_id) & @shared_entities.values.map { |entity_values| entity_values[:shared_entity_owners] }.flatten).present? ||
      (associated_entities(email).map(&:entity) & @shared_entities.values.map { |entity_values| entity_values[:shared_entity_formatted] }.flatten).present?
    )
  end

  def can_read_emails?
    can_access?('email', 'read')
  end

  def can_read_all_emails?
    can_access?('email', 'read_all')
  end

  def can_delete_emails?
    can_access?('email', 'delete')
  end

  def can_delete_all_emails?
    can_access?('email', 'delete_all')
  end

  def load_shared_entities
    unless @shared_entites
      @shared_entities ||= {}.with_indifferent_access
      [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity|
        result = SharedAccess.new(entity).fetch
        @shared_entities[entity] = {
          shared_entity_owners: result['accessByOwners'].keys.map(&:to_i),
          shared_entity_formatted: result['accessByRecords'].keys.map { |entity_id| "#{entity}_#{entity_id}" }
        }
      end
    end
  end

  def associated_entities(email)
    (email.related_to + email.to_recipients + email.cc_recipients + email.bcc_recipients + [email.sender]).uniq { |l| l.entity }
  end
end
