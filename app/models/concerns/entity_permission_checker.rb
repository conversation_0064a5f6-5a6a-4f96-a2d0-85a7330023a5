module EntityPermissionChecker

  private

  def has_email_permission_on_entity(type, id)
    return false unless type || id

    case type
    when LOOKUP_LEAD
      entity = get_lead_details(id)
    when LOOKUP_DEAL
      entity = get_deal_details(id)
    when LOOKUP_CONTACT
      entity = get_contact_details(id)
    else
      return false
    end

    unless entity
      Rails.logger.info "Entity of type: #{type} not found for id: #{id}"
      return false
    end

    entity['recordActions']['email'] == true
  end

  def get_lead_details(lead_id)
    lead = GetLead.call(lead_id).result
  end

  def get_deal_details(deal_id)
    deal = GetDeal.call(deal_id).result
  end

  def get_contact_details(contact_id)
    contact = GetContact.call(contact_id).result
  end
end