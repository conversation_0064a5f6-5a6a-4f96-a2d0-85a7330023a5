module VariablesProcessor

  private

  def validate_and_replace_variables(category)
    valid_variables = V2::GetVariables.call(category).result

    body = @params[:body]
    subject = @params[:subject]

    # Validate and replace normal variables
    variables_in_body = body.scan(/{{[\s|\w|\/|\[|\!|\@|\-|\#|\=|\$|\;|\%|\^|\&|\*|\(|\)|\|\.|\?|\"|\:|\<|\>|\]|\+]+}}/).map(&:strip).uniq

    conditional_variables_in_body = body.scan(/({{[\s|\w|\/|\[|\!|\@|\-|\#|\=|\$|\;|\%|\^|\&|\*|\(|\)|\|\.|\?|\"|\:|\<|\>|\]|\+]+}, {if missing: [^{}]*[\s|\w|\/]*}})/).flatten.map(&:strip).uniq
    variables_in_body.each do |variable|
      body = validate_and_replace_normal_variable(body, valid_variables, variable)
    end

    conditional_variables_in_body.each do |variable|
      body = validate_and_replace_conditional_variable(body, valid_variables, variable)
    end

    @params[:body] = body

    # Validate and replace conditional variables
    variables_in_subject = subject.scan(/{{[\s|\w|\/|\[|\!|\@|\-|\#|\=|\$|\;|\%|\^|\&|\*|\(|\)|\|\.|\?|\"|\:|\<|\>|\]|\+]+}}/).map(&:strip).uniq
    conditional_variables_in_subject = subject.scan(/({{[\s|\w|\/|\[|\!|\@|\-|\#|\=|\$|\;|\%|\^|\&|\*|\(|\)|\|\.|\?|\"|\:|\<|\>|\]|\+]+}, {if missing: [^{}]*[\s|\w|\/]*}})/).flatten.map(&:strip).uniq
    variables_in_subject.each do |variable|
      subject = validate_and_replace_normal_variable(subject, valid_variables, variable)
    end

    conditional_variables_in_subject.each do |variable|
      subject = validate_and_replace_conditional_variable(subject, valid_variables, variable)
    end

    @params[:subject] = subject
  end

  def validate_and_replace_normal_variable(text, valid_variables, variable_in_text)
    if variable = valid_variables.find { |v| matches_normal_variable?(variable_in_text, v) }
      text = text.gsub(variable_in_text, "{{#{variable['entity']}.#{variable["internalName"]}}}")
    else
      raise(ExceptionHandler::InvalidVariableError, ErrorCode.invalid_variable)
    end
    text
  end

  def validate_and_replace_conditional_variable(text, valid_variables, variable_in_text)
    actual_variable = variable_in_text.scan(/{{[\s|\w|\/|\[|\!|\@|\-|\#|\=|\$|\;|\%|\^|\&|\*|\(|\)|\|\.|\?|\"|\:|\<|\>|\]|\+]+},/).first
    Rails.logger.info "============= Variables =============="
    Rails.logger.info actual_variable
    Rails.logger.info valid_variables
    if variable = valid_variables.find { |v| matches_conditional_variable?(actual_variable, v) }
      text = text.gsub(actual_variable, "{{#{variable['entity']}.#{variable["internalName"]}},")
    else
      raise(ExceptionHandler::InvalidVariableError, ErrorCode.invalid_variable)
    end
    text
  end

  def matches_normal_variable?(variable_in_text, variable)
    (
      Nokogiri::HTML.parse(variable_in_text).text == "{{#{variable['displayName']}}}" ||
      Nokogiri::HTML.parse(variable_in_text).text == "{{#{variable['entity']}.#{variable['internalName']}}}" ||
      Nokogiri::HTML.parse(variable_in_text).text == "{{#{variable['internalName']}}}"
    )
  end

  def matches_conditional_variable?(variable_in_text, variable)
    (
      Nokogiri::HTML.parse(variable_in_text).text == "{{#{variable['displayName']}}," ||
      Nokogiri::HTML.parse(variable_in_text).text == "{{#{variable['entity']}.#{variable['internalName']}}," ||
      Nokogiri::HTML.parse(variable_in_text).text == "{{#{variable['internalName']}},"
    )
  end
end
