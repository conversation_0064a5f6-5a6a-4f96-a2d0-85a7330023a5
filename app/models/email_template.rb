class EmailTemplate < ApplicationRecord
  belongs_to :created_by, class_name: 'User'
  belongs_to :updated_by, class_name: 'User'

  has_many :template_attachments, dependent: :destroy

  validates :name, :tenant_id, :category, :created_by_id, :updated_by_id, presence: true
  validates :active, inclusion: { in: [ true, false ] }

  validates_uniqueness_of :name, scope: :tenant_id, case_sensitive: false
  scope :active, -> { where(active: true) }
  after_update :publish_email_template_usage, if: Proc.new{|t| t.saved_change_to_active?}
  after_create :publish_email_template_usage

  def self.usage_per_tenant(tenant_id = nil)
    email_template_data = active.group(:tenant_id).select('tenant_id, count(*)')
    email_template_data = email_template_data.where(tenant_id: tenant_id) if tenant_id
    email_template_data = email_template_data.as_json
    data = []
    email_template_data.each do |d|
      data << { tenantId: d['tenant_id'], count: d['count'], usageEntity: 'EMAIL_TEMPLATE'}
    end
    data
  end

  def get_record_actions
    auth_data = GetSecurityContext.call.result
    email_template_permission = auth_data.permissions.find{ |p| p.name == 'email_template' }

    can_update_all = email_template_permission&.action&.update_all && self.tenant_id.to_s.eql?(auth_data.tenant_id.to_s)
    can_update_own = email_template_permission&.action&.update && self.created_by_id.to_s.eql?(auth_data.user_id.to_s)
    can_read_all   = email_template_permission&.action&.read_all && self.tenant_id.to_s.eql?(auth_data.tenant_id.to_s)
    can_read_own   = email_template_permission&.action&.read && self.created_by_id.to_s.eql?(auth_data.user_id.to_s)

    { update: can_update_all || can_update_own,  read: can_read_all || can_read_own }
  end

  def publish_email_template_usage
    TenantEmailTemplateUsagePublisher.publish(tenant_id)
  end
end
