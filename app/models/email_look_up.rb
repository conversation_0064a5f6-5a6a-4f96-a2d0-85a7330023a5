class EmailLookUp < ApplicationRecord
  enum recipient_type: [:to, :cc, :bcc]

  belongs_to :look_up
  belongs_to :email

  before_create :set_tenant_id
  after_create :publish_entity_metadata

  default_scope { where(deleted: false) }

  private

  def set_tenant_id
    self.tenant_id = email.tenant_id
  end

  def publish_entity_metadata
    return unless [LOOKUP_DEAL, LOOKUP_LEAD].include? look_up.entity_type
    return unless email.sender.entity_type.eql? LOOKUP_USER
    Rails.logger.info "EmailLookUp Callback: #{look_up.metadata}"
    EmailMetadataPublisher.call(email, look_up)
  end
end
