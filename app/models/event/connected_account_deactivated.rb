# frozen_string_literal: true

class Event::ConnectedAccountDeactivated
  include ActiveModel::Model

  attr_accessor(
    :params
  )

  def initialize params
    @params = params
  end

  def routing_key
    if params['account_type'] == EMAIL_OAUTH
      EMAIL_OAUTH_DISCONNECTED
    else
      EMAIL_CLIENT_DISCONNECTED
    end
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    {
      tenantId: params['tenant_id'],
      userId: params['user_id'],
      disconnectedBy: params['disconnected_by'],
      userEmail: params['user_email'],
      tenantEmail: params['tenant_email'],
      connectedEmail: params['connected_email']
    }.to_json
  end
end
