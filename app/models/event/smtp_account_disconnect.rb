class Event::SmtpAccountDisconnect
  include ActiveModel::Model

  attr_accessor(
    :userId,
    :tenantId,
    :email,
    :reason
  )

  def initialize user_id, tenant_id, email, reason
    @userId = user_id
    @tenantId = tenant_id
    @reason = reason
    @email = email
  end

  def routing_key
    SMTP_ACCOUNT_DISCONNECT_EVENT
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    super
  end
end
