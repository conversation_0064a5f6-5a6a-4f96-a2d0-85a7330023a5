# frozen_string_literal: true

class Event::LookUpUpdated
  include ActiveModel::Model

  attr_accessor(
    :params
  )

  def initialize params
    @params = params
  end

  def routing_key
    EMAIL_LOOKUP_UPDATED
  end

  def as_json(options = {})
    {
      entityId: params[:entity_id],
      entityType: params[:entity_type],
      name: params[:name],
      ownerId: params[:owner_id],
      tenantId: params[:tenant_id]
    }
  end

  def to_json(options = {})
    as_json.to_json
  end
end
