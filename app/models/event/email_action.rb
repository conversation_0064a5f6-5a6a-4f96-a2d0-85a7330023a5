class Event::EmailAction
  include ActiveModel::Model

  attr_accessor(
    :data, :action
  )

  def initialize(data, action)
    @data   = data
    @action = action
  end

  def routing_key
    return EMAIL_CREATED if action.eql?(CREATE_ACTION)
    return EMAIL_DELETED if action.eql?(DELETE_ACTION)
    return EMAIL_OPENED_NOTIFY_USER if action.eql?(OPEN_ACTION)
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    data.to_json
  end
end
