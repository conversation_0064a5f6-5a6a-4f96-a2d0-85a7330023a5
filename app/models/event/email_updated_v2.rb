# frozen_string_literal: true

class Event::EmailUpdatedV2

  def initialize(email, old_serialized_email_data)
    @email = email
    @old_serialized_email_data = old_serialized_email_data
  end

  def routing_key
    EMAIL_UPDATED_V2
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    {
      entity: EmailDetailsSerializer.call(@email, false, nil, true, add_owner_id: true).result,
      oldEntity: @old_serialized_email_data,
      metadata: {
        tenantId: @email.tenant_id,
        userId: @email.owner.id,
        entityType: "EMAIL",
        entityId: @email.id,
        entityAction: "UPDATED",
        executedWorkflows: @email.metadata&.dig('executedWorkflows') || []
      }
    }.to_json
  end
end
