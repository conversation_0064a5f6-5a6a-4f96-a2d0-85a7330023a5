# frozen_string_literal: true

class Event::EmailDeletedWorkflowV2
  def initialize(deleted_email_serialized_data)
    @deleted_email_serialized_data = deleted_email_serialized_data
  end

  def routing_key
    EMAIL_DELETED_WORKFLOW_V2
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    {
      entity: nil,
      oldEntity: @deleted_email_serialized_data,
      metadata: {
        tenantId: @deleted_email_serialized_data['tenantId'],
        userId: @deleted_email_serialized_data['deletedBy']['id'],
        entityType: "EMAIL",
        entityId: @deleted_email_serialized_data['id'],
        entityAction: "DELETED"
      }
    }.to_json
  end
end
