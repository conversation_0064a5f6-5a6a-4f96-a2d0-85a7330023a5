# frozen_string_literal: true

class Event::EmailClicked

  def initialize(email, old_serialized_email_data)
    @email = email
    @old_serialized_email_data = old_serialized_email_data
  end

  def routing_key
    EMAIL_CLICKED
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    {
      entity: EmailDetailsSerializer.call(@email, false, nil, true, add_owner_id: true).result,
      oldEntity: @old_serialized_email_data,
      metadata: {
        tenantId: @email.tenant_id,
        userId: @email.owner.id,
        entityType: "EMAIL",
        entityId: @email.id,
        entityAction: "EMAIL_CLICKED",
        executedWorkflows: @email.metadata&.dig('executedWorkflows') || []
      }
    }.to_json
  end
end
