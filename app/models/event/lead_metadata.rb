class Event::LeadMetadata
  include ActiveModel::Model

  attr_accessor(
    :createdAt,
    :leadId,
    :tenantId,
    :ownerId,
    :entityType,
    :metadata
  )

  def initialize email, look_up
    @createdAt = email.created_at
    @leadId = look_up.entity_id
    @tenantId = email.tenant_id
    @ownerId = email.owner_id
    @entityType = 'EMAIL'
    @metadata = look_up.metadata
  end

  def routing_key
    "email.update.lead.metaInfo"
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    super
  end
end
