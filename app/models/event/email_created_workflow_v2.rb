# frozen_string_literal: true

class Event::EmailCreatedWorkflowV2
  def initialize(email_serialized_data, metadata = {})
    @email_serialized_data = email_serialized_data
    @metadata = metadata&.with_indifferent_access || {}
  end

  def routing_key
    EMAIL_CREATED_WORKFLOW_V2
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    {
      entity: @email_serialized_data,
      oldEntity: nil,
      metadata: {
        tenantId: @email_serialized_data['tenantId'],
        userId: @email_serialized_data['owner']['id'],
        entityType: "EMAIL",
        entityId: @email_serialized_data['id'],
        entityAction: "CREATED",
        workflowId: @metadata[:workflowId],
        executedWorkflows: @metadata[:executedWorkflows],
        executeWorkflow: @metadata[:executeWorkflow].nil? ? true : @metadata[:executeWorkflow],
        workflowName: @metadata[:workflowName]
      }
    }.to_json
  end
end
