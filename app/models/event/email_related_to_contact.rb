class Event::EmailRelatedToContact
  include ActiveModel::Model

  attr_accessor(
    :emailId,
    :tenantId,
    :entityType,
    :entityId,
    :fromType,
    :fromId,
    :toType,
    :toId
  )

  def initialize from_user, to_user_id, related_to_id, email_id
    @emailId = email_id
    @tenantId = from_user[:tenant_id]
    @entityType = LOOKUP_CONTACT.upcase
    @entityId = related_to_id
    @fromType = LOOKUP_USER.upcase
    @fromId = from_user[:id]
    @toType = LOOKUP_USER.upcase
    @toId = to_user_id
  end

  def routing_key
    "email.relatedto.contact"
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    super
  end
end
