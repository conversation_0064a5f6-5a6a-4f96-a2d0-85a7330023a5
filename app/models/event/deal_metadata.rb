class Event::DealMetadata
  include ActiveModel::Model

  attr_accessor(
    :createdAt,
    :dealId,
    :tenantId,
    :ownerId,
    :entityType,
    :metadata
  )

  def initialize email, look_up
    @createdAt = email.created_at
    @dealId = look_up.entity_id
    @tenantId = email.tenant_id
    @ownerId = email.owner_id
    @entityType = 'EMAIL'
    @metadata = look_up.metadata
  end

  def routing_key
    "email.update.deal.metaInfo"
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    super
  end
end
