class LookUp < ApplicationRecord
  has_many :email_look_ups
  has_many :emails, through: :email_look_ups

  validates :entity, presence: true
  validates :entity_type, presence: true
  validate :entity_data
  validates :email, allow_nil: true, allow_blank: true, format: {with: URI::MailTo::EMAIL_REGEXP}

  attr_accessor :metadata
  #validate :freeze_entity_once_created, on: :update

  default_scope { where(deleted: false) }

  before_validation do
    if entity.blank?
      self.entity = "#{self.entity_type}_#{self.entity_id}"
    elsif entity_type.blank?
      data = self.entity.split('_')
      self.entity_type = data[0]
      self.entity_id = data[1]
    end
  end

  def is_a_user?
    entity_type == LOOKUP_USER
  end

  def is_a_lead?
    entity_type == LOOKUP_LEAD
  end

  def is_a_contact?
    entity_type == LOOKUP_CONTACT
  end

  def is_a_deal?
    entity_type == LOOKUP_DEAL
  end

  def is_a_custom_email?
    entity_type == LOOKUP_CUSTOM_EMAIL
  end

  private
  def entity_data
    return unless entity.present?
    return if is_a_custom_email?
    value = entity.split('_')
    errors.add(:entity, 'Invalid Value') if value.length != 2 || !LOOKUP_TYPES.include?(value[0]) || (value[1].to_i == 0)
  end

  def freeze_entity_once_created
    errors.add(:entity, 'Lookup entity cannot be modified') if changes[:entity].present?
  end
end
