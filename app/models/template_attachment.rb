class TemplateAttachment < ApplicationRecord
  belongs_to :email_template

   def extract_file_name with_time_stamp = true
    regex = with_time_stamp ? Regexp.new(/[^>]*\/[^>]*\/[0-9]+_([^>]*)\.([^>]*)/) : Regexp.new(/[^>]*\/[^>]*\/[0-9]+_([^>]*)_[^>]*\.([^>]*)/)
    file_name.scan(regex).flatten.join(".") rescue file_name
  end

  def self.usage_per_tenant(tenant_id = nil)
    data = []
    attachment_data = joins(:email_template).group(:tenant_id).select("tenant_id, sum(file_size) as count")
    attachment_data = attachment_data.where("email_templates.tenant_id" => tenant_id) if tenant_id
    attachment_data = attachment_data.as_json
    attachment_data.each do |d|
      data << { tenantId: d['tenant_id'], count: d['count'].to_i, usageEntity: 'STORAGE_EMAIL_ATTACHMENT'}
    end
    data
  end

  def self.total_attachment_size_for_tenant tenant_id
    joins(:email_template).where('email_templates.tenant_id = ?', tenant_id).sum(:file_size).to_i
  end
end
