class ErrorCode
  def self.not_found(record = 'record')
    '01602001'
  end

  def self.bad_data
    '01602002'
  end

  def self.internal_error
    '01602003'
  end

  def self.invalid_credentials
    '01601001'
  end

  def self.invalid_token
    '01601002'
  end

  def self.missing_token
    '01601003'
  end

  def self.expired_token
    '01601004'
  end

  def self.unauthorized
    '01601005'
  end

  def self.create_email_template_not_allowed
    '01601006'
  end

  def self.read_email_template_not_allowed
    '01601007'
  end

  def self.update_email_template_not_allowed
    '01601008'
  end

  def self.invalid
    '01603001'
  end

  def self.third_party_api
    '01603006'
  end

  def self.third_party_api_auth
    '01603007'
  end

  def self.not_connected
    '01603008'
  end

  def self.reply_not_allowed
    '01603009'
  end

  def self.invalid_variable
    '01603010'
  end

  def self.duplicate_template
    '01603011'
  end

  def self.invalid_mail_provider_name
    '01603012'
  end

  def self.outlook_authentication_failed
    '01603013'
  end

  def self.delete_not_allowed
    '01603014'
  end

  def self.inactive_email_template
    '01603015'
  end

  def self.rate_limit_error
    '01603016'
  end

  def self.smtp_unauthorized
    '01603017'
  end

  def self.unsupported_media_type
    '01603018'
  end

  def self.not_implemented
    '01603019'
  end

  def self.bad_gateway
    '01603020'
  end

  def self.service_unavailable
    '01603021'
  end

  def self.gateway_timeout
    '01603022'
  end

  def self.email_thread_not_found
    '01603023'
  end
end
