module ExceptionHandler
  extend ActiveSupport::Concern

  SERVICE_CODE = '016'
  MODULE_SECURITY = '01'
  MODULE_API = '02'
  MODULE_DOMAIN = '03'

  # Define custom error subclasses - rescue catches `StandardErrors`
  class AuthenticationError < StandardError; end
  class InternalServerError < StandardError; end
  class InvalidDataError < StandardError; end
  class EmailThreadNotFoundError < StandardError; end
  class MissingToken < StandardError; end
  class InvalidToken < StandardError; end
  class Forbidden < StandardError; end
  class ThirdPartyAPIError < StandardError; end
  class ThirdPartyAPIAuthError < StandardError; end
  class NotConnectedError < StandardError; end
  class CreateEmailTemplateNotAllowedError < StandardError; end
  class ReadEmailTemplateNotAllowedError < StandardError; end
  class UpdateEmailTemplateNotAllowedError < StandardError; end
  class InvalidVariableError < StandardError; end
  class DuplicateTemplateNameError < StandardError; end
  class InvalidMailProviderNameError < StandardError; end
  class OutlookAuthenticationError < StandardError; end
  class DeleteNotAllowedError < StandardError; end
  class NotFound < StandardError; end

  included do
    # Define custom handlers
    rescue_from ExceptionHandler::AuthenticationError do |e|
      json_response(
        {
          errorCode: e.message.presence || ErrorCode.invalid_credentials
        },
        :unauthorized
      )
    end

    rescue_from ExceptionHandler::CreateEmailTemplateNotAllowedError do |e|
      json_response(
        {
          errorCode: ErrorCode.create_email_template_not_allowed
        },
        :forbidden
      )
    end

    rescue_from ExceptionHandler::ReadEmailTemplateNotAllowedError do |e|
      json_response(
        {
          errorCode: ErrorCode.read_email_template_not_allowed
        },
        :forbidden
      )
    end

    rescue_from ExceptionHandler::UpdateEmailTemplateNotAllowedError do |e|
      json_response(
        {
          errorCode: ErrorCode.update_email_template_not_allowed
        },
        :forbidden
      )
    end

    rescue_from ExceptionHandler::InternalServerError do |e|
      json_response(
        {
          errorCode: ErrorCode.internal_error
        },
        :internal_server_error
      )
    end
    rescue_from ExceptionHandler::MissingToken do |e|
      json_response(
        {
          errorCode: ErrorCode.missing_token
        },
        :unauthorized
      )
    end

    rescue_from ExceptionHandler::InvalidToken do |e|
      json_response(
        {
          errorCode: ErrorCode.invalid_token
        },
        :unauthorized
      )
    end

    rescue_from ExceptionHandler::Forbidden do |e|
      json_response(
        {
          errorCode: ErrorCode.unauthorized
        },
        :unauthorized
      )
    end

    rescue_from ExceptionHandler::InvalidDataError do |e|
      json_response(
        {
          errorCode: e.message.presence || ErrorCode.invalid
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::EmailThreadNotFoundError do |e|
      json_response(
        {
          errorCode: e.message.presence || ErrorCode.email_thread_not_found
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::InvalidVariableError do |e|
      json_response(
        {
          errorCode: ErrorCode.invalid_variable
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::DuplicateTemplateNameError do |e|
      json_response(
        {
          errorCode: ErrorCode.duplicate_template
        },
        :unprocessable_entity
      )
    end

    rescue_from ActiveRecord::RecordNotFound do |e|
      json_response(
        {
          errorCode: ErrorCode.not_found
        },
        :not_found
      )
    end

    rescue_from ExceptionHandler::ThirdPartyAPIError do |e|
      json_response(
        {
          errorCode: ErrorCode.third_party_api
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::ThirdPartyAPIAuthError do |e|
      json_response(
        {
          errorCode: ErrorCode.third_party_api_auth
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::NotConnectedError do |e|
      json_response(
        {
          errorCode: ErrorCode.not_connected
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::InvalidMailProviderNameError do |e|
      json_response(
        {
          errorCode: ErrorCode.invalid_mail_provider_name
        },
        :unprocessable_entity
      )
    end

    rescue_from ExceptionHandler::OutlookAuthenticationError do |e|
      json_response(
        {
          errorCode: ErrorCode.outlook_authentication_failed
        },
        :forbidden
      )
    end

    rescue_from ExceptionHandler::DeleteNotAllowedError do |e|
      json_response(
        {
          errorCode: ErrorCode.delete_not_allowed
        },
        :forbidden
      )
    end

    rescue_from ExceptionHandler::NotFound do |e|
      json_response(
        {
          errorCode: ErrorCode.not_found
        },
        :not_found
      )
    end

=begin
    rescue_from ActiveRecord::ConnectionTimeoutError  do |e|
      Rails.logger.error "ActiveRecord::ConnectionTimeoutError stats: #{ActiveRecord::Base.connection_pool.stat}"
    end
=end
  end
end
