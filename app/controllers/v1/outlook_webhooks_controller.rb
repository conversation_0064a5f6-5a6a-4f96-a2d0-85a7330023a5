module V1
  class OutlookWebhooksController < ApplicationController
    skip_before_action :authenticate, only: :create

    def create
      token = params['validationToken']
      render plain: token and return unless params['value']
      unless ConnectedAccount.find_by(subscription_id: params['value'][0]['subscriptionId'], active: true).present?
        Rails.logger.info "Outlook suscription does not exist with subscription id: #{params['value'][0]['subscriptionId']}"
      else
        OutlookWebhookPublisher.call(params['value'].as_json)
      end
      render plain: token
    end
  end
end
