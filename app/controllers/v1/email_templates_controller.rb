# frozen_string_literal: true

module V1
  class EmailTemplatesController < ApplicationController
    def create
      command = CreateEmailTemplate.call(email_template_params)
      if command.success?
        json_response(Response::Created.new(command.result).output, :created)
      end
    end

    def update
      command = UpdateEmailTemplate.call(email_template_params)

      if command.success?
        json_response(EmailTemplateSerializer.call(command.result).result, :ok)
      end
    end

    def search
      command = GetEmailTemplates.call(search_params)
      if command.success?
        json_response(EmailTemplateSearchResultsSerializer.call(command.result).result, :ok)
      end
    end

    def lookup
      command = GetEmailTemplates.new(lookup_params).lookup
      if command.success?
        json_response(EmailTemplateSearchResultsSerializer.new(command.result).serialize_lookup, :ok)
      end
    end

    def variables
      command = V1::GetVariables.call(params[:category])
      if command.success?
        json_response({variables: command.result}, :ok)
      end
    end

    def show
      command = GetEmailTemplateDetails.call(params[:id])
      if command.success?
        result = command.result
        json_response(EmailTemplateSerializer.call(result[:email_template], result[:body], result[:subject]).result, :ok)
      end
    end

    def details_with_entity
      command = GetEmailTemplateWithEntityDetails.call(params[:id], params[:entity_id])
      if command.success?
        result = command.result
        json_response(EmailTemplateSerializer.call(result[:email_template], result[:body], result[:subject]).result, :ok)
      end
    end

    def activate
      ActivateDeactivateEmailTemplate.call({id: params[:id], active: true})
    end

    def deactivate
      ActivateDeactivateEmailTemplate.call({id: params[:id], active: false})
    end

    def download_attachment
      attachment = TemplateAttachment.find_by(id: params[:attachment_id], email_template_id: params[:id])
      raise(ActiveRecord::RecordNotFound, ErrorCode.not_found("email template: #{params[:id]}, attachment: #{params[:attachment_id]},")) unless attachment.present?

      command = GetPresignedUrlFromS3.call(attachment, S3_EMAIL_TEMPLATE_BUCKET)
      json_response(command.result) if command.success?
    end

    private

    def email_template_params
      params.permit(
        :id,
        :name,
        :active,
        :category,
        :subject,
        :body,
        {
          attachments: [:id, :data, :fileName]
        }
      )
    end

    def search_params
      params.permit(
        :page,
        :size,
        :sort,
        {
          jsonRule: [
            rules: [
              :operator,
              :field,
              :value,
            ]
          ]
        }
      )
    end

    def lookup_params
      params.permit(:category, :q)
    end
  end
end
