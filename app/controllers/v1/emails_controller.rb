module V1
  class EmailsController < ApplicationController
    before_action :convert_params_to_json, only: [:create, :forward]

    def create
      command = CreateEmail.call(email_params)
      if command.success?
        json_response(Response::Created.new(command.result).output, :created)
      end
    end

    def mark_as_read
      is_thread_read =  MarkAsReadUnread.call({ email_id: params[:id], read: true }).result

      json_response({ isThreadMarkedAsRead: is_thread_read }, :ok)
    end

    def mark_as_unread
      is_thread_read = MarkAsReadUnread.call({ email_id: params[:id], read: false }).result

      json_response({ isThreadMarkedAsRead: is_thread_read }, :ok)
    end

    def index
      command = GetEmailsForThread.call(thread_params)
      if command.success?
        json_response(ThreadEmailsSerializer.call(command.result).result, :ok)
      end
    end

    def forward
      command = ForwardEmail.call(email_params)
      json_response(Response::Created.new(command.result).output, :created) if command.success?
    end

    def download_attachment
      command = DownloadAttachment.call(params[:id], params[:attachment_id])
      json_response(command.result) if command.success?
    end

    def destroy
      command = DeleteEmail.call(params[:id])
      head :ok if command.success?
    end

    def send_email
      command = SendEmailToEntity.call(email_params)
      json_response({}, :ok) if command.success?
    end

    def smart_list_records_count
      command = SmartListRecordsCount.call(smart_list_records_count_params['id'])
      json_response(command.result, :ok) if command.success?
    end

    def show
      command = GetEmailDetails.call(params[:id])
      json_response(command.result, :ok) if command.success?
    end

    def history
      command = GetEmailHistory.call(params.permit!)
      json_response(V1::EmailHistoryResultsSerializer.call(command.result).result, :ok) if command.success?
    end

    private

    def convert_params_to_json
      if params[:relatedTo].class.eql?(String)
        params[:relatedTo] = JSON.parse(params[:relatedTo])
        params[:to] = JSON.parse(params[:to])
        params[:cc] = JSON.parse(params[:cc])
        params[:bcc] = JSON.parse(params[:bcc])
      end
    end

    def email_params
      case params[:action]
      when 'send_email'
        params.permit(
          :id,
          :body,
          :subject,
          :emailTemplateId,
          :trackingEnabled,
          {
            to: [ :entity, :id, :name, :email ]
          },
          {
            cc: [ :entity, :id, :name, :email ]
          },
          {
            bcc: [ :entity, :id, :name, :email ]
          },
          {
            relatedTo: [ :entity, :id, :name, :email ]
          },
          {
            attachments: [:data, :id, :fileName]
          },
          {
            emailTemplateAttachments: [:id, :fileName]
          },
          {
            campaign: [:id, :name]
          },
          {
            activity: [:id, :name]
          }
        )
      else
        params.permit(
          :id,
          :body,
          :subject,
          :emailTemplateId,
          :trackingEnabled,
          {
            to: [ :entity, :id, :name, :email ]
          },
          {
            cc: [ :entity, :id, :name, :email ]
          },
          {
            bcc: [ :entity, :id, :name, :email ]
          },
          {
            relatedTo: [ :entity, :id, :name, :email ]
          },
          {
            attachments: [:data, :id, :fileName]
          },
          {
            emailTemplateAttachments: [:id, :fileName]
          }
        )
      end
    end

    def thread_params
      params.permit(
        :id,
        :page,
        :size
      )
    end

    def smart_list_records_count_params
      current_query_string = URI(request.url).query
      id_values =
        URI.decode_www_form(current_query_string).select { |pair| pair[0] == 'id' }.collect { |pair| pair[1] }
      params['id'] = id_values
      params.permit(id: [])
    end
  end
end
