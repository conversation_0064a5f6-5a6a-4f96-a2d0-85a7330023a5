module V1
  class EmailThreadsController < ApplicationController
    before_action :convert_params_to_json, only: [:reply]
    def search
      command = GetEmails.call(params.permit!)
      if command.success?
        json_response(V1::EmailSearchResultsSerializer.call(command.result, params[:sort]).result, :ok)
      end
    end

    def mark_as_read
      MarkAsReadUnread.call({ thread_id: params[:id], read: true })
    end

    def mark_as_unread
      MarkAsReadUnread.call({ thread_id: params[:id], read: false })
    end

    def reply
      command = ReplyOnEmail.call(email_params)
      if command.success?
        json_response(Response::Created.new(command.result).output, :created)
      end
    end

    def destroy
      command = DeleteEmailThread.call(params[:id], params[:publishUsage])
      head :ok if command.success?
    end

    def associate_with_deals
      command = AssociateEmailThreadWithDeals.call(params.permit(:id, { deals: [:id, :name]}))
      head :ok if command.success?
    end

    private

    def convert_params_to_json
      if params[:relatedTo].class.eql?(String)
        params[:relatedTo] = JSON.parse(params[:relatedTo])
        params[:to] = JSON.parse(params[:to])
        params[:cc] = JSON.parse(params[:cc])
        params[:bcc] = JSON.parse(params[:bcc])
      end
    end

    def email_params
      params.permit(
        :id,
        :replyTo,
        :body,
        :subject,
        :trackingEnabled,
        {
          to: [ :entity, :id, :name, :email ]
        },
        {
          cc: [ :entity, :id, :name, :email ]
        },
        {
          bcc: [ :entity, :id, :name, :email ]
        },
        {
          relatedTo: [ :entity, :id, :name, :email ]
        },
        {
          attachments: [:data, :fileName]
        },
        {
          emailTemplateAttachments: [:id, :fileName]
        }
      )
    end
  end
end
