module V1
  class EmailHistoryResultsSerializer
    prepend SimpleCommand
    def initialize(emails)
      @email_paginated_data = emails
      @emails = emails

      @auth_data = GetSecurityContext.call.result
    end

    def call
      return nil unless @emails

      json = Jbuilder.new
      json.body do
        json.content(@emails) do |email|
          json.id email.id
          json.from get_recipient_json([email.from])
          json.to get_recipient_json(email.to)
          json.cc get_recipient_json(email.cc)
          json.bcc get_recipient_json(email.bcc)
          json.subject email.subject
          json.body get_plain_text_body(email.body)
          json.sentAt email.created_at
          json.direction email.direction
        end

        json.page do
          json.no @email_paginated_data.current_page.to_i
          json.size @email_paginated_data.per_page
        end
        json.totalElements @email_paginated_data.total_entries
        json.totalPages @email_paginated_data.total_pages
        json.first @email_paginated_data.previous_page.nil?
        json.last @email_paginated_data.next_page.nil?
      end
    end

    private

    def get_plain_text_body body
      body = ActionView::Base.full_sanitizer.sanitize(body)
      Nokogiri::HTML(body).text.squish
    end

    def get_recipient_json email_ids
      return [] if email_ids.blank?
      email_ids.map{|email| { email: email} }
    end
  end
end
