class EmailDetailsSerializer
  prepend SimpleCommand

  def initialize(email, user_context = true, deleted_by = nil, additional_attributes = false, send_email_ids_matching_global_message_id = false, add_owner_id: false, send_body: false)
    @email = email
    @user_context = user_context
    @deleted_by = deleted_by
    @additional_attributes = additional_attributes
    @send_email_ids_matching_global_message_id = send_email_ids_matching_global_message_id
    @add_owner_id = add_owner_id
    @send_body = send_body
  end

  def call
    return nil unless @email
    JSON(
      Jbuilder.new do |email|
        email.(@email, :id)
        email.body get_body email
        email.sender LookUpSerializer.call(@email.sender, @add_owner_id).result
        email.relatedTo @email.related_to.uniq.collect{|r| LookUpSerializer.call(r, @add_owner_id).result}
        email.toRecipients @email.to_recipients.collect{|r| LookUpSerializer.call(r, @add_owner_id).result}
        email.ccRecipients @email.cc_recipients.collect{|r| LookUpSerializer.call(r, @add_owner_id).result}
        email.bccRecipients @email.bcc_recipients.collect{|r| LookUpSerializer.call(r, @add_owner_id).result}
        email.subject @email.subject.present? ? @email.subject.to_s.force_encoding("utf-8") : 'No Subject'
        email.sentAt @email.created_at
        email.status @email.status
        email.trackingEnabled @email.tracking_enabled
        email.tenantId @email.tenant_id
        email.read @email.read? if @user_context
        email.threadId @email.email_thread_id
        email.globalMessageId @email.global_message_id
        if @deleted_by.present?
          email.deletedBy  UserSerializer.call(@deleted_by).result
          email.deletedAt DateTime.now.utc
        end
        email.owner UserSerializer.call(@email.owner).result
        email.direction @email.direction
        email.campaignInfo @email.campaign_info if @email.campaign_info.present?

        if @additional_attributes
          email.linksClickedAt link_details @email
          email.attachments get_attachments @email
          email.openedAt @email.email_track_logs.order(created_at: :desc).pluck(:created_at)
        end

        if @send_email_ids_matching_global_message_id
          if(@email.direction == RECEIVED && !@email.global_message_id.blank?)
            email.emailsMatchingGlobalMessageId Email.where(global_message_id: @email.global_message_id, direction: RECEIVED).where.not(id: @email.id).pluck(:id)
          else
            email.emailsMatchingGlobalMessageId []
          end
        end
      end.target!
    )
  end

  def get_body email
    @send_body ? @email.body.to_s.dup.force_encoding("utf-8") : nil
  end

  def link_details email
    res = []
    email.link_mappings.each do |link_mapping|
      if link_mapping.email_link_logs.any?
        res << { id: link_mapping.id, url: CGI.unescape(link_mapping.url), clickedAt: link_mapping.email_link_logs.order(created_at: :desc).pluck(:created_at) }
      end
    end
    res
  end

  def get_attachments email
    email.attachments.map{|attachment| { id: attachment.id, fileName: attachment.extract_file_name(false), fileSize: attachment.file_size.to_i, inline: attachment.inline, cid: attachment.content_id } }
  end
end
