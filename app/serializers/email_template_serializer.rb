class EmailTemplateSerializer
  prepend SimpleCommand

  def initialize(email_template, body = nil, subject = nil)
    @email_template = email_template
    @body = body || @email_template.body
    @subject = subject || @email_template.subject
  end

  def call
    JSON(
      Jbuilder.new do |email_template|
        email_template.id @email_template.id
        email_template.body @body
        email_template.subject @subject
        email_template.name @email_template.name
        email_template.category @email_template.category
        email_template.status @email_template.active ? 'Active' : 'Inactive'
        email_template.createdBy UserSerializer.call(@email_template.created_by).result
        email_template.createdAt @email_template.created_at
        email_template.attachments get_attachments
        email_template.recordActions @email_template.get_record_actions
        email_template.updatedAt @email_template.updated_at
        email_template.updatedBy UserSerializer.call(@email_template.updated_by).result
      end
      .target!
    )
  end

  private
  def get_attachments
    @email_template.template_attachments.map{|attachment| { id: attachment.id, fileName: attachment.extract_file_name(false), fileSize: attachment.file_size.to_i} }
  end
end
