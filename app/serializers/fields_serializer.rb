# frozen_string_literal: true

class FieldsSerializer
  prepend SimpleCommand

  def initialize(fields)
    @fields = fields
  end

  def call
    return [] unless @fields

    json = Jbuilder.new
    json.array! @fields do |f|
      json.id f['id']
      json.name f['id']
      json.displayName f['header']
      json.type f['fieldType']
      json.standard f['isStandard']
      json.sortable f['isSortable']
      json.filterable f['isFilterable']
      json.internal f['isInternal']
      json.required f['isRequired']
      json.active f['active']
      json.lookup f['lookup']
      json.picklist f['picklist']
    end
  end
end
