class EmailTemplateSearchResultsSerializer
  prepend <PERSON><PERSON><PERSON>mand

  def initialize(email_templates)
    @email_templates = email_templates
  end

  def call
    return nil unless @email_templates
    json = Jbuilder.new
    json.body do
      json.content(@email_templates) do |email_template|
        json.id email_template.id
        json.name email_template.name
        json.category email_template.category
        json.status email_template.active ? 'Active' : 'Inactive'
        json.createdBy UserSerializer.call(email_template.created_by).result
        json.createdAt email_template.created_at
        json.updatedAt email_template.updated_at
        json.updatedBy UserSerializer.call(email_template.updated_by).result

        json.recordActions email_template.get_record_actions
      end

      json.page do
        json.no @email_templates.current_page.to_i
        json.size @email_templates.per_page
      end
      json.totalElements @email_templates.total_entries
      json.totalPages @email_templates.total_pages
      json.first @email_templates.previous_page.nil?
      json.last @email_templates.next_page.nil?
    end
  end

  def serialize_lookup
    return nil unless @email_templates
    json = Jbuilder.new

    json.body do
      json.content(@email_templates) do |email_template|
        json.id email_template.id
        json.name email_template.name
      end
    end
  end
end
