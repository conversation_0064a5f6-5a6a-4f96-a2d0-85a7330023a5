class LookUpSerializer
  prepend SimpleCommand

  def initialize(look_up, add_owner_id = false)
    @look_up = look_up
    @add_owner_id = add_owner_id
  end

  def call
    return nil unless @look_up

    JSON(
      Jbuilder.new do |look_up|
      look_up.id @look_up.entity_id
      look_up.entity @look_up.entity_type
      look_up.name (@look_up.name || @look_up.email)
      look_up.email @look_up.email if [LOOKUP_USER, LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_CUSTOM_EMAIL].include?(@look_up.entity_type)
      look_up.ownerId @look_up.owner_id if @add_owner_id
    end
    .target!
    )
  end
end
