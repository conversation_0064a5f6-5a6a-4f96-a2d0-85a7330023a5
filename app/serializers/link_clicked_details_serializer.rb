class LinkClickedDetailsSerializer
  prepend SimpleCommand

  def initialize(link, email)
    @link = link 
    @email = email
  end

  def call
    return nil unless @email
    JSON(
      Jbuilder.new do |email|
        email.(@email, :id)
        email.link CGI.unescape @link.url
        email.sender LookUpSerializer.call(@email.sender).result
        email.relatedTo @email.related_to.uniq.collect{|r| LookUpSerializer.call(r).result}
        email.subject @email.subject.present? ? @email.subject.to_s.force_encoding("utf-8") : 'No Subject'
        email.sentAt @email.created_at
        email.tenantId @email.tenant_id
        email.threadId @email.email_thread_id
        email.owner UserSerializer.call(@email.owner).result
      end.target!
    )
  end
end
