# frozen_string_literal: true

module RecordActionsProcessor

  def email_record_actions(email)
    {
      read: user_can_read?(email),
      reply: user_can_reply_to?(email),
      forward: user_can_forward?(email),
      delete: user_can_delete?(email)
    }
  end

  def thread_record_actions(email)
    {
      read: user_can_read?(email),
      delete: user_can_delete_thread?(email.email_thread)
    }
  end

  def user_can_read?(email)
    (
      @auth_data.can_read_all_emails? ||
      (
        @auth_data.can_read_emails? &&
        (
          @auth_data.email_owner?(email) ||
          @auth_data.conversation_owner?(email.email_thread) ||
          @auth_data.user_is_participant_in?(email) ||
          @auth_data.user_has_read_all_with_email_on_associated_entity?(email) ||
          @auth_data.related_entity_owner?(email) ||
          @auth_data.shared_entity_owner?(email)
        )
      )
    )
  end

  def user_can_reply_to?(email)
    (
      user_can_read?(email) &&
      @auth_data.user_is_participant_in?(email) &&
      @auth_data.includes_connected_account_email_address?(@connected_account, email) &&
      (email.connected_account_id == @connected_account&.id) &&
      (
        @auth_data.user_has_read_all_with_email_on_associated_entity?(email) ||
        @auth_data.related_entity_owner?(email) ||
        @auth_data.shared_entity_owner?(email)
      )
    )
  end

  def user_can_forward?(email)
    (
      user_can_read?(email) &&
      (
        @auth_data.user_is_participant_in?(email) ||
        @auth_data.user_has_read_all_with_email_on_associated_entity?(email) ||
        @auth_data.related_entity_owner?(email) ||
        @auth_data.shared_entity_owner?(email)
      )
    )
  end

  def user_can_delete?(email)
    (
      @auth_data.can_delete_all_emails? ||
      (
        @auth_data.can_delete_emails? &&
        (
          @auth_data.email_owner?(email) ||
          @auth_data.conversation_owner?(email.email_thread)
        )
      )
    )
  end

  def user_can_read_thread?(thread_id)
    email_thread_rule = {
      type: 'long',
      field: 'email_thread_id',
      operator: 'equal',
      value: thread_id
    }

    (
      @auth_data.can_read_all_emails? ||
      (
        @auth_data.can_read_emails? &&
        FilterEmailsQuery.call(@auth_data, { jsonRule: { condition: 'AND', rules: [email_thread_rule] }.with_indifferent_access }).result.any?
      )
    )
  end

  def user_can_delete_thread?(thread)
    (
      @auth_data.can_delete_all_emails? ||
      (
        @auth_data.can_delete_emails? &&
        @auth_data.conversation_owner?(thread)
      )
    )
  end
end
