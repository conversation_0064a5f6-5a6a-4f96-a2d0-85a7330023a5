class GetUserDetails
  prepend SimpleCommand

  def initialize user_id, tenant_id
    @user_id = user_id
    @tenant_id = tenant_id
  end

  def call
    user = User.
      find_or_initialize_by(
        id: @user_id,
        tenant_id: @tenant_id
      )
    if user.new_record?
      tenant = Tenant.find_or_create_by(id: @tenant_id)
      user_lookup = ValidateUsers.call([LookUp.new(entity: "#{LOOKUP_USER}_#{@user_id}", entity_id: @user_id, tenant_id: @tenant_id)]).result.first
      user.name = user_lookup.name
      user.tenant = tenant
    end
    if user.save
      user
    else
      raise(ExceptionHandler::AuthenticationError, ErrorCode.not_found("user: #{@user_id}"))
    end
  end
end

