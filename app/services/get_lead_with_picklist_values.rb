require 'rest-client'

class GetLeadWithPicklistValues < GetEntityWithPicklistValues

  def initialize(lead_id)
    super(lead_id, LOOKUP_LEAD)
  end

  private

  def process_variables(data)
    data.merge!(data.delete('customFieldValues')) if data['customFieldValues'].present?
    user_lookup_fields = set_user_field_values(data)

    data['requirementBudget'] = "#{data['requirementCurrency']} #{data['requirementBudget']}" if data['requirementBudget'].present?

    data = set_picklist_values(data)

    if data['metaData'].present?
      id_name_store = data['metaData']['idNameStore'].deep_dup || {}
      id_name_store.delete_if{|c,v| v.blank? }
      id_name_store.each do |field,values|
        data[field] =
          if values[data[field].to_s].present?
            values[data[field].to_s]
          elsif (values.is_a?(Hash) && data[field].is_a?(Array))
            data[field].map { |f| values[f.to_s] }.join(', ')
          else
            data[field]
          end
      end
    end

    ['companyPhones', 'phoneNumbers'].each do |phoneField|
      data[phoneField] = data[phoneField].to_a.map { |phones| "#{phones['dialCode']} #{phones["value"]}" }.join(', ')
    end

    data['emails'] = data['emails'].to_a.map { |email| email['value'] }.join(', ')
    data['products'] = data['products'].to_a.map { |email| email['name'] }.join(', ')
    data['pipelineStage'] = data.dig('pipeline', 'stage', 'name')
    data['pipeline'] = data.dig('pipeline', 'name')

    @entity_fields.select { |field| ['DATETIME_PICKER', 'DATE_PICKER'].include?(field['type']) }.each do |field|
      internal_name = field['internalName']
      if data[internal_name].present?
        data[internal_name] = field['type'] == 'DATETIME_PICKER' ? parse_date_time(data[internal_name]) : parse_date(data[internal_name])
      end
    end

    LEAD_EXCLUDED_FIELDS.each { |field| data.delete(field) }

    [data, user_lookup_fields]
  end

  def set_user_field_values(data)
    data.slice(*LEAD_USER_LOOKUP_FIELDS)
  end
end
