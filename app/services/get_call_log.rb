# frozen_string_literal: true

require 'rest-client'

class Get<PERSON>all<PERSON>og
  prepend <PERSON>Command

  def initialize(call_log_id)
    @call_log_id = call_log_id
  end

  def call
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    begin
      response = RestClient.get(
        "#{SERVICE_CALL}/v1/call-logs/#{@call_log_id}",
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?

    rescue RestClient::NotFound
      Rails.logger.error 'Get CallLog - 404'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error 'Get CallLog - 500'
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error 'Get CallLog - 400'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue StandardError => e
      Rails.logger.error "Get CallLog - invalid response - #{e}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
