class ListenForImapSentWebhook
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(EMAIL_EXCHANGE, IMAP_SENT_WEBHOOK_EVENT, IMAP_SENT_WEBHOOK_QUEUE) do |payload|
      payload = JSON(payload)
      payload = payload["params"]
      Rails.logger.info "Message Received: #{EMAIL_EXCHANGE} | #{IMAP_SENT_WEBHOOK_EVENT} | #{payload['account']} | #{payload['event']}"
      if payload["event"].eql? ('messageSent')
        ListImapSentBox.call(payload['account'])
      elsif payload['event'].eql?('authenticationError')
        SmtpAccountDisconnectPublisher.call(payload['account'], UNAUTHORIZATION_DISCONNECT)
      elsif ['messageDeliveryError'].include?(payload['event'])
        ProcessImapMessage.call(payload)
        SmtpAccountDisconnectPublisher.call(payload['account'], LIMIT_EXCEEDED_DISCONNECT)
      else
        ProcessImapMessage.call(payload)
      end
    end
  end
end
