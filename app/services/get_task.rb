# frozen_string_literal: true

require 'rest-client'

class GetTask
  prepend <PERSON>Command

  def initialize(task_id)
    @task_id = task_id
  end

  def call
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    begin
      response = RestClient.get(
        "#{SERVICE_PRODUCTIVITY}/v1/tasks/#{@task_id}",
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?

    rescue RestClient::NotFound
      Rails.logger.error 'Get Task - 404'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error 'Get Task - 500'
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error 'Get Task - 400'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue StandardError => e
      Rails.logger.error "Get Task - invalid response - #{e}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
