class ListenForSendWorkflowEmailEvent
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(WORKFLOW_EXCHANGE, WORKFLOW_SEND_EMAIL_EVENT, WORKFLOW_SEND_EMAIL_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload.inspect} for #{WORKFLOW_SEND_EMAIL_QUEUE}"
      payload = JSON(payload)
      user_id = payload["userId"]
      tenant_id = payload["tenantId"]
      sender_id = payload["senderId"]
      data = payload
      SendWorkflowEmail.call(user_id, tenant_id, sender_id, data)
    end
  end
end
