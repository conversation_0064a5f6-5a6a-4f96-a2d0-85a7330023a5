# frozen_string_literal: true

class DeleteEmail
  prepend SimpleCommand
  include RecordActionsProcessor

  def initialize(email_id)
    @email_id = email_id
  end

  def call
    command = GetSecurityContext.call

    unless command.success?
      Rails.logger.error "Unauthorised: Missing user context in DeleteEmail"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end

    @auth_data = command.result
    @connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, user_id: @auth_data.user_id, active: true)

    email = Email.find_by(id: @email_id, tenant_id: @auth_data.tenant_id)

    raise(ExceptionHandler::NotFound, ErrorCode.not_found) unless email.present?
    raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized) unless user_can_delete?(email)

    begin
      email_thread = email.email_thread
      files = email.attachments.pluck(:file_name)
      data_for_publisher = BuildDataForEmailUnrelatedPublisher.call([email]).result
      deleted_serialized_email = EmailDetailsSerializer.call(email, false, FetchUser.call(@auth_data.user_id, @auth_data.tenant_id).result, false, true, add_owner_id: true).result

      email.destroy!
      if email_thread.emails.count.zero?
        # Calls tenant usage publisher in after_destroy callback
        email_thread.destroy!
      else
        PublishUsageJob.perform_later(@auth_data.tenant_id)
      end

      PublishEvent.call(Event::EmailDeletedWorkflowV2.new(deleted_serialized_email))
      Rails.logger.info "Email Deleted Workflow V2 publisher called Email ID #{deleted_serialized_email['id']} Tenant ID #{deleted_serialized_email['tenantId']}"
      deleted_serialized_email.delete('emailsMatchingGlobalMessageId')
      EmailDeleteEventPublisher.call(deleted_serialized_email, DELETE_ACTION)
      EmailUnrelatedToContactEventPublisher.call(data_for_publisher) if data_for_publisher.present?
      DeleteFileFromS3.call(files, S3_EMAIL_BUCKET) if files.present?
    rescue StandardError => e
      Rails.logger.error "Error while deleting email. Message #{e.message}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
