require 'rest-client'

class UserSettingsService
  prepend SimpleCommand

  def fetch
    user_settings = fetch_user_settings
    {
      timezone: user_settings[:timezone],
      preffered_date_format: user_settings[:date_format],
      profile_id: user_settings[:profile_id]
    }
  end

  private

  def fetch_user_settings
    begin
      command = GetSecurityContext.call("token")
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
      token = command.result

      response = RestClient.get(
        SERVICE_IAM + "/v1/users/me",
        {
          :Authorization => "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )
      body = JSON(response.body)
      return { timezone: body['timezone'], date_format: body['dateFormat'], profile_id: body['profileId'] }
    end
  rescue RestClient::NotFound => e
    Rails.logger.error "Error while fetching User setting: 404 - #{e.message}"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  rescue RestClient::InternalServerError => e
    Rails.logger.error "Error while fetching User Setting: 500 - #{e.message}"
    raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
  rescue RestClient::BadRequest  => e
    Rails.logger.error "Error while fetching User setting: 400 - #{e.message}"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  rescue StandardError => e
    Rails.logger.error "Error while fetching User setting: 400 - #{e.message}"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  end
end
