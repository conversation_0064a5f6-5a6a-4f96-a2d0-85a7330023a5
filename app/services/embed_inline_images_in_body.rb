class EmbedInlineImagesInBody
  prepend SimpleCommand

  def initialize(emails)
    @emails = emails
    @img_tag_regex       = /<img\s[\w=\\":;\s\-\.\(\)\[\]%|\/]*>/i
    @attachment_id_regex = /cid:[\w\.@-]*/i
  end

  def call
    return [] if @emails.blank?

    begin
      @emails.each do |email|
        body = email.body
        matched_tags = body&.scan(@img_tag_regex).to_a # Match image tag

        matched_tags.each do |img_tag|
          body = email.body

          matched_attachment_id = img_tag.match(@attachment_id_regex).to_a[0]
          next if matched_attachment_id.blank?
          attachment_id = matched_attachment_id.split(':')[1]
          next if attachment_id.blank?

          inline_attachment = email.attachments.where("inline = true AND content_id = ?", attachment_id).first
          next if inline_attachment.blank?

          url_details = GetPresignedUrlFromS3.call(inline_attachment, S3_EMAIL_BUCKET).result
          email.body = body.gsub(img_tag, get_formatted_img_tag(url_details[:url], inline_attachment.extract_file_name(false), img_tag))
        end
      end

      @emails
    rescue StandardError => e
      Rails.logger.error "Email Service | Failed to embed inline image to email body: #{e.message} | Email: #{@emails}"
    end
  end

  private

  def get_formatted_img_tag(url, file_name, img_tag)
    img_tag.gsub(/src="[^"]*"/, "src=\"#{url}\"")
  end
end
