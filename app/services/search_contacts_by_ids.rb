# frozen_string_literal: true

require 'rest-client'

class SearchContactsByIds
  prepend SimpleCommand

  def initialize(contact_id_email_map, tenant_id)
    @contact_ids = contact_id_email_map.keys
    @contact_id_email_map = contact_id_email_map
    @tenant_id = tenant_id
  end

  def call
    return [] if @contact_ids.blank?
    response = search_contacts
    if response['content'].present?
      response['content'] = response['content'].select { |contact| contact.dig('recordActions', 'email') }
    end

    response['content'].to_a.map do |contact_data|
      {
        entity: "#{LOOKUP_CONTACT}_#{contact_data['id']}",
        name: "#{contact_data['firstName']} #{contact_data['lastName']}".strip,
        owner_id: contact_data['ownerId'],
        email: @contact_id_email_map[contact_data['id']],
        tenant_id: @tenant_id,
        associated_deals: contact_data['associatedDeals']
      }
    end
  end

  private

  def search_contacts
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result

    payload = { fields: %w[id firstName lastName emails associatedDeals ownerId] }
    rules = [{
      field: 'id',
      id: 'id',
      operator: 'in',
      relatedFieldIds: nil,
      type: 'double',
      value: @contact_ids.join(',')
    }]
    payload[:jsonRule] = { rules: rules, condition: 'OR', valid: true }

    begin
      response = RestClient.post(
        "#{SERVICE_SEARCH}/v1/search/contact?sort=updatedAt,desc&page=0&size=1000",
        payload.to_json,
        {
          Authorization: "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error 'SearchContactsById invalid response'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error 'SearchContactsById 404'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error 'SearchContactsById 500'
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error 'SearchContactsById 400'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
