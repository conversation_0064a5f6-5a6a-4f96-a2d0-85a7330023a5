class SaveAttachmentsFromGmail
  prepend SimpleCommand

  def initialize(params)
    @email       = params[:email]
    @attachments = params[:attachments]
    @gmail       = params[:gmail]
  end

  def call
    return if @attachments.blank? || @email.blank? || @gmail.blank?

    begin
      attachments_to_create = []
      @attachments.each do |attachment|
        attachment_body = @gmail.get_user_message_attachment('me', @email.source_id, attachment[:id]).data
        attachment_file = File.new("#{Rails.root}/tmp/#{DateTime.now.to_i}_#{@email.id}_#{attachment[:name]}", 'wb')
        attachment_file.write(attachment_body)
        attachments_to_create <<  HashWithIndifferentAccess.new({
                                    data: attachment_file,
                                    fileName: attachment[:name],
                                    type: attachment[:type],
                                    content_id: attachment[:content_id]
                                  })
      end

      CreateAttachment.call(@email, attachments_to_create, nil, true)
    rescue StandardError => e
      Rails.logger.error "Email Service | Failed to create received attachment: #{e.message} | Email: #{@email}"
    end
  end
end
