class ListenForLeadNameUpdate
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize
  end

  def call
    subscribe_lead_name_updated_event
  end

  private
    def subscribe_lead_name_updated_event
      RabbitmqConnection.subscribe(LEAD_EXCHANGE, LEAD_NAME_UPDATED_EVENT, LEAD_NAME_UPDATED_QUEUE) do |payload|
        Rails.logger.info "Received message #{payload} for #{LEAD_NAME_UPDATED_EVENT}"
        payload = JSON(payload)
        id = payload["leadId"]
        tenant_id = payload["tenantId"]
        name = "#{payload['firstName']} #{payload['lastName']}".strip

        lookup_params = {}
        lookup_params[:name] = name
        look_ups = LookUp.where(entity: "#{LOOKUP_LEAD}_#{id}", tenant_id:tenant_id)

        if look_ups.update_all(name: name) > 0
          PublishEvent.call(
            Event::LookUpUpdated.new({
              entity_id: id,
              entity_type: LOOKUP_LEAD,
              name: name,
              owner_id: look_ups.first.owner_id,
              tenant_id: tenant_id
            })
          )
        end
      end
    end
end
