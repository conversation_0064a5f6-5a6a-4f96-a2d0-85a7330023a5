# frozen_string_literal: true

class GetTaskWithPicklistValues < GetEntityWithPicklistValues
  def initialize(task_id)
    super(task_id, LOOKUP_TASK)
  end

  private

  def process_variables(data)
    data.merge!(data.delete('customFieldValues')) if data['customFieldValues'].present?
    user_lookup_fields = set_user_field_values(data)

    @entity_fields.select { |field| %w[LOOK_UP PICK_LIST].include?(field['type']) && field['entity'] == LOOKUP_TASK }.each do |field|
      if data[field['internalName']].present?
        data[field['internalName']] = data.dig('metaData', 'idNameStore', field['internalName'], data[field['internalName']].to_s)
      end
    end

    @entity_fields.select { |field| %w[DATETIME_PICKER DATE_PICKER].include?(field['type']) && field['entity'] == LOOKUP_TASK }.each do |field|
      internal_name = field['internalName']
      if data[internal_name].present?
        data[internal_name] = field['type'] == 'DATETIME_PICKER' ? parse_date_time(data[internal_name]) : parse_date(data[internal_name])
      end
    end

    if data['relation'].present?
      entity_names = []
      data['relation'].each do |entity|
        entity_details = nil

        if entity['targetEntityType'] == 'COMPANY'
          command = GetCompanyIdName.call(entity['targetEntityId'])
          next unless command.success?
          entity_details = command.result&.first
        else
          command = "Get#{entity['targetEntityType'].titleize}".constantize.call(entity['targetEntityId'])
          next unless command.success?
          entity_details = command.result
        end
        
        next unless entity_details
        
        entity_names << "#{entity_details['firstName']} #{entity_details['lastName']}#{entity_details['name']}".strip
      end
      data['relation'] = entity_names.join(', ')
    end
    data = data.except(*TASK_EXCLUDED_FIELDS)

    [data, user_lookup_fields]
  end

  def set_user_field_values(data)
    data.slice(*TASK_USER_LOOKUP_FIELDS)
  end
end
