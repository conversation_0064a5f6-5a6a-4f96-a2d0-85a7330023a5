# frozen_string_literal: true

class GetEmailFields
  prepend SimpleCommand

  def call
    command = GetSecurityContext.call

    unless command.success?
      Rails.logger.error 'Unauthorised: User context missing in GetEmailFields'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end

    list_layout = YAML.load_file("#{Rails.root}/app/files/email-list-layout.json")

    fields = list_layout.dig('pageConfig', 'tableConfig', 'columns')

    FieldsSerializer.call(fields).result
  end
end
