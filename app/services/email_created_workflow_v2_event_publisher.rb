# frozen_string_literal: true

class EmailCreatedWorkflowV2EventPublisher
  prepend SimpleCommand

  def initialize(email, metadata)
    @email = email
    @metadata = metadata
  end

  def call
    begin
      GlobalMessage.create!(message_id: @email.global_message_id) unless @email.global_message_id.blank?
      PublishEvent.call(Event::EmailCreatedWorkflowV2.new(EmailDetailsSerializer.call(@email, false, nil, true, false, add_owner_id: true).result, @metadata))
      Rails.logger.info "Email Created Workflow V2 event publisher called tenantId: #{@email.tenant_id} ownerId: #{@email.owner_id} email: #{@email.id}"
    rescue ActiveRecord::RecordInvalid, ActiveRecord::RecordNotUnique, PG::UniqueViolation => e
      Rails.logger.info "Skipped email created workflow v2 event for tenantId: #{@email.tenant_id} ownerId: #{@email.owner_id} email: #{@email.id}"
    end
  end
end
