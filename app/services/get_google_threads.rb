# TODO: Need to remove this class if we are not using this anywhere. Need to confirm once.
require 'google/apis/gmail_v1'
class GetGoogleThreads
  prepend SimpleCommand

  def initialize(thread_ids, connected_account)
    @thread_ids = thread_ids
    @connected_account = connected_account
  end

  # TODO: Implement adapter design pattern when we add new provider

  def call
    gmail = Google::Apis::GmailV1::GmailService.new
    command = GetConnectedAccountAccessToken.call(@connected_account)
    if command.success?
      begin
        gmail.authorization = Signet::OAuth2::Client.new(access_token: command.result)
        @thread_ids.each do |thread_id|
          begin
            sync_thread(thread_id, gmail)
          rescue Exception => e
            Rails.logger.info "SYNC EXCEPTION: #{e.to_s}"
          end
        end
      rescue Google::Apis::ServerError, Google::Apis::ClientError => e
        Rails.logger.error "Exception while getting email for #{@connected_account.id} - #{e.to_s}"
        #raise ExceptionHandler::ThirdPartyAPIError
      rescue Google::Apis::AuthorizationError => e
        Rails.logger.error "Exception while getting email for #{@connected_account.id} - #{e.to_s}"
        #raise(ExceptionHandler::ThirdPartyAPIAuthError, ErrorCode.unauthorized)
      rescue Exception => e
        Rails.logger.error "Exception while retrieving emails #{e.to_s}"
      end
    end
  end

  private

  def sync_thread(thread_id, gmail)

    res = gmail.get_user_thread('me', thread_id)
    email_thread = EmailThread.find_by(source_thread_id: thread_id)
    res.messages.each do |message|
      next if Email.where(source_id: message.id).exists?
      payload = message.payload
      email = Email.new
      headers = payload.headers
      email.from = headers.select{|a| a.name.eql? 'From' }.as_json.last['value']
      #email.cc = [headers.select{|a| a.name.eql? 'Cc' }&.as_json&.last['value']]
      email.to = [headers.select{|a| a.name.eql? 'To' }&.as_json&.last['value']]
      email.subject = headers.select{|a| a.name.eql? 'Subject' }.as_json.last['value']
      email.source_id = message.id
      email.email_thread_id = email_thread.id
      email.owner = @connected_account.user
      email.body = payload.parts.first.body.data
      email.connected_account = @connected_account
      email.sender = @connected_account.user
      email.tenant_id = @connected_account.user.tenant_id
      email.save
      email_thread.emails.first.related_to.each { |r| email.related_to << r }
      EmailCreateEventPublisher.call(email.id, CREATE_ACTION)
      # email.related_to << email_thread.emails.first.related_to.first
    end
  end
end
