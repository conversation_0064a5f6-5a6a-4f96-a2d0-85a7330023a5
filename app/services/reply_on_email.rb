# frozen_string_literal: true

class ReplyOnEmail
  prepend SimpleCommand
  include RecordActionsProcessor

  def initialize(params)
    @params = params.to_hash.with_indifferent_access
  end

  def call
    command = GetSecurityContext.call

    unless command.success?
      Rails.logger.error 'Unauthorised: Missing user context in ReplyOnEmail'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end

    @auth_data = command.result
    @connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, user_id: @auth_data.user_id, active: true)

    reply_to_email = Email.find_by(tenant_id: @auth_data.tenant_id, email_thread_id: @params[:id], id: @params[:replyTo])

    if reply_to_email.nil?
      Rails.logger.error 'Email Not found while replying'
      raise(ExceptionHandler::NotFound, ErrorCode.not_found("email: #{@email_id}"))
    end

    unless user_can_reply_to?(reply_to_email)
      Rails.logger.error "Unauthorised: User cannot reply to email. User id #{@auth_data.user_id} Email id #{@params[:replyTo]}"
      raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
    end

    @params[:thread_id] = @params.delete(:id)
    CreateEmail.call(@params).result
  end
end
