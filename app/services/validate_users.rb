require 'rest-client'
class ValidateUsers
  prepend SimpleCommand

  def initialize(users = [])
    @users = users
  end

  def call
    return [] if @users.nil? || @users.empty?
    command = GetSecurityContext.call
    if command.success?
      auth_data = command.result
      verified_users = get_user_summary
      verified_users.each do |v_user|
        user = @users.select{|x| x.entity_id == v_user["id"]}.first
        if user.nil?
          Rails.logger.error "ValidateUser: couldnt find matching users in response from IAM"
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        else
          user.name = v_user["name"]
        end
      end
      return @users
    else
      raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
    end
  end
  private

  def get_user_summary
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    user_ids_params = @users.collect{|user| user.entity_id }.uniq.join(",")
    begin
      response = RestClient.get(SERVICE_IAM + "/v1/users/summary?id=" + user_ids_params,{'Authorization': "Bearer #{token}"}
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "ValidateUser.get_user_summary invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::Unauthorized
      Rails.logger.error "ValidateUser.get_user_summary iam 401"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    rescue RestClient::NotFound
      Rails.logger.error "ValidateUser.get_user_summary iam 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "ValidateUser.get_user_summary iam 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "ValidateUser.get_user_summary iam 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end

