# frozen_string_literal: true

class EmailOpenedEventPublisher
  prepend SimpleCommand

  def initialize(email)
    @email = email
    @action = OPEN_ACTION
  end

  def call
    Rails.logger.info "Email opened event publisher called | #{event_payload}"
    event = Event::EmailAction.new(event_payload, @action)
    PublishEvent.call(event)
  end

  private

  def event_payload
    EmailDetailsSerializer.call(@email, false, add_owner_id: true).result
  end
end

