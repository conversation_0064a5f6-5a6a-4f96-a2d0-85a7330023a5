class DownloadAttachmentFromS3
  attr_accessor :attachment
  prepend <PERSON>Command
  def initialize attachment
    @attachment = attachment
    Aws.config.update(
      endpoint: ENV['AWS_ENDPOINT'],
      region: ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY_ID'], ENV['AWS_SECRET_ACCESS_KEY'])
    )
    s3 = Aws::S3::Resource.new
    bucket_name = attachment.is_a?(TemplateAttachment) ? S3_EMAIL_TEMPLATE_BUCKET : S3_EMAIL_BUCKET
    @bucket = s3.bucket(bucket_name)
  end

  def call
    begin
      obj = @bucket.object(attachment.file_name)
      obj.download_file(attachment.extract_file_name)
      File.new(attachment.extract_file_name)
    rescue StandardError => e
      Rails.logger.error "Error while downloading attachment from s3 for attachment_id: #{attachment.id} , Error: #{e.message}"
    end
  end
end
