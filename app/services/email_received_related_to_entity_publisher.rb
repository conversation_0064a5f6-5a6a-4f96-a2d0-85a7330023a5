class EmailReceivedRelatedToEntityPublisher
  prepend SimpleCommand
  attr_reader :email

  def initialize(email, user_context = true)
    @email = email
    @user_context = user_context
  end

  def call
    data = EmailDetailsSerializer.call(email, @user_context, add_owner_id: true).result
    event = Event::EmailReceivedRelatedToEntity.new(data.except('body'))
    PublishEvent.call(event)
    Rails.logger.info "Event::EmailReceivedRelatedToEntity data #{event.to_json}"
  end
end
