require 'google/apis/gmail_v1'
class GmailMailSender
  attr_accessor :source_thread_id, :reply_to_email_id, :tenant_id
  prepend SimpleCommand
  include ParamsProcessor

  def initialize(params, token)
    @email_record = params[:email]
    @tenant_id = @email_record.tenant_id
    @thread_id = params[:thread_id]
    @attachments = params[:attachments] || []
    @connected_account = @email_record.connected_account
    @reply_to_email_id = params[:reply_to_email_id]
    @token = token
  end

  def call
    gmail = Google::Apis::GmailV1::GmailService.new
    if @thread_id.present?
      email_thread = EmailThread.where(tenant_id: @tenant_id, id: @thread_id).select('source_thread_id').first
      raise(ActiveRecord::RecordNotFound, ErrorCode.not_found("email-thread: #{@thread_id}")) if email_thread.nil?
      @source_thread_id = email_thread.source_thread_id
    end

    begin
      gmail.authorization = Signet::OAuth2::Client.new(access_token: @token)
      #settings =  gmail.get_user_setting_send_as('me', @connected_account.email)
      #display_name = settings&.display_name 
      message = build_message gmail#, display_name
      res = gmail.send_user_message('me', { thread_id: @source_thread_id }, upload_source: StringIO.new(message.to_s), content_type: 'message/rfc822')
      global_message_id = gmail.get_user_message('me', res.id, format: 'METADATA', metadata_headers: ['Message-ID']).payload.headers.select{|a| a.name.upcase.eql? 'MESSAGE-ID' }.as_json.last['value']
      return OpenStruct.new({ id: res.id, thread_id: res.thread_id, global_message_id: global_message_id })
    rescue Google::Apis::ServerError, Google::Apis::ClientError => e
      Rails.logger.error "GmailMailSender Exception while sending mail for #{@email_record.id} - #{e.to_s}"
      raise ExceptionHandler::ThirdPartyAPIError
    rescue Google::Apis::AuthorizationError => e
      Rails.logger.error "GmailMailSender Exception while sending mail for #{@email_record.id} - #{e.to_s}"
      raise(ExceptionHandler::ThirdPartyAPIAuthError, ErrorCode.unauthorized)
    rescue Google::Apis::RateLimitError => e
      Rails.logger.error "GmailMailSender Exception while sending email for #{@email_record.id} - #{e.to_s}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.rate_limit_error)
    end
  end

  private

  def build_message gmail#, display_name
    message = RMail::Message.new
    message.header['To'] = @email_record.to.join(',')
    message.header['Cc'] = @email_record.cc.join(',')
    message.header['Bcc'] = @email_record.bcc.join(',')
    encoded_subject = Base64.strict_encode64(@email_record.subject.to_s.encode('UTF-8'))
    subject  =  "=?UTF-8?B?#{encoded_subject}?="
    message.header['Subject'] = subject
    display_name = @email_record.connected_account.display_name || @email_record.connected_account.user.name
    message.header['From'] = "#{display_name} <#{@email_record.from}>" if display_name
    message.header['Content-Type'] = 'multipart/related' if @attachments.any? { |attachment| attachment['type'] == 'inline' }

    if @reply_to_email_id.present?
      message_content = gmail.get_user_message('me', @reply_to_email_id)
      message_id = message_content.payload.headers.find{|h| h.name.downcase == 'message-id'}.value rescue nil
      message.header['References'] = message.header['In-Reply-To'] = message_id if message_id.present?
    end
    build_body message
  end

  def build_body message
    m1 = RMail::Message.new
    m1.body = @email_record.body
    m1.header['Content-Type'] = 'text/html; charset="UTF-8"'
    messages = [m1]

    @attachments.each do |a|
      m = RMail::Message.new
      content_type = a['data'].respond_to?(:content_type) ? a['data'].content_type : MIME::Types.type_for(a['fileName'])[0].content_type rescue 'multipart/form-data'
      m.header['Content-Type'] = "#{content_type}; name=\"#{a['fileName']}\""
      m.header["Content-Disposition"] = "#{a['type'] || 'attachment'}; filename=\"#{get_file_name(a)}\""
      m.header["Content-Transfer-Encoding"]= "base64"
      m.header["Content-ID"] = "<#{a['content_id']}>" unless a['content_id'].blank?
      m.header["X-Attachment-Id"] = "#{a['content_id']}" unless a['content_id'].blank?

      m.body = Base64.encode64("#{File.read(a['data'].path)}")
      messages << m
    end
    message.body = messages
    message
  end
end
