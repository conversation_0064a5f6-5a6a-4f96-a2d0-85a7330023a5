class FetchDisplayName
  prepend SimpleCommand
  include GraphApiClient

  attr_accessor :user_id, :tenant_id, :name, :data

  def initialize(connected_account)
    @connected_account = connected_account
  end

  def call
    add_token_in_current_thread
    name = fetch_display_name
    @connected_account.update(display_name: name) unless name.blank?
  end

  private

  def fetch_display_name
    case @connected_account.provider_name
    when GOOGLE_PROVIDER
      fetch_gmail_display_name
    when MICROSOFT_PROVIDER
      fetch_ms_display_name
    end
  end

  def add_token_in_current_thread
    thread = Thread.current
    thread[:token] = GenerateToken.call(@connected_account.user.id, @connected_account.user.tenant_id).result
  end

  def fetch_gmail_display_name
    gmail = Google::Apis::GmailV1::GmailService.new
    command = GetConnectedAccountAccessToken.call(@connected_account, skip_auth: true)
    if command.success?
      begin
        gmail.authorization = Signet::OAuth2::Client.new(access_token: command.result)
        settings =  gmail.get_user_setting_send_as('me', @connected_account.email)
        return settings&.display_name 
      rescue Google::Apis::ServerError, Google::Apis::ClientError => e
        Rails.logger.error "Exception while fetching display name #{@connected_account.email}: #{e.to_s}"
        return nil
      rescue Google::Apis::AuthorizationError => e
        Rails.logger.error "Exception while fetching display name #{@connected_account.email} #{e.to_s}"
        return nil
      end
    end
  end

  def fetch_ms_display_name
    url = "#{GRAPH_HOST}/v1.0/me"
    command = GetConnectedAccountAccessToken.call(@connected_account, skip_auth: true)
    if command.success?
      headers = {
        "Content-type" => "application/json",
        "Authorization" => "Bearer #{command.result}"
      }
      begin
        response = RestClient.get url, headers
        if(response && response.code.eql?(200))
          JSON(response.body)['displayName']
        else
          return nil
        end
      rescue Exception => e
        Rails.logger.error "Exception while fetching display name #{@connected_account.email} | #{e.to_s}"
        return nil
      end
    end
  end
end
