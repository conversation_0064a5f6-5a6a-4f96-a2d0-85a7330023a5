class ListenForUserUpdate
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize
  end

  def call
    subscribe_user_name_updated_event
  end

  private
    def subscribe_user_name_updated_event
      RabbitmqConnection.subscribe(USER_EXCHANGE, USER_NAME_UPDATED_EVENT, USER_NAME_UPDATED_QUEUE) do |payload|
        Rails.logger.debug "Received message #{payload} for #{USER_NAME_UPDATED_EVENT}"
        payload = JSON(payload)
        id = payload["userId"]
        tenant_id = payload["tenantId"]
        name = "#{payload['firstName']} #{payload['lastName']}"

        lookup_params = {}
        lookup_params[:name] = name
        User.where(id: id, tenant_id:tenant_id).update_all(name: name)
      end
    end
end
