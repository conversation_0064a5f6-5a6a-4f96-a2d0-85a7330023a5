class GetPresignedUrlFromS3
  prepend SimpleCommand
  attr_accessor :params, :attachment

  def initialize(attachment, bucket)
    @attachment = attachment
    Aws.config.update(
      endpoint: ENV['AWS_ENDPOINT'],
      region: ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY_ID'], ENV['AWS_SECRET_ACCESS_KEY'])
    )
    s3 = Aws::S3::Resource.new
    @bucket = s3.bucket(bucket)
  end

  def call
    return unless @attachment
    begin
      obj = @bucket.object(@attachment.file_name)
      extracted_file_name = @attachment.extract_file_name(false)
      url = obj.presigned_url(:get, expires_in: 300, response_content_disposition: "attachment; filename=#{extracted_file_name}")
      { url: url,  file_name: extracted_file_name }
    rescue StandardError => e
      Rails.logger.error "Email Service | Failed to get signed URL of file from s3: #{e.message} | #{@file_path}"
    end
  end
end
