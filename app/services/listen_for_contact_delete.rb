class ListenForContactDelete
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(CONTACT_EXCHANGE, CONTACT_DELETED_EVENT, CONTACT_DELETED_QUEUE) do |payload|
      Rails.logger.info "Received message for #{CONTACT_DELETED_EVENT}"
      payload = JSON(payload)
      UpdateEmailForEntityDeleteEventJob.perform_later({ id: payload["id"], tenant_id: payload["tenantId"], user_id: payload["userId"], entity_type: LOOKUP_CONTACT, publish_usage: payload['publishUsage'] })
    end
  end
end
