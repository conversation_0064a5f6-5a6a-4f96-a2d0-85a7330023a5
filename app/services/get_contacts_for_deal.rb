require 'rest-client'

class GetContactsForDeal
  attr_accessor :deal_id
  prepend SimpleCommand

  def initialize deal_id
    @deal_id = deal_id
  end

  def call
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    Rails.logger.info "Fetching contacts associated with deal-id: #{ deal_id }"
    begin
      response = RestClient.post(
        SERVICE_SEARCH + '/v1/search/contact?page=0&size=30',
        {
          "fields": ['id'],
          "jsonRule": {
            "condition": 'AND',
            "rules": [
              {
                "operator": 'equal',
                "id": 'associatedDeals',
                "field": 'associatedDeals',
                "type": 'long',
                "value": deal_id
              }
            ],
            "valid": true
          }
        }.to_json,
        {
          :Authorization => "Bearer #{ token }",
          :content_type => 'application/json'
        }
      )

      deal_contacts = JSON(response.body)
      return [] unless deal_contacts
      return deal_contacts['content']
    rescue RestClient::NotFound
      Rails.logger.error "Get associated contacts on Deal id: #{@deal_id} - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "Get associated contacts on Deal id: #{@deal_id} - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "Get associated contacts on Deal id: #{@deal_id} - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
