class ListenForDeactivateCustomConnectedAccount
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(USER_EXCHANGE, DEACTIVATE_CUSTOM_CONNECTED_ACCOUNT_EVENT, DEACTIVATE_CUSTOM_CONNECTED_ACCOUNT_QUEUE) do |payload|
      Rails.logger.debug "Received message #{payload} for #{DEACTIVATE_CUSTOM_CONNECTED_ACCOUNT_EVENT}"
      payload = JSON(payload)
      tenant_id = payload['tenantId']
      user_id = payload['userId']
      provider = CUSTOM_PROVIDER
      disconnected_by = payload['disconnectedBy']
      user_email = payload['userEmail']
      tenant_email = payload['tenantEmail']
      DeactivateConnectedAccount.call(tenant_id, user_id, provider, disconnected_by, EMAIL_CLIENT, user_email, tenant_email)
    end
  end
end
