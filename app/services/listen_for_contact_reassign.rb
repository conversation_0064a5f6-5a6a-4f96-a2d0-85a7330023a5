# frozen_string_literal: true

class ListenForContactReassign
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(CONTACT_EXCHANGE, CONTACT_OWNER_UPDATED_EVENT, CONTACT_OWNER_UPDATED_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{CONTACT_OWNER_UPDATED_EVENT}"
      payload = JSON(payload)
      data = {}
      data[:entity_id] = payload['entityId']
      data[:new_owner_id] = payload['newOwnerId']
      data[:tenant_id] = payload['tenantId']

      UpdateOwnerForEntity.call(LOOKUP_CONTACT, data).result
    end
  end
end
