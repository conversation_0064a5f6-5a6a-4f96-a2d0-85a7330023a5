require 'rest-client'

class GetContact
  prepend SimpleCommand

  def initialize(contact_id)
    @contact_id = contact_id
  end

  def call
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result

    begin
      response = RestClient.get(
        SERVICE_SALES + "/v1/contacts/#{@contact_id}",
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "Get Contact - invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "Get Contact - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "Get Contact - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "Get Contact - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end