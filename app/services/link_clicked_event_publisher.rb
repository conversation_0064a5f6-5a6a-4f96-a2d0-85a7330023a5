# frozen_string_literal: true

class LinkClickedEventPublisher
  prepend <PERSON>Command

  def initialize(link, email)
    @email = email
    @link = link
    @action = LINK_CLICKED_ACTION
  end

  def call
    Rails.logger.info "Email link event publisher called | #{@link.id} | #{event_payload}"
    event = Event::LinkClickedAction.new(event_payload)
    PublishEvent.call(event)
  end

  private

  def event_payload
    LinkClickedDetailsSerializer.call(@link, @email).result
  end
end

