require 'rest-client'

class SmtpAccountDisconnectPublisher
  prepend SimpleCommand
  def initialize(account, reason)
    @connected_account = load_connected_account(account)
    @reason = reason
  end

  def call
    return if @connected_account.blank?
    event = Event::SmtpAccountDisconnect.new(
      @connected_account.user_id,
      @connected_account.tenant_id,
      @connected_account.email,
      @reason
    )
    PublishEvent.call(event)
    Rails.logger.info "Event:SmtpAccountDisconnect: #{event.to_json}"
  end

  private

  def load_connected_account account
    @connected_account = ConnectedAccount.where(active: true, access_token: account).first
  end
end
