# frozen_string_literal: true

require 'rest-client'

class ConnectedAccountDeactivatedPublisher
  prepend SimpleCommand

  attr_accessor :params

  def initialize(tenant_id, user_id, disconnected_by, account_type, user_email, tenant_email, connected_email = nil)
    @params = {
      tenant_id: tenant_id,
      user_id: user_id,
      disconnected_by: disconnected_by,
      account_type: account_type,
      user_email: user_email,
      tenant_email: tenant_email,
      connected_email: connected_email
    }.with_indifferent_access
  end

  def call
    event = Event::ConnectedAccountDeactivated.new(params)
    PublishEvent.call(event)
    Rails.logger.info "Event::ConnectedAccountDeactivated data #{event.to_json}"
  end
end
