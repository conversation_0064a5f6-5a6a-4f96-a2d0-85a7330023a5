# frozen_string_literal: true

class GetCallLogWithPicklistValues < GetEntityWithPicklistValues
  def initialize(call_log_id)
    super(call_log_id, LOOKUP_CALL)
  end

  private

  def process_variables(data)
    data.merge!(data.delete('customFieldValues')) if data['customFieldValues'].present?
    user_lookup_fields = set_user_field_values(data)

    data = data.except(*CALL_LOG_EXCLUDED_FIELDS)

    @entity_fields.select { |field| %w[LOOK_UP PICK_LIST].include?(field['type']) && %w[associatedLeads associatedDeals associatedContacts].exclude?(field['internalName']) }.each do |field|
      if data[field['internalName']].present?
        data[field['internalName']] = data[field['internalName']].is_a?(Hash) ? data[field['internalName']]['name'] : data[field['internalName']].map { |f| f['name'] }.join(', ')
      end
    end

    @entity_fields.select { |field| %w[DATETIME_PICKER DATE_PICKER].include?(field['type']) }.each do |field|
      internal_name = field['internalName']
      if data[internal_name].present?
        data[internal_name] = field['type'] == 'DATETIME_PICKER' ? parse_date_time(data[internal_name]) : parse_date(data[internal_name])
      end
    end

    data['associatedLeads'] = data['relatedTo'].select { |entity| entity['entity'] == 'lead' }.map { |lead| lead['name'] }.join(', ')
    data['associatedContacts'] = data['relatedTo'].select { |entity| entity['entity'] == 'contact' }.map { |contact| contact['name'] }.join(', ')
    data['associatedDeals'] = data['relatedTo'].select { |entity| entity['entity'] == 'deal' }.map { |deal| deal['name'] }.join(', ')

    [data, user_lookup_fields]
  end

  def set_user_field_values(data)
    data.slice(*CALL_LOG_USER_LOOKUP_FIELDS).inject({}) { |hash, (key, val)| hash.merge(key => val.try(:[], 'id')) }
  end
end
