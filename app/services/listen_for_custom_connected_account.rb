class ListenForCustomConnectedAccount
  prepend SimpleCommand
  include ActiveModel::Validations

  attr_accessor :user_id, :tenant_id, :name, :data
  def call
    RabbitmqConnection.subscribe(USER_EXCHANGE, CUSTOM_CONNECTED_ACCOUNT_SYNC_EVENT, CUSTOM_CONNECTED_ACCOUNT_SYNC_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{CUSTOM_CONNECTED_ACCOUNT_SYNC_EVENT}"
      payload = JSON(payload)
      @user_id = payload["userId"]
      @tenant_id = payload["tenantId"]
      @name = payload["firstName"].to_s + payload["lastName"].to_s
      @data = payload
      @data["provider"] = CUSTOM_PROVIDER
      @data['accessToken'] = payload['accountId']
      @data['expiresAt'] = (DateTime.now + 100.years).to_s
      CreateConnectedAccount.call(@user_id, @tenant_id, @name, @data)
    end
  end
end
