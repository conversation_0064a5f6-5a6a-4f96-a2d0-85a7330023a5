require 'rest-client'

class EmailRelatedToContactEventPublisher
  prepend SimpleCommand
  def initialize(params, auth_data, email)
    @params = params
    @auth_data = auth_data
    @email = email
    @user = email.sender
  end

  def call
    deal_params = @params[:relatedTo]
    associated_contacts = deal_params && deal_params['entity'] == LOOKUP_DEAL ? GetContactsForDeal.call(deal_params[:id]).result : []
    contacts = filter_associated_contacts(associated_contacts)
    recipient_users = @email.related_to.where(entity_type: LOOKUP_USER).select('entity_id').map(&:entity_id)
    recipient_users << @user.entity_id if has_read_access? && has_email_access?
    if contacts.present?
      contacts.each do |contact|
        owner_user = { id: contact[:owner_id], tenant_id: @auth_data.tenant_id }
        related_to_id = contact['id']
        recipient_users.reject{|recipient| recipient == contact[:owner_id]}.each do |user_id|
          event = Event::EmailRelatedToContact.new(owner_user, user_id, related_to_id, @email.id)
          PublishEvent.call(event)
          Rails.logger.info "Event::EmailRelatedToContact event: #{event.to_json}"
        end
      end
    end
  end

  private

  def filter_associated_contacts(associated_contacts)
    return [] unless associated_contacts.present?
    check_if_associated_contact = -> (elems, c) { elems.find { |el| el[:id] == c['id'].to_s && el[:entity] == LOOKUP_CONTACT } }

    contacts = []
    associated_contacts.each do |c|
      contact = check_if_associated_contact.call(@params[:to], c)
      contact = check_if_associated_contact.call(@params[:cc], c) unless contact
      contact = check_if_associated_contact.call(@params[:bcc], c) unless contact
      contacts << contact.merge(owner_id: c['ownerId']) if contact
    end
    contacts
  end

  def has_read_access?
    contact_permission = @auth_data.permissions.find{ |p| p.name == 'contact' }
    contact_permission&.action&.read || contact_permission&.action&.read_all
  end

  def has_email_access?
    contact_permission = @auth_data.permissions.find{ |p| p.name == 'contact' }
    contact_permission&.action&.email
  end
end
