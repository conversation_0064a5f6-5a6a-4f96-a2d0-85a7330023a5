class GetEmails
  include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  prepend SimpleCommand

  def initialize(filter_params)
    @filter_params = filter_params
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      auth_data = command.result

      unless auth_data.can_read_emails?
        Rails.logger.error "Unauthorised: User does not have access to read emails"
        raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
      end

      command = FilterEmailsQuery.call(auth_data, @filter_params.to_h.with_indifferent_access)
      command.result
    else
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end
end
