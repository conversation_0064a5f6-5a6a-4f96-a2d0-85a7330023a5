require 'google/apis/gmail_v1'
class FetchGmailHistory
  prepend SimpleCommand

  def initialize(params)
    data = JSON.parse(Base64.decode64(params["data"]))
    @email = data['emailAddress']
    @history_id = data['historyId']
    @message_publish_time = params["publish_time"]
  end

  def call
    gmail = Google::Apis::GmailV1::GmailService.new
    command = GetConnectedAccount.call @email
    unless command.result.present?
      Rails.logger.info "Connected account not found for email: #{@email}"
      return
    end
    connected_account = command.result
    return if connected_account.last_synced_at.present? && (connected_account.last_synced_at > @message_publish_time)
    connected_account.update(last_synced_at: DateTime.now)
    add_token_in_current_thread connected_account
    command = GetConnectedAccountAccessToken.call(command.result, skip_auth: true)
    if command.success?
      begin
        message_ids = []
        gmail.authorization = Signet::OAuth2::Client.new(access_token: command.result)
        labels_res = gmail.list_user_labels('me')
        labels = labels_res.labels.collect(&:id) - ["CHAT", "TRASH", "DRAFT", "SPAM", "IMPORTANT", "STARRED", "UNREAD"]
        labels.each do |label|
          res = gmail.list_user_histories('me', label_id: label, start_history_id: connected_account.last_history_id)
          unless res.history.blank?
            Rails.logger.info "SIZE FetchGmailHistory: #{res.history.count} | connected_account: #{connected_account.id}"
          end
          # Rails.logger.info "GC size: #{GC.stat} | connected account: #{connected_account.id}"
          next if res.history.blank?
          res.history.each do |h|
            message_ids << h.messages.collect(&:id)
          end
          message_ids.compact!
        end
      rescue Exception => e
        Rails.logger.info "Exception #{connected_account.last_history_id} #{e.to_s}"
      end
    end
    [message_ids.flatten.uniq, connected_account, @history_id]
  end

  private

  def add_token_in_current_thread connected_account
    thread = Thread.current
    thread[:token] = GenerateToken.call(connected_account.user_id, connected_account.tenant_id).result
  end
end
