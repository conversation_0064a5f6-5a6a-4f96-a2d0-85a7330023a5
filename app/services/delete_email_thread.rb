class DeleteEmailThread
  prepend SimpleCommand
  include RecordActionsProcessor

  def initialize(id, publish_usage = true)
    @id = id
    # Do not publish usage only when publish usage is false
    Rails.logger.info "Publish Usage: #{publish_usage}"
    @publish_usage = (publish_usage == 'false' ? false : (publish_usage || publish_usage.nil?))
    Rails.logger.info "Publish Usage: #{@publish_usage}"
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, user_id: @auth_data.user_id, active: true)
      user_id = @auth_data.user_id
      tenant_id = @auth_data.tenant_id

      thread = EmailThread.find_by(id: @id, tenant_id: tenant_id)
      raise(ExceptionHandler::NotFound, ErrorCode.not_found) unless thread
      raise(ExceptionHandler::DeleteNotAllowedError, ErrorCode.delete_not_allowed) unless user_can_delete_thread?(thread)

      begin
        data_for_publisher = BuildDataForEmailUnrelatedPublisher.call(thread.emails).result
        deletion_message = "EMAIL Thread ID #{thread.id}, subject: #{thread.emails.first.subject} is deleted by tenantId #{tenant_id}, userId #{user_id}"
        deleted_serialized_emails = []

        thread.emails.each do |email|
          deleted_serialized_emails << EmailDetailsSerializer.call(email, false, FetchUser.call(user_id, tenant_id).result, false, true, add_owner_id: true).result
        end

        Attachment.where(email_id: thread.emails.pluck(:id)).update_all(deleted: true)
        thread.emails.update_all(deleted: true)
        thread.update(deleted: true)
        deleted_serialized_emails.each do |deleted_serialized_email|
          PublishEvent.call(Event::EmailDeletedWorkflowV2.new(deleted_serialized_email))
          Rails.logger.info "Email Deleted Workflow V2 publisher called Email ID #{deleted_serialized_email['id']} Tenant ID #{deleted_serialized_email['tenantId']}"
          deleted_serialized_email.delete('emailsMatchingGlobalMessageId')
          EmailDeleteEventPublisher.call(deleted_serialized_email, DELETE_ACTION)
        end

        Rails.logger.info deletion_message
        EmailUnrelatedToContactEventPublisher.call(data_for_publisher) if data_for_publisher
        PublishUsageJob.perform_later(tenant_id) if @publish_usage
      rescue => e
        Rails.logger.info "Delete EmailThread:: Failed to delete thread: #{e.message}"
      end
    else
      Rails.logger.error 'Unauthorised: User context missing in DeleteEmailThread'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end
end
