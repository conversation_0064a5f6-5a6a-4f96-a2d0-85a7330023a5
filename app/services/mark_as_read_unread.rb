class MarkAsReadUnread
  prepend SimpleCommand
  include RecordActionsProcessor

  def initialize(params={})
    @email_id = params[:email_id]
    @thread_id = params[:thread_id]
    @read = params[:read]
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, user_id: @auth_data.user_id, active: true)
      @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result
      @email_id.present? ? mark_email : @thread_id.present? && mark_emails_for_thread
    else
      Rails.logger.error 'Unauthorised: User context missing in MarkAsReadUnread'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end

  private

  def mark_email
    email = Email.where(tenant_id: @user.tenant_id, id: @email_id).first

    if email.nil?
      Rails.logger.error 'Email Not found while Mark email as read / unread'
      raise(ActiveRecord::RecordNotFound, ErrorCode.not_found("email: #{@email_id}"))
    end

    unless user_can_read?(email)
      Rails.logger.error "Unauthorised: User cannot mark email as read / unread. User id #{@user.id} Email id #{@email_id}"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end

    @read ? !email.read_by.include?(@user.id) && email.read_by.append(@user.id) : email.read_by.delete(@user.id)

    unless email.save
      Rails.logger.error "Error while marking email as read / unread #{email.errors.to_a.join(',')}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end

    # Returns true when all emails in thread are read
    Email.where(tenant_id: @auth_data.tenant_id, email_thread_id: email.email_thread_id)
          .where.not('read_by @> ARRAY[?]::bigint[]', [@auth_data.user_id]).empty?
  end

  def mark_emails_for_thread
    emails_for_thread = Email.where(tenant_id: @user.tenant_id, email_thread_id: @thread_id)

    unless emails_for_thread.exists?
      Rails.logger.error 'Emails not found for thread while mark thread as read / unread'
      raise(ExceptionHandler::NotFound, ErrorCode.not_found("email_thread: #{@thread_id}"))
    end

    if user_can_read_thread?(@thread_id)
      if @read
        emails_for_thread.where.not('read_by @> ARRAY[?]::bigint[]', [@user.id])
          .update_all(['read_by = array_append(read_by, ?::bigint)', @user.id])
      else
        emails_for_thread.where('read_by @> ARRAY[?]::bigint[]', [@user.id])
          .update_all(['read_by = array_remove(read_by, ?::bigint)', @user.id])
      end
    else
      Rails.logger.error "Unauthorised: User cannot mark thread as read / unread. User id #{@user.id} Thread id #{@thread_id}"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end

  rescue ActiveRecord::ActiveRecordError => e
    Rails.logger.error "Error while marking email thread as read / unread thread: #{@thread_id} for tenant: #{@user.tenant_id} error: #{e.message}"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  end
end
