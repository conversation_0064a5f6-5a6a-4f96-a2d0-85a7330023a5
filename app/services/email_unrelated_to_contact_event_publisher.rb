require 'rest-client'

class EmailUnrelatedToContactEventPublisher
  prepend SimpleCommand
  def initialize(data)
    @data = data
  end

  def call
    @data.each do |datum|
      event = Event::EmailUnrelatedToContact.new(datum[:owner], datum[:user_id], datum[:related_to_id], datum[:email_id])
      PublishEvent.call(event)
      Rails.logger.info "Event::EmailUnRelatedToContact event: #{event.to_json}"
    end
  end
end
