class SaveAttachmentsFromImap
  prepend SimpleCommand

  def initialize(params)
    @email       = params[:email]
    @attachments = params[:attachments]
    @account_id = params[:account_id]
  end

  def call
    return if @attachments.blank? || @email.blank? || @account_id.blank?

    begin
      attachments_to_create = []
      @attachments.each do |attachment|
        response = RestClient.get(
          SERVICE_EMAIL_ENGINE + "/v1/account/#{@account_id}/attachment/#{attachment[:id]}",
        )

        attachment_body = response.body
        attachment_file = File.new("#{Rails.root}/tmp/#{DateTime.now.to_i}_#{@account_id}_#{attachment[:name]}", 'wb')
        attachment_file.write(attachment_body)
        attachments_to_create <<  HashWithIndifferentAccess.new({
          data: attachment_file,
          fileName: attachment[:name],
          type: attachment[:type],
          content_id: attachment[:content_id]
        })
      end

      CreateAttachment.call(@email, attachments_to_create, nil, true)
    rescue StandardError => e
      Rails.logger.error "Email Service | Failed to create received attachment: #{e.message} | Email: #{@email}"
    end
  end
end
