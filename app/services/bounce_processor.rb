# frozen_string_literal: true

class BounceProcessor
  prepend SimpleCommand

  def initialize(message, connected_account)
    @message = message
    @connected_account = connected_account
  end

  def call
    bounce_info = bounce_detector_class.call(@message)
    return unless bounce_info.dig(:is_bounced)

    process_bounce(bounce_info)
  end

  private

  def bounce_detector_class
    case @connected_account.provider_name
    when GOOGLE_PROVIDER
      GmailBounceDetector
    when MICROSOFT_PROVIDER
      OutlookBounceDetector
  end

  def process_bounce(bounce_info)
    original_email = find_original_email(bounce_info)

    if original_email.present?
      original_email.update!(
        bounce_type: bounce_info[:bounce_type],
        failed_reason: bounce_info[:failed_reason],
        status: Email.statuses['failed']
      )

      Rails.logger.info "Email Bounce processed: Original email #{original_email.id} marked as #{bounce_info[:bounce_type]} bounce"
    else
      Rails.logger.warn "Email Bounce: Could not find original email"
    end
  end

  def find_original_email(bounce_info)
    message_id = bounce_info[:original_message_id]
    return nil unless message_id

    Email.where(connected_account: @connected_account)
         .where(
           "global_message_id = ?",
           message_id
         )
         .where(direction: Email.directions[:sent])
         .first
  end
end
