class ForwardEmail
  prepend SimpleCommand
  include RecordActionsProcessor

  def initialize params
    @params = params
  end

  def call
    return unless @params[:id].present?

    command = GetSecurityContext.call
    unless command.success?
      Rails.logger.error 'Unauthorised: User context missing in CreateEmail'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end

    @auth_data = command.result
    @connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, user_id: @auth_data.user_id, active: true)
    email = Email.where(tenant_id: @auth_data.tenant_id, id: @params[:id]).first

    if email.nil?
      Rails.logger.error 'Email Not found while forwarding'
      raise(ActiveRecord::RecordNotFound, ErrorCode.not_found("email: #{@email_id}"))
    end

    unless user_can_forward?(email)
      Rails.logger.error "Unauthorised: User cannot forward email. User id #{@auth_data.user_id} Email id #{@params[:id]}"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end

    @params[:forwardTo] = email.id
    @params[:thread_id] = email.email_thread_id if email.email_thread.owner_id == @auth_data.user_id
    CreateEmail.call(@params).result
  end
end
