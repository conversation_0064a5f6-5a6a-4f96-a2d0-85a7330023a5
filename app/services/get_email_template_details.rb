class GetEmailTemplateDetails
  prepend SimpleCommand

  def initialize id
    @id = id
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result

      @email_template = EmailTemplate.find(@id)
      unless has_read_permission_on_email_template
        Rails.logger.error 'Insufficient permission: Read email template'
        raise(ExceptionHandler::ReadEmailTemplateNotAllowedError,ErrorCode.read_email_template_not_allowed)
      end

      return replace_variables
    else
      Rails.logger.error 'Unauthorised: User context missing in CreateEmailTemplate'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end

  private

  def has_read_permission_on_email_template
    email_template_permission = @auth_data.permissions.find{ |p| p.name == 'email_template' }
    return true if email_template_permission&.action&.read_all && @email_template.tenant_id.eql?(@auth_data.tenant_id.to_i)
    return true if email_template_permission&.action&.read && @email_template.created_by_id.eql?(@auth_data.user_id)
    false
  end

  def replace_variables
    valid_variables = V2::GetVariables.call(@email_template.category).result
    body = @email_template.body
    subject = @email_template.subject

    variables_in_body = body.scan(/{{[\s|\w|\.]+}}/).map(&:strip).uniq

    conditional_variables_in_body = body.scan(/({{[\s|\w|\.]+}, {if missing: [^{}]*[\s|\w]*}})/).flatten.map(&:strip).uniq
    variables_in_body.each do |variable|
      body = replace_normal_variable(body, valid_variables, variable)
    end

    conditional_variables_in_body.each do |variable|
      body = replace_conditional_variable(body, valid_variables, variable)
    end

    variables_in_subject = subject.scan(/{{[\s|\w|\.]+}}/).map(&:strip).uniq
    conditional_variables_in_subject = subject.scan(/({{[\s|\w|\.]+}, {if missing: [^{}]*[\s|\w]*}})/).flatten.map(&:strip).uniq

    variables_in_subject.each do |variable|
      subject = replace_normal_variable(subject, valid_variables, variable)
    end

    conditional_variables_in_subject.each do |variable|
      subject = replace_conditional_variable(subject, valid_variables, variable)
    end

    {body: body, subject: subject, email_template: @email_template}
  end

  def replace_normal_variable(text, valid_variables, variable_in_text)
    variable =
      if variable_in_text.include?('.')
        valid_variables.find { |v| variable_in_text == "{{#{v['entity']}.#{v['internalName']}}}"}
      else
        valid_variables.select { |v| v['entity'] == @email_template.category }
                        .find { |v| variable_in_text == "{{#{v['internalName']}}}"}
      end

    return text unless variable.present?
    text.gsub(variable_in_text, "{{#{variable["displayName"]}}}")
  end

  def replace_conditional_variable(text, valid_variables, variable_in_text)
    actual_variable = variable_in_text.scan(/{{[\s|\w|\.]*},/).first
    variable =
      if variable_in_text.include?('.')
        valid_variables.find { |v| actual_variable == "{{#{v['entity']}.#{v['internalName']}}," }
      else
        valid_variables.select { |v| v['entity'] == @email_template.category }
                        .find { |v| actual_variable == "{{#{v['internalName']}}," }
      end
    return text unless variable.present?
    text.gsub(actual_variable, "{{#{variable["displayName"]}},")
  end
end

