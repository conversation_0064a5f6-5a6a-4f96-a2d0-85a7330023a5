# frozen_string_literal: true

class GetUsersWithPicklistValues < GetEntityWithPicklistValues

  def initialize(user_fields_ids)
    @skip_fetch_user_settings = true
    @user_fields_ids = user_fields_ids.reject { |_, user_id| user_id.blank? }
    super(@user_fields_ids.values.uniq.join(','), nil, LOOKUP_USER)
  end

  def process_variables(data)
    @user_fields_ids.inject({}.with_indifferent_access) do |hash, (user_field, user_id)|
      user_data = data['content'].find { |u_data| u_data['id'] == user_id }&.deep_dup || {}
      if user_data.present?
        user_data = set_picklist_values(user_data)
        user_data['phoneNumbers'] = user_data['phoneNumbers'].to_a.map { |phones| "#{phones['dialCode']} #{phones["value"]}" }.join(', ')

        if data['metaData'].present?
          id_name_store = data['metaData']['idNameStore'].deep_dup || {}
          id_name_store.delete_if { |c, v| v.blank? }
          id_name_store.each{ |field, values| user_data[field] = values[user_data[field].to_s] || user_data[field] }
        end

        hash.merge!(user_field => user_data.slice(*USER_ALLOWED_FIELDS))
      end

      hash
    end
  end
end
