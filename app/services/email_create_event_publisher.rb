# frozen_string_literal: true

class EmailCreateEventPublisher
  prepend SimpleCommand

  def initialize(id, action)
    @action = action
    @id = id
  end

  def call
    Rails.logger.info "Email create event publisher called"
    event = Event::EmailAction.new(create_payload, @action)
    PublishEvent.call(event)
  end

  private

  def create_payload
    email = Email.find_by(id: @id)
    entity_data = EmailDetailsSerializer.call(email, false, add_owner_id: true).result
    {
      "entity" => entity_data,
      "oldEntity" => nil,
      "metadata" => {
        "tenantId" => email.tenant_id,
        "userId" => email.owner.id,
        "entityId" => email.id,
        "entityType" => 'Email',
        "entityAction" => 'CREATED'
      }
    }
  end
end

