class V1::GetVariables
  prepend SimpleCommand

  def initialize category
    @category = category
  end

  def call
    command = GetSecurityContext.call

    if command.success?
      if [LOOKUP_LEAD, <PERSON><PERSON><PERSON>UP_CONTACT, LOOKUP_DEAL, <PERSON><PERSON><PERSON><PERSON>_CALL, LOOKUP_MEETING, LO<PERSON>UP_TASK].include?(@category)
        command = "GetFields::#{@category.camelize}".constantize.call
        return allowed_entity_variables(command.result) if command.success?
      else
        Rails.logger.error "V1::GetVariables Invalid category #{@category}"
        raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      end
    else
      Rails.logger.error 'Unauthorised: User context missing in V1::GetVariables'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end

  private

  def allowed_entity_variables(fields)
    excluded_fields = "#{@category.upcase}_EXCLUDED_FIELDS".constantize
    fields.reject { |field| excluded_fields.include?(field['internalName']) || field['type'] == 'CHECKBOX' }
  end
end
