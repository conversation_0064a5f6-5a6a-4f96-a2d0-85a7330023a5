# frozen_string_literal: true

class AssociateEmailThreadWithDeals
  prepend SimpleCommand

  def initialize(params)
    @deals = params[:deals]
    @email_thread_id = params[:id]
  end

  def call
    command = GetSecurityContext.call

    if command.success?
      @auth_data = command.result
      @tenant_id = @auth_data.tenant_id
    else
      Rails.logger.error 'Unauthorised: User context missing in AssociateEmailThreadWithDeals'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end

    load_email_thread

    @deals.each do |deal|
      deal[:entity_id] = deal[:id]
      deal[:entity] = LOOKUP_DEAL
    end

    verified_deals = ValidateDeals.call(build_recipients).result

    @email_thread.emails.each do |email|
      old_serialized_email_data = EmailDetailsSerializer.call(email, false, nil, true, add_owner_id: true).result

      deal_look_up_ids = email.related_to.where(entity_type: LOOKUP_DEAL).collect(&:id)
      removed_deal_look_up_ids = deal_look_up_ids - verified_deals.collect(&:id)
      
      verified_deals.each do |deal|
        look_up = email.related_to.find_by(entity_id: deal.entity_id, entity_type: LOOKUP_DEAL)
        if look_up.blank?
          email.related_to << deal
        end
      end
    
      email.email_look_ups.where(look_up_id: removed_deal_look_up_ids).delete_all
      deal_ids = (email.reload.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_DEAL).pluck(:entity_id) + email.email_thread.deal_ids.to_a).uniq
      email.email_thread.update(deal_ids: deal_ids)
      PublishEvent.call(Event::EmailUpdatedV2.new(email.reload, old_serialized_email_data))
    end
  end

  private

  def build_recipients
    recipients_data = @deals || []
    recipients = recipients_data.collect do |pdata|
      pdata[:tenant_id] = @tenant_id
      look_up = GetLookUp.call(pdata).result
      look_up.name = pdata[:name] if pdata[:name]
      look_up.save! unless look_up.new_record?
      look_up
    end
  end

  def load_email_thread
    @email_thread = EmailThread.find_by(tenant_id: @tenant_id, id: @email_thread_id)
    if @email_thread.blank?
      Rails.logger.error "Email thread not found: User id #{@auth_data.user_id} email thread id #{@email_thread_id}"
      raise(ExceptionHandler::NotFound, ErrorCode.not_found)
    end
  end
end
