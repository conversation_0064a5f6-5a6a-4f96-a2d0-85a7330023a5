# frozen_string_literal: true

class PermanentDeleteEntities
  prepend Simple<PERSON>ommand

  def call
    EmailThread.unscoped.where(deleted: true).find_in_batches do |email_threads|
      email_ids = Email.unscoped.where(email_thread_id: email_threads.collect(&:id)).select("emails.id as email_id").collect(&:email_id)
      attachments = Attachment.unscoped.where(email_id: email_ids)
      attachment_file_names = attachments.pluck(:file_name)
      DeleteFileFromS3.call(attachment_file_names, S3_EMAIL_BUCKET) unless attachment_file_names.blank?
      attachments.delete_all
      EmailTrackLog.where(email_id: email_ids).delete_all
      EmailLinkLog.where(email_id: email_ids).delete_all
      TrackMapping.where(email_id: email_ids).delete_all
      LinkMapping.where(email_id: email_ids).delete_all
      EmailLookUp.unscoped.where(email_id: email_ids).delete_all
      Email.unscoped.where(id: email_ids).delete_all
      EmailThread.unscoped.where(id: email_threads.collect(&:id)).delete_all
    end

    LookUp.unscoped.where(deleted: true).find_in_batches do |lookups_batch|
      lookup_ids = lookups_batch.map(&:id)
      EmailLookUp.unscoped.where(look_up_id: lookup_ids).delete_all
      LookUp.unscoped.where(id: lookup_ids).delete_all
    end

    Email.unscoped.where(deleted: true).find_in_batches do |emails_batch|
      email_ids = emails_batch.map(&:id)
      attachments = Attachment.unscoped.where(email_id: email_ids)
      attachment_file_names = attachments.pluck(:file_name)
      DeleteFileFromS3.call(attachment_file_names, S3_EMAIL_BUCKET) unless attachment_file_names.blank?
      attachments.delete_all
      EmailTrackLog.where(email_id: email_ids).delete_all
      EmailLinkLog.where(email_id: email_ids).delete_all
      TrackMapping.where(email_id: email_ids).delete_all
      LinkMapping.where(email_id: email_ids).delete_all
      EmailLookUp.unscoped.where(email_id: email_ids).delete_all
      Email.unscoped.where(id: email_ids).delete_all
    end
  end
end
