require 'google/apis/gmail_v1'
class RegisterOutlookWebhook
  prepend SimpleCommand
  include GraphApiClient

  def initialize(connected_account)
    @connected_account = connected_account
  end

  def call
    begin
      command = GetConnectedAccountAccessToken.call(@connected_account, skip_auth: true)
      if command.success?
        @token = command.result
        parsed_res = subscribe_webhook
        subscription_id = parsed_res['id']
        expiry = Time.parse(parsed_res['expirationDateTime']).to_i
        @connected_account.update(subscription_id: subscription_id, subscription_expiry: expiry)
        @connected_account
      end
    rescue RestClient::Unauthorized => e
      Rails.logger.error "Subscribe outlook - unauthorized: #{e.response.body}"
      raise(ExceptionHandler::OutlookAuthenticationError, ErrorCode.outlook_authentication_failed)
    rescue RestClient::ExceptionWithResponse => e
      Rails.logger.info "Error while registering the outlook webhook #{e.response.body}"
      raise(ExceptionHandler::ThirdP<PERSON>yAPIError, ErrorCode.third_party_api)
    end
  end
end
