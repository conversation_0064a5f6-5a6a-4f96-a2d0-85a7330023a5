class DeleteOutlookSubscription
  prepend SimpleCommand
  include GraphApiClient

  def initialize(subscription_id, token = nil)
    @subscription_id = subscription_id
    @token = token
  end

  def call
    begin
      delete_subscription(@subscription_id)
    rescue RestClient::ExceptionWithResponse => e
      Rails.logger.error "Error in outlook delete subscription API: #{e.response.body}"
      raise(ExceptionHandler::ThirdPartyAPIError, ErrorCode.third_party_api)
    end
  end
end