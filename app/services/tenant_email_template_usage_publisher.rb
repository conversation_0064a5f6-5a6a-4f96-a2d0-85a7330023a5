class TenantEmailTemplateUsagePublisher
  def self.publish(tenant_id)
    #total_attachment_size = Attachment.total_attachment_size_for_tenant(tenant_id)
    #total_template_attachment_size = TemplateAttachment.total_attachment_size_for_tenant(tenant_id)
    usage = [
      {
        tenantId: tenant_id,
        count: EmailTemplate.active.where(tenant_id: tenant_id).count,
        usageEntity: 'EMAIL_TEMPLATE'
      }
      #  {
      #    tenantId: tenant_id,
      #    count: total_attachment_size + total_template_attachment_size,
      #    usageEntity: 'STORAGE_EMAIL_ATTACHMENT'
      #  }
    ]
    event = Event::TenantUsage.new(usage)
    PublishEvent.call(event)
    Rails.logger.info "Event::TenantEmailTemplateUsagePublisher data #{event.to_json}"
  end
end
