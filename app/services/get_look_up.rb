class GetLookUp
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize look_up_data
    @data = look_up_data
  end

  def call
    if @data[:id].blank? && @data[:entity].eql?('email')
      @data[:entity] = LOOKUP_CUSTOM_EMAIL
      #@data[:id] = @data[:email]
      @data[:name] = @data[:email]
      build_lookup
    elsif @data[:id].blank?
      raise ExceptionHandler::InvalidDataError, ErrorCode.invalid
    elsif @data[:tenant_id].blank?
      raise ExceptionHandler::InvalidDataError, ErrorCode.invalid
    elsif @data[:entity].blank?
      raise ExceptionHandler::InvalidDataError, ErrorCode.invalid
    else
      build_lookup
    end
  end

  private
  def build_lookup
    entity = @data[:entity].eql?(LOOKUP_CUSTOM_EMAIL) ? @data[:entity] : "#{@data[:entity]}_#{@data[:id]}"
    look_up = LookUp.find_or_initialize_by(
      tenant_id: @data[:tenant_id],
      entity: entity,
      entity_id: @data[:id],
      entity_type: @data[:entity],
      email: "#{@data[:email]}"
    )
    if look_up.new_record?
      look_up.name = @data[:name]
    end
    look_up
  end
end

