require 'google/apis/gmail_v1'
class SendEmail
  attr_accessor :source_thread_id, :reply_to_email_id, :tenant_id
  prepend SimpleCommand

  def initialize(params)
    @params = params
  end

  def call
    @connected_account = @params[:email].connected_account
    raise(ExceptionHandler::NotConnectedError, ErrorCode.not_connected) unless @connected_account.present?
    
    if @connected_account.provider_name.eql?(CUSTOM_PROVIDER)
      sender.call(@params).result
    else
      command = GetConnectedAccountAccessToken.call(@connected_account)
      if command.success?
        token = command.result
        sender.call(@params, token).result
      end
    end 
  end

  private

  def sender
    case @connected_account.provider_name
    when GOOGLE_PROVIDER
      GmailMailSender
    when MICROSOFT_PROVIDER
      OutlookMailSender
    when CUSTOM_PROVIDER
      SmtpMailSender
    else
      raise(ExceptionHandler::InvalidMailProviderNameError, ErrorCode.invalid_mail_provider_name)
    end
  end
end
