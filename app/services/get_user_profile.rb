require 'rest-client'

class GetUserProfile
  prepend SimpleCommand

  def initialize(user_id = nil, token = nil)
    @user_id = user_id
    @token = token
  end

  def call
    token = @token.blank? ? fetch_token : @token

    url = @user_id ? "/v1/users/#{@user_id}" : "/v1/users/me"

    Rails.logger.info "Set_owner:: Url to get profile #{url}"
    begin
      response = RestClient.get(
        SERVICE_IAM + url,
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "Get User Profile - invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "Get User Profile - 404"
      raise(ExceptionHandler::InvalidOwnerUserError, ErrorCode.invalid_owner)
    rescue RestClient::InternalServerError
      Rails.logger.error "Get User Profile - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "Get User Profile - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end

  private

  def fetch_token
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?

    command.result
  end
end

