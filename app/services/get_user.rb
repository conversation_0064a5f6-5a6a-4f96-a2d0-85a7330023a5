# frozen_string_literal: true
require 'rest-client'

class GetUser
  prepend SimpleCommand

  def initialize(user_ids)
    @user_ids = user_ids
  end

  def call
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result

    payload = { fields: USER_ALLOWED_FIELDS }
    rules = [
      {
        field: 'id',
        id: 'id',
        operator: 'in',
        type: 'double',
        value: @user_ids
      }
    ]

    payload[:jsonRule] = { rules: rules, condition: 'AND', valid: true }

    begin
      response = RestClient.post(
        SERVICE_IAM + "/v1/users/search?sort=updatedAt,desc&page=0&size=100",
        payload.to_json,
        {
          Authorization: "Bearer #{token}",
          content_type: :json
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "SearchUsers invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "SearchUsers 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "SearchUsers 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "SearchUsers 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
