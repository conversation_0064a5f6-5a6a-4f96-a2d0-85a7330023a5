# frozen_string_literal: true

require 'rest-client'

class SharedAccess
  def initialize(entity)
    @entity = entity
  end

  def fetch
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result

    return {} unless [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].include?(@entity)

    begin
      response = RestClient.get(
        "#{SERVICE_CONFIG}/v1/internal/share/access/#{@entity.upcase}/EMAIL",
        {
          Authorization: "Bearer #{token}"
        }
      )

      return JSON(response.body) unless response.nil?

      Rails.logger.error "SharedAccess - invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)

    rescue RestClient::NotFound
      Rails.logger.error "SharedAccess - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "SharedAccess - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "SharedAccess - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
