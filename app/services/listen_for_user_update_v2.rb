class ListenForUserUpdateV2
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize
  end

  def call
    subscribe_user_updated_event
  end

  private
  def subscribe_user_updated_event
    RabbitmqConnection.subscribe(USER_EXCHANGE, USER_UPDATED_V2_EVENT, USER_UPDATED_V2_QUEUE) do |payload|
      Rails.logger.debug "Received message #{payload} for #{USER_UPDATED_V2_EVENT}"
      payload = JSON(payload)
      id = payload['entity']['id']

      profile_id = payload['entity']['profile']['id']
      user = User.find_by(id: id)
      return if user.blank?
      user.update(profile_id: profile_id) if user.profile_id.blank? || !user.profile_id.eql?(profile_id)
    end
  end
end
