require 'rest-client'
class ListImapSentBox
  prepend SimpleCommand

  LABELS = ['Sent', '[Gmail]/Sent Mail']

  def initialize(account_id)
    @account_id = account_id
  end

  def call
    return if @account_id.blank?
    response = list_sent_box
  end

  private

  def list_sent_box
    LABELS.each do |label|
      begin
        response = RestClient.get(
          SERVICE_EMAIL_ENGINE + "/v1/account/#{@account_id}/messages?path=#{label}",
        )
        Rails.logger.info "EmailEngine list sent box  #{@account_id} #{label} | Success"
      rescue Exception => e
        Rails.logger.error "EmailEngine List sent box #{@account_id} #{label}| Error: #{e.to_s}"
      end
    end
  end
end
