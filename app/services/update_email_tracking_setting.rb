class UpdateEmailTrackingSetting
  prepend SimpleCommand

  def initialize(tenant_id, params)
    @tenant_id = tenant_id
    @data = params.with_indifferent_access
  end

  def call
    email_tracking_add_on = @data[:usageEntityLimits].find{ |k| k[:usageEntity] == 'EMAIL_TRACKING'}
    enabled = enabled = email_tracking_add_on  ? (email_tracking_add_on[:limit] > 0 ? true : false) : false
    tenant = Tenant.find_or_initialize_by(id: @tenant_id)
    tenant.tracking_enabled = enabled
    tenant.save
  end
end
