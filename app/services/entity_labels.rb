require 'rest-client'

class EntityLabels
  prepend SimpleCommand

  def initialize(entity)
    @entity = entity
  end

  def call
    if [LOOKUP_CALL, LOOKUP_MEETING].include?(@entity)
      {
        displayName: @entity.titleize,
        displayNamePlural: "#{@entity.titleize}s"
      }.with_indifferent_access
    else
      token = GetSecurityContext.call(:token).result

      response = RestClient.get(
        SERVICE_CONFIG + '/v1/entities/label',
        {
          Authorization: "Bearer #{token}"
        }
      )

      JSON(response.body).with_indifferent_access[@entity.upcase]
    end
  end
end
