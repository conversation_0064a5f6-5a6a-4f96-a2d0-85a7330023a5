require 'rest-client'

class BuildDataForEmailUnrelatedPublisher
  prepend SimpleCommand
  def initialize(emails)
    @emails = emails
  end

  def call
    data_for_publisher = []
    @emails.each do |email|
      recipient_users = []
      recipient_users << email.to_recipients.where(entity_type: LOOKUP_USER).select('entity_id').map(&:entity_id)
      recipient_users << email.cc_recipients.where(entity_type: LOOKUP_USER).select('entity_id').map(&:entity_id)
      recipient_users << email.bcc_recipients.where(entity_type: LOOKUP_USER).select('entity_id').map(&:entity_id)
      recipient_users << email.sender.id if email.sender.entity_type.eql?(LOOKUP_USER)
      deals = email.related_to.where(entity_type: LOOKUP_DEAL)
      deals.each do |deal|
        associated_contacts = GetContactsForDeal.call(deal.entity_id).result
        contacts = filter_associated_contacts(associated_contacts, email)
        next if contacts.blank?
        contacts.each do |contact|
          owner_user = { id: contact[:owner_id], tenant_id: email.tenant_id }
          related_to_id = contact['id']
          recipient_users.flatten.compact.each do |user_id|
            data_for_publisher << { owner: owner_user , user_id: user_id, related_to_id: related_to_id, email_id: email.id}
          end
        end
      end
    end
    return data_for_publisher
  end

  private

  def filter_associated_contacts(associated_contacts, email)
    return [] unless associated_contacts.present?
    check_if_associated_contact = -> (elems, c) { elems.find { |el| el['id'].to_s == c['id'].to_s } }
    contacts = []
    to = email.to_recipients.where(entity_type: LOOKUP_CONTACT).select(:entity_id).map{ |c| {'id' => c.entity_id}}
    cc = email.cc_recipients.where(entity_type: LOOKUP_CONTACT).select(:entity_id).map{ |c| {'id' => c.entity_id}}
    bcc = email.bcc_recipients.where(entity_type: LOOKUP_CONTACT).select(:entity_id).map{ |c| {'id' => c.entity_id}}
    entities = to + cc + bcc
    return [] if entities.blank?
    associated_contacts.each do |c|
      contact = check_if_associated_contact.call(entities, c)
      contacts << contact.merge(owner_id: c['ownerId']) if contact
    end
    contacts
  end
end
