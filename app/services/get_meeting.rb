# frozen_string_literal: true

require 'rest-client'

class GetMeeting
  prepend SimpleCommand

  def initialize(meeting_id)
    @meeting_id = meeting_id
  end

  def call
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    begin
      response = RestClient.get(
        "#{SERVICE_MEETING}/v1/meetings/#{@meeting_id}",
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?

    rescue RestClient::NotFound
      Rails.logger.error 'Get Meeting - 404'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error 'Get Meeting - 500'
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error 'Get Meeting - 400'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue StandardError => e
      Rails.logger.error "Get Meeting - invalid response - #{e}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
