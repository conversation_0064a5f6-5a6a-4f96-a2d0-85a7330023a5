class RecipientsValidator
  prepend SimpleCommand

  def initialize params
    @params = params
    @user = params[:user]
  end

  def call
    recipients = build_recipients
    Rails.logger.info "validate recipients #{recipients.to_json}"
    validated_recipients = []
    validated_recipients += validate_users(recipients.select{|x| x.is_a_user?})
    validated_recipients += validate_leads(recipients.select{|x| x.is_a_lead?})
    validated_recipients += validate_contacts(recipients.select{|x| x.is_a_contact?})
    validated_recipients += validate_deals(recipients.select{|x| x.is_a_deal?})
    validated_recipients += recipients.select{|x| x.is_a_custom_email?}
    Rails.logger.info "validated recipients #{validated_recipients.to_json}"
    validated_recipients
  end

  private

  def build_recipients
    recipients_data = @params[:recipients] || []
    recipients = recipients_data.collect do |pdata|
      pdata[:tenant_id] = @user.tenant_id
      look_up = GetLookUp.call(pdata).result
      look_up.name = pdata[:name] if pdata[:name]
      look_up.save! unless look_up.new_record?
      look_up
    end
    remove_duplicates(recipients)
  end

  def remove_duplicates recipients
    recipients.uniq { |participant| [participant.entity, participant.email] }
  end

  def validate_users user_recipients = []
    return [] if user_recipients.empty?
    ValidateUsers.call(user_recipients).result
  end

  def validate_leads lead_recipients = []
    return [] if lead_recipients.empty?
    ValidateLeads.call(lead_recipients).result
  end

  def validate_contacts contact_recipients = []
    return [] if contact_recipients.empty?
    ValidateContacts.call(contact_recipients).result
  end

  def validate_deals deal_recipients = []
    return [] if deal_recipients.empty?
    ValidateDeals.call(deal_recipients).result
  end
end
