class ActivateDeactivateEmailTemplate
  prepend SimpleCommand

  def initialize(params = {})
    @id = params[:id]
    @active = params[:active]
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      auth_data = command.result
      user = GetUserDetails.call(auth_data.user_id, auth_data.tenant_id).result

      unless auth_data.can_access?('email_template', 'update')
        raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
      end

      template = EmailTemplate.find_by(id: @id, tenant_id: auth_data.tenant_id)
      if template.present?
        if auth_data.can_access?('email_template', 'update_all') || template.created_by_id == user.id
          template.update(active: @active, updated_by: user)
        else
          raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
        end
      else
        raise(ExceptionHandler::NotFound, ErrorCode.not_found)
      end
    else
      Rails.logger.error 'Unauthorised: User context missing in ActivateDeactivateEmailTemplate'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end
end
