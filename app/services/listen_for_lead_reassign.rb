# frozen_string_literal: true

class ListenForLeadReassign
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(LEAD_EXCHANGE, LEAD_OWNER_UPDATED_EVENT, LEAD_OWNER_UPDATED_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{LEAD_OWNER_UPDATED_EVENT}"
      payload = JSON(payload)
      data = {}
      data[:entity_id] = payload['entityId']
      data[:new_owner_id] = payload['newOwnerId']
      data[:tenant_id] = payload['tenantId']

      UpdateOwnerForEntity.call(LOOKUP_LEAD, data).result
    end
  end
end
