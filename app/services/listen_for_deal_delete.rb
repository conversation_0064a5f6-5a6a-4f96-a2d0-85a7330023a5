class ListenForDealDelete
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(DEAL_EXCHANGE, DEAL_DELETED_EVENT, DEAL_DELETED_QUEUE) do |payload|
      Rails.logger.info "Received message for #{DEAL_DELETED_EVENT}"
      payload = JSON(payload)
      UpdateEmailForEntityDeleteEventJob.perform_later({ id: payload["id"], tenant_id: payload["tenantId"], user_id: payload["userId"], entity_type: LOOKUP_DEAL, publish_usage: payload['publishUsage'] })
    end
  end
end
