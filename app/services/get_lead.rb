require 'rest-client'

class GetLead
  prepend SimpleCommand

  def initialize(lead_id)
    @lead_id = lead_id
  end

  def call
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result

    begin
      response = RestClient.get(
        SERVICE_SALES + "/v1/leads/#{@lead_id}",
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "Get Lead - invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "Get Lead - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "Get Lead - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "Get Lead - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end