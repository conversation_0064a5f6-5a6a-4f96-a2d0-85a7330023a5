class ListenForConnectedAccount
  prepend SimpleCommand
  include ActiveModel::Validations

  attr_accessor :user_id, :tenant_id, :name, :data

  def call
    RabbitmqConnection.subscribe(USER_EXCHANGE, CONNECTED_ACCOUNT_SYNC_EVENT, CONNECTED_ACCOUNT_SYNC_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{CONNECTED_ACCOUNT_SYNC_EVENT}"
      payload = JSON(payload)
      @user_id = payload["userId"]
      @tenant_id = payload["tenantId"]
      @name = payload["firstName"].to_s + payload["lastName"].to_s
      @data = payload
      CreateConnectedAccount.call(@user_id, @tenant_id, @name, @data)
    end
  end
end
