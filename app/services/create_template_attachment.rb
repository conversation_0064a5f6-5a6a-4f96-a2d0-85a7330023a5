require 'google/apis/gmail_v1'
class CreateTemplateAttachment
  prepend SimpleCommand

  def initialize(email_template, files)
    @email_template = email_template
    @files = files
  end

  def call
    return unless @email_template && @files
    command = GetSecurityContext.call
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid) unless command.success?

    @files.each do |file|
      file_name = file['fileName'].split('.').first
      file_ext = file['fileName'].split('.').last
      file_name = "tenant_#{@email_template.tenant_id}/user_#{@email_template.created_by_id}/#{@email_template.id}_#{file_name}_#{DateTime.now.to_i}.#{file_ext}"
      file_size = file['data'].try(:size)
      if file['data'].present?
        file_path = file['data'].path
        Rails.logger.info "File path: #{file_path}"
        Rails.logger.info "File name: #{file_name}"
        UploadFileToS3.call(file_path, file_name, S3_EMAIL_TEMPLATE_BUCKET)
      end

      @email_template.template_attachments.create(file_name: file_name, file_size: file_size.to_i, inline: file['type'].eql?('inline'))
    end
  end
end
