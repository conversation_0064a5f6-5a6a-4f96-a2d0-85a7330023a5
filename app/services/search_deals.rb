# frozen_string_literal: true

require 'rest-client'

class SearchDeals
  prepend SimpleCommand

  def initialize(deal_ids, tenant_id)
    @deal_ids = deal_ids
    @tenant_id = tenant_id
  end

  def call
    return [] if @deal_ids.blank?
    response = search_deals
    if response['content'].present?
      response['content'] = response['content'].select { |deal| deal.dig('recordActions', 'email') }
    end

    response['content'].to_a.map do |deal_data|
      {
        entity: "#{LOOKUP_DEAL}_#{deal_data['id']}",
        name: deal_data['name'],
        owner_id: deal_data['ownerId'],
        tenant_id: @tenant_id
      }
    end
  end

  private

  def search_deals
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    begin
      response = RestClient.post(
        SERVICE_SEARCH + '/v1/search/deal?page=0&size=1000&sort=updatedAt,desc',
        {
          fields: %w[id name ownedBy ownerId],
          jsonRule: {
            condition: 'AND',
            rules: [
              {
                operator: 'in',
                id: 'id',
                field: 'id',
                type: 'double',
                value: @deal_ids.join(',')
              }
            ],
            valid: true
          }
        }.to_json,
        {
          Authorization: "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "ValidateDeal.deal_search_response invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "ValidateDeal.deal_search_response deal 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "ValidateDeal.deal_search_response deal 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "ValidateDeal.deal_search_response deal 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
