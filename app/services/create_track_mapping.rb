class CreateTrackMapping
  prepend SimpleCommand

  def initialize(body, email_id, tenant_id)
    @body = body
    @email_id = email_id
    @tenant_id = tenant_id
  end

  def call
    track_mapping = TrackMapping.create(email_id: @email_id, tenant_id: @tenant_id)
    @body = @body.to_s +  "<img src=\"#{API_HOST}/v1/email_mappings/#{track_mapping.id}?cache_buster=#{Time.now.to_i}\" width=\"1\" height=\"1\" id = \"img_05b324f0\" style=\"display: none; width: 1; height: 1; border: 0\"/>"
    return { body: @body, track_mapping: track_mapping }
  end
end
