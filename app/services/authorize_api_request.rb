class AuthorizeApiRequest
  prepend SimpleCommand

  def initialize(headers = {}, action="read")
    @headers = headers
    @action = action
  end

  def call
    auth_data = decode_auth_token
    #commenting the below check as need to setup this in auth on local
#    if auth_data.can_access?("email", @action)
      thread = Thread.current
      thread[:auth] = auth_data
      thread[:token] = http_auth_header
      auth_data
#    else
#      raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
#    end
  end

  private

  attr_reader :headers

  def decode_auth_token
    @decoded_auth_token ||= ParseToken.call(http_auth_header).result
  end

  def http_auth_header
    if headers['Authorization'].present?
      return headers['Authorization'].split(' ').last
    end
      raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
  end
end
