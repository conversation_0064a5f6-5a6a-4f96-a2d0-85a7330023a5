class CreateConnectedAccount
  prepend SimpleCommand
  include ActiveModel::Validations
  include GraphApiClient

  attr_accessor :user_id, :tenant_id, :name, :data

  def initialize(user_id, tenant_id, name, params)
    @user_id = user_id
    @tenant_id = tenant_id
    @data = params
    @name = name
  end

  def call
    Tenant.find_or_create_by(id: @tenant_id)
    @user =  User.find_by(id: @user_id) || User.create(id: @user_id, tenant_id: @tenant_id, name: @name)
    create_connected_account(@user)
  end

  private

  def create_connected_account(user)
    acct = ConnectedAccount.find_or_initialize_by(provider_name: @data['provider'], tenant_id: @tenant_id, email: @data['email'], user_id: user.id)
    expiry = Time.parse(@data['expiresAt']).to_i
    acct.assign_attributes(access_token: @data['accessToken'],
                           expires_at: expiry,
                           scopes: @data['scopes'],
                           active: true
                          )
    acct.save!
    register_webhook(acct)
    FetchDisplayName.call(acct)
  end

  def register_webhook(acct)
    thread = Thread.current
    thread[:token] = GenerateToken.call(@user_id, @tenant_id).result

    case acct.provider_name
    when GOOGLE_PROVIDER
      RegisterGmailWebhook.call(acct)
    when MICROSOFT_PROVIDER
      connected_account = RegisterOutlookWebhook.call(acct).result
      delete_old_subscriptions(connected_account)
    end
  end

  def delete_old_subscriptions(connected_account)
    command = GetConnectedAccountAccessToken.call(connected_account, skip_auth: true)
    if command.success?
      @token = command.result
      subscription_id_to_skip = connected_account.subscription_id
      begin
        subscriptions = get_subscriptions
        if subscriptions.present?
          subscriptions.each do |subscription|
            next if subscription['id'] == subscription_id_to_skip
            DeleteOutlookSubscription.call(subscription['id'], @token)
          end
        end
      end
    end
  end
end
