class GetEmailTemplates
  prepend SimpleCommand

  def initialize(filter_params)
    @filter_params = filter_params
    @auth_data = nil
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      user_id = @auth_data.user_id
      tenant_id = @auth_data.tenant_id
      if has_read_all_permission_on_email_template
        result = EmailTemplate.where(tenant_id: tenant_id)
      elsif has_read_permission_on_email_template
        result = EmailTemplate.where(tenant_id: tenant_id, created_by_id: user_id)
      else
        Rails.logger.error 'Insufficient permission: Read email template'
        raise(ExceptionHandler::ReadEmailTemplateNotAllowedError, ErrorCode.read_email_template_not_allowed)
      end

      result = filter_templates(result)
      result = sort_templates(result)
      result = paginate(result)
    else
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end

  def lookup
    rules = [
      {
        field: 'active',
        type: 'long',
        operator: 'equal',
        value: true
      }
    ]

    if @filter_params[:q].present?
      rules << {
        field: 'name',
        type: 'string',
        operator: 'contains',
        value: @filter_params[:q]
      }
    end

    if @filter_params[:category].present?
      rules << {
        field: 'category',
        type: 'long',
        operator: 'equal',
        value: @filter_params[:category]
      }
    end

    @filter_params[:jsonRule] = {
      rules: rules
    }

    call
  end

  private

  def get_filter_rules
    if json_rule = @filter_params[:jsonRule]
      if json_rule[:rules].present?
        rules = json_rule[:rules]
      end
    end
    return rules
  end

  def filter_templates(templates)
    rules = get_filter_rules
    return templates unless rules

    rules.each do |rule|
      if rule[:field] == 'category'
        templates = templates.where(category: rule[:value])
      elsif rule[:field] == 'name'
        templates = templates.where("name ILIKE ?", "#{rule[:value]}%")
      elsif rule[:field] == 'active'
        templates = templates.where(active: rule[:value])
      end
    end

    templates
  end

  def sort_templates(templates)
    columns = ['name', 'category', 'created_at', 'updated_at', 'created_by_id', 'active']
    if sort = @filter_params[:sort]
      col, order = sort&.split(',')
      col = col&.underscore
    else
      return templates
    end

    unless columns.include?(col) && ['asc', 'desc'].include?(order)
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end

    templates = templates.order("email_templates.#{col} #{order}")
    templates
  end

  def paginate(templates)
    page = @filter_params[:page] || 1
    size = @filter_params[:size] || 10
    templates.paginate(page: page.to_i, per_page: size.to_i)
  end

  def has_read_permission_on_email_template
    email_template_permission = @auth_data.permissions.find{ |p| p.name == 'email_template' }
    email_template_permission&.action&.read
  end

  def has_read_all_permission_on_email_template
    email_template_permission = @auth_data.permissions.find{ |p| p.name == 'email_template' }
    email_template_permission&.action&.read_all
  end
end