# frozen_string_literal: true

class SendEmailToEntity
  prepend SimpleCommand

  def initialize(params)
    @params = params
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      if @params[:emailTemplateId].present?
        email_template = EmailTemplate.find_by(tenant_id: @auth_data.tenant_id, id: @params[:emailTemplateId], category: @params[:relatedTo][:entity])
        if email_template.blank?
          Rails.logger.error "SendEmailToEntity EmailTemplate not found for tenant #{@auth_data.tenant_id} with id #{@params[:emailTemplateId]} for entity type #{@params[:relatedTo][:entity]}"
          raise(ExceptionHandler::NotFound, ErrorCode.not_found)
        elsif !email_template.active?
          Rails.logger.error "SendEmailToEntity EmailTemplate inactive for tenant #{@auth_data.tenant_id} with id #{@params[:emailTemplateId]}"
          raise(ExceptionHandler::InvalidDataError, ErrorCode.inactive_email_template)
        end
        email_data = GetEmailTemplateWithEntityDetails.call(@params[:emailTemplateId], @params[:relatedTo][:id]).result
        @params[:subject] = email_data[:subject]
        @params[:body] = email_data[:body]
        @params[:emailTemplateAttachments] = email_template.template_attachments.map{ |attachment| { id: attachment.id, fileName: attachment.extract_file_name(false) } }

        if @params[:campaign].present? && @params[:activity].present?
          @params[:campaignInfo] = {
            campaignId: @params.dig('campaign', 'id'),
            activityId: @params.dig('activity', 'id'),
            entityId: @params.dig(:relatedTo, :id),
            entitytype: @params.dig(:relatedTo, :entity),
            email: @params.dig(:relatedTo, :email)
          }
        end
      end
      CreateEmail.call(@params.to_h.with_indifferent_access, validate_entities: false)
    else
      Rails.logger.error 'Unauthorised: User context missing in SendEmailToEntity'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end
end
