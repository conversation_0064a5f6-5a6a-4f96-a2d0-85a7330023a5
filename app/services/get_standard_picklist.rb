require 'rest-client'

class GetStandardPicklist
  prepend SimpleCommand

  # local caching of standard picklist
  # Refer: activesupport-6.0.3/lib/active_support/core_ext/module/attribute_accessors_per_thread.rb
  thread_cattr_accessor :standard_picklist

  def call
    return standard_picklist if standard_picklist.present?

    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result

    Rails.logger.info "GetStandardPicklist called"
    begin
      response = RestClient.get(
        SERVICE_CONFIG + "/v1/picklists/standard",
        {
          'Authorization': "Bearer #{token}"
        }
      )

      if response.present?
        self.standard_picklist = JSON(response.body)
        return self.standard_picklist
      end

      Rails.logger.error "Get Picklist - invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "Get Picklist - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "Get Picklist - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "Get Picklist - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
