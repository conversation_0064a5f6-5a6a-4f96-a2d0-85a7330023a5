class CreateLinkMapping
  prepend SimpleCommand

  def initialize(body, email_id, tenant_id)
    @body = body
    @email_id = email_id
    @tracked_links = {}
    @tenant_id = tenant_id
  end

  def call
    build_kylas_links
  end

  private
  def build_kylas_links
    doc = Nokogiri::HTML(@body)
    doc.css("a").map do |link|
      if (href = link.attr("href"))
        link['href'] = convert_link href
      end
    end
    doc.to_html
  end

  def convert_link href
    href = ensure_link_protocol href
    url = CGI.escape href
    mapping_id = @tracked_links["#{url}"] || LinkMapping.create(email_id: @email_id, url: url, tenant_id: @tenant_id).id
    @tracked_links[url] = mapping_id
    "#{API_HOST}/v1/link_mappings/#{mapping_id}?url=#{url}"
  end

  def ensure_link_protocol link
    return link if link.include?('://')
    "http://#{link}"
  end
end
