class UpdateEmailTemplate
  prepend SimpleCommand
  include VariablesProcessor

  def initialize params
    @params = params.to_h
    @params[:attachments] ||= []
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @tenant_id = @auth_data.tenant_id
      @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result
      @email_template = EmailTemplate.find(@params[:id])

      unless has_update_permission_on_email_template
        Rails.logger.error 'Insufficient permission: Update email template'
        raise(ExceptionHandler::UpdateEmailTemplateNotAllowedError, ErrorCode.update_email_template_not_allowed)
      end

      validate_and_replace_variables(@params[:category])

      @params[:updated_by] = @user
      begin
        @email_template.assign_attributes(@params.except(:attachments))
        @email_template.save!
        attachments = get_existing_attachments_to_delete

        if attachments
          file_names = attachments.pluck(:file_name)
          attachments.destroy_all
          DeleteFileFromS3.call(file_names, S3_EMAIL_TEMPLATE_BUCKET)
        end

        new_attachments = get_attachments_to_create
        CreateTemplateAttachment.call(@email_template, new_attachments) if new_attachments.present?
        @email_template
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error e.message
        raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      end
    else
      Rails.logger.error "Unauthorised: User context missing in Updating Email Template"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end

  private

  def has_update_permission_on_email_template
    email_template_permission = @auth_data.permissions.find{ |p| p.name == 'email_template' }

    return true if email_template_permission&.action&.update_all && @email_template.tenant_id.to_s == @auth_data.tenant_id.to_s
    return true if email_template_permission&.action&.update && @email_template.created_by_id.to_s == @auth_data.user_id.to_s
    false
  end

  def get_existing_attachments_to_delete
    file_names = []
    # get existing attachments
    existing_attachments_ids_for_template = @email_template.template_attachments.pluck(:id)
    # get existing attachments availbale in update request
    existing_attachments_in_request = @params[:attachments].map{ |c| c['id'].to_i if c['id'].present? }.reject(&:nil?)

    # subtract new existing attachments from all existing attachments
    attachment_ids_for_delete = existing_attachments_ids_for_template - existing_attachments_in_request

    # Get all the resultants attachments
    @email_template.template_attachments.where(id: attachment_ids_for_delete)
  end

  def get_attachments_to_create
    @params[:attachments].select{ |c| c if c['id'].blank? }
  end
end
