require 'rest-client'
class ValidateDeals
  prepend SimpleCommand

  def initialize(deals = [])
    @deals = deals
  end

  def call
    return [] if @deals.nil? || @deals.empty?
    command = GetSecurityContext.call
    if command.success?
      verified_deals = deal_search_response
      verified_deals = verified_deals.select { |deal| deal.dig('recordActions', 'email') }
      @deals.each do |deal|
        v_deal = verified_deals.find { |v_d| deal.entity_id == v_d["id"] }
        if v_deal.present?
          deal.name = v_deal["name"]
          deal.owner_id = v_deal['ownerId']
        else
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      return @deals
    else
      raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
    end
  end

  private

  def deal_search_response
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    deal_ids_params = @deals.collect{|deal| deal.entity_id }.uniq.join(",")
    begin
      response = RestClient.post(
        SERVICE_SEARCH + '/v1/search/deal?page=0&size=1000&sort=updatedAt,desc',
        {
          fields: %w[id name ownedBy ownerId],
          jsonRule: {
            condition: 'AND',
            rules: [
              {
                operator: 'in',
                id: 'id',
                field: 'id',
                type: 'double',
                value: deal_ids_params
              }
            ],
            valid: true
          }
        }.to_json,
        {
          Authorization: "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )
      return JSON(response.body)['content'] unless response.nil?
      Rails.logger.error "ValidateDeal.deal_search_response invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "ValidateDeal.deal_search_response deal 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "ValidateDeal.deal_search_response deal 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "ValidateDeal.deal_search_response deal 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
