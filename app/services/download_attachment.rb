# frozen_string_literal: true

class DownloadAttachment
  prepend SimpleCommand
  include RecordActionsProcessor

  def initialize(email_id, attachment_id)
    @email_id = email_id
    @attachment_id = attachment_id
  end

  def call
    command = GetSecurityContext.call

    unless command.success?
      Rails.logger.error "Unauthorised: User context missing in DownloadAttachment"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end

    @auth_data = command.result
    @connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, user_id: @auth_data.user_id, active: true)

    email = Email.find_by(id: @email_id, tenant_id: @auth_data.tenant_id)

    if email.nil?
      Rails.logger.error "Email not found while downloading attachment. User id #{@auth_data.user_id} email id #{@email_id} attachment id #{@attachment_id}"
      raise(ExceptionHandler::NotFound, ErrorCode.not_found)
    end

    unless user_can_read?(email)
      Rails.logger.error "Unauthorised: User does not have access to email while downloading attachment. User id #{@auth_data.user_id} email id #{@email_id} attachment id #{@attachment_id}"
      raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
    end

    attachment = email.attachments.find_by(id: @attachment_id)
    if attachment.nil?
      Rails.logger.error "Attachment not found while downloading attachment. User id #{@auth_data.user_id} email id #{@email_id} attachment id #{@attachment_id}"
      raise(ExceptionHandler::NotFound, ErrorCode.not_found)
    end

    GetPresignedUrlFromS3.call(attachment, S3_EMAIL_BUCKET).result
  end
end
