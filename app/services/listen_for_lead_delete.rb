class ListenForLeadDelete
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(LEAD_EXCHANGE, LEAD_DELETED_EVENT, LEAD_DELETED_QUEUE) do |payload|
      Rails.logger.info "Received message for #{LEAD_DELETED_EVENT}"
      payload = JSON(payload)
      UpdateEmailForEntityDeleteEventJob.perform_later({ id: payload["id"],tenant_id:  payload["tenantId"], user_id: payload["userId"], entity_type: LOOKUP_LEAD, publish_usage: payload['publishUsage'] })
    end
  end
end
