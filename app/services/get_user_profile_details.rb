class GetUserProfileDetails
  prepend SimpleCommand

  def initialize(user_id, tenant_id, token = nil)
    @user_id = user_id
    @tenant_id = tenant_id
    @token = token
  end

  def call
    user = User.find_or_initialize_by(
      id: @user_id,
      tenant_id: @tenant_id
    )
    if user.new_record?
      user_profile = GetUserProfile.call(@user_id, @token).result&.with_indifferent_access
      Rails.logger.info "User profile: #{user_profile.inspect}"

      user.name = "#{user_profile[:firstName]} #{user_profile[:lastName]}"
    end

    Rails.logger.info "User built using profile: #{user.inspect}"
    user
  end
end

