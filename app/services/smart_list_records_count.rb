# frozen_string_literal: true

class SmartListRecordsCount
  prepend <PERSON>Command

  def initialize(smart_list_ids)
    @smart_list_ids = smart_list_ids
  end

  def call
    return [] if @smart_list_ids.blank?

    command = GetSecurityContext.call

    if command.success?
      auth_data = command.result

      smart_lists_details = FetchSmartListsDetails.call(@smart_list_ids)
      records_count_details = []

      smart_lists_details.result.each do |smart_list|
        email_result = FilterEmailsQuery.call(auth_data, smart_list['searchRequest'].slice('jsonRule').with_indifferent_access)

        filtered_emails_for_smart_list = email_result.result

        records_count_details << {
          'id' => smart_list['id'],
          'name' => smart_list['name'],
          'count' => filtered_emails_for_smart_list.total_entries
        }
      rescue StandardError => e
        Rails.logger.error "#{self.class} -> Error Response: #{e.message}"
        nil
      end
      records_count_details
    else
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end
end
