class ParseToken
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def initialize(token)
    @token = token
    @data = nil
  end

  def call
    begin
      body = JWT.decode(@token, nil, false)
      decoded_token = HashWithIndifferentAccess.new body[0]
        if decoded_token && decoded_token[:data]
          @data = decoded_token['data']
          create_auth_data
        else
          raise ExceptionHandler::Forbidden, ErrorCode.unauthorized
        end
    rescue JWT::DecodeError => e
      raise ExceptionHandler::Forbidden, ErrorCode.unauthorized
    end
  end

  private
    def create_auth_data
      auth_data = Auth::Data.new(@data)
      if auth_data.valid?
        auth_data
      else
        raise ExceptionHandler::InvalidToken, ErrorCode.unauthorized
      end
    end

end

