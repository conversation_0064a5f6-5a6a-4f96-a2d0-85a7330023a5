class RenewOutlookWebhook
  prepend SimpleCommand
  include GraphApiClient

  def initialize(connected_account)
    @connected_account = connected_account
  end

  def call
    begin
      command = GetConnectedAccountAccessToken.call(@connected_account, skip_auth: true)
      @token = command.result if command.success?

      subscription_id = @connected_account.subscription_id
      parsed_res = renew_webhook_subscription(subscription_id)
      subscription_id = parsed_res['id']
      expiry = Time.parse(parsed_res['expirationDateTime']).to_i
      @connected_account.update(subscription_id: subscription_id, subscription_expiry: expiry)
      @connected_account
    rescue RestClient::Unauthorized => e
      Rails.logger.error "Subscribe outlook - unauthorized: #{e.response.body}"
      raise(ExceptionHandler::OutlookAuthenticationError, ErrorCode.outlook_authentication_failed)
    rescue RestClient::NotFound
      Rails.logger.error "Could not find subscription with id: #{@connected_account.subscription_id}"
      Rails.logger.info "Creating new subscription for account: #{@connected_account.email}"
      connected_account = RegisterOutlookWebhook.call(@connected_account).result
    rescue RestClient::ExceptionWithResponse => e
      Rails.logger.info "Error while registering the outlook webhook #{e.response.body}"
      raise(ExceptionHandler::ThirdPartyAPIError, ErrorCode.third_party_api)
    end
  end
end
