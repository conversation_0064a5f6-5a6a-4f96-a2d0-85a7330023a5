# frozen_string_literal: true

require 'rest-client'
class SearchUsers
  prepend SimpleCommand

  def initialize(email_ids, tenant_id)
    @email_ids = email_ids
    @tenant_id = tenant_id
  end

  def call
    return [] if @email_ids.blank?
    response = search_users
    command = EntitySearchResultParser.call(@email_ids, response, LOOKUP_USER, @tenant_id)
    command.result
  end

  private

  def search_users
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    payload = { "emailIds": @email_ids }

    begin
      response = RestClient.post(
        "#{SERVICE_IAM}/v1/users/search-by-email",
        payload.to_json,
        {
          :Authorization => "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )
      return JSON(response.body) unless response.nil?

      Rails.logger.error "SearchUsers invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)

    rescue RestClient::NotFound
      Rails.logger.error "SearchUsers 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)

    rescue RestClient::InternalServerError
      Rails.logger.error "SearchUsers 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)

    rescue RestClient::BadRequest
      Rails.logger.error "SearchUsers 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
