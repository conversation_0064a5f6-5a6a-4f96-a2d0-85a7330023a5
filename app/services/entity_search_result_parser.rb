class EntitySearchResultParser
  prepend SimpleCommand

  def initialize(email_ids, data, entity_type, tenant_id, include_associated_deals_on_contact: false)
    @email_ids = email_ids
    @data = data
    @entity_type = entity_type
    @tenant_id = tenant_id
    @include_associated_deals_on_contact = include_associated_deals_on_contact
  end

  def call
    matched = []
    unmatched = []
    content = (@entity_type.eql? LOOKUP_USER) ? @data : @data['content']

    @email_ids.each do |email|
      content.each do |entity|
        emails = entity['emails'].presence || []

        # In case of users search results it comes in string type 'email' field
        email_values = [entity['emailId']] if emails.empty?
        next unless emails || email_values

        email_values = emails.map{|e| e['value']} unless email_values
        if email_values.include?(email)
          name = "#{entity['firstName']} #{entity['lastName']}"
          # Prepare matched
          entity_look_up_hash = { entity: "#{@entity_type}_#{entity['id']}", email: email, name: name, tenant_id: @tenant_id }
          if @include_associated_deals_on_contact
            entity_look_up_hash[:associated_deals] = entity['associatedDeals'].presence || []
          end
          if [LOOKUP_CONTACT, LOOKUP_LEAD].include?(@entity_type)
            entity_look_up_hash[:owner_id] = entity['ownerId']
          end
          matched << entity_look_up_hash
          @email_ids.delete(email)
        end
      end
    end

    # Prepare unmatched
    @email_ids.each{ |email| unmatched << { entity: LOOKUP_CUSTOM_EMAIL, email: email, tenant_id: @tenant_id } }
    return { matched: matched, unmatched: unmatched }
  end
end
