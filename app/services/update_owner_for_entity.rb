# frozen_string_literal: true

class UpdateOwnerForEntity
  prepend SimpleCommand

  def initialize(entity_type, data)
    @entity_type = entity_type
    @data = data
  end

  def call
    lookup = LookUp.where(tenant_id: @data[:tenant_id], entity: "#{@entity_type}_#{@data[:entity_id]}")

    if @data[:new_owner_id].present?

      if lookup.update_all(owner_id: @data[:new_owner_id]) > 0
        PublishEvent.call(
          Event::LookUpUpdated.new({
            entity_id: @data[:entity_id],
            entity_type: @entity_type,
            name: lookup.first.name,
            owner_id: @data[:new_owner_id],
            tenant_id: @data[:tenant_id]
          })
        )
      end
      Rails.logger.info "UpdateOwnerForEntity LookUp updated for entity id #{@data[:entity_id]} with entity type #{@entity_type}"
    end
  end
end
