# frozen_string_literal: true

class GetMeetingWithPicklistValues < GetEntityWithPicklistValues
  def initialize(meeting_id)
    super(meeting_id, LOOKUP_MEETING)
  end

  private

  def process_variables(data)
    data.merge!(data.delete('customFieldValues')) if data['customFieldValues'].present?
    user_lookup_fields = set_user_field_values(data)

    data = data.except(*MEETING_EXCLUDED_FIELDS)

    @entity_fields.select { |field| %w[LOOK_UP PICK_LIST].include?(field['type']) && field['entity'] == LOOKUP_MEETING }.each do |field|
      if data[field['internalName']].present?
        data[field['internalName']] = data.dig(field['internalName'], 'name')
      end
    end

    @entity_fields.select { |field| %w[DATETIME_PICKER DATE_PICKER].include?(field['type']) && field['entity'] == LOOKUP_MEETING }.each do |field|
      internal_name = field['internalName']
      if data[internal_name].present?
        data[internal_name] = field['type'] == 'DATETIME_PICKER' ? parse_date_time(data[internal_name]) : parse_date(data[internal_name])
      end
    end

    if data['checkedInDetails'].present?
      data['checkedInAt'] = parse_date_time(data.dig('checkedInDetails', 'at'))
      data['checkedInLatitude'] = data.dig('checkedInDetails', 'latitude')
      data['checkedInLongitude'] = data.dig('checkedInDetails', 'longitude')
    end

    data.delete('checkedInDetails')

    if data['checkedOutDetails'].present?
      data['checkedOutAt'] = parse_date_time(data.dig('checkedOutDetails', 'at'))
      data['checkedOutLatitude'] = data.dig('checkedOutDetails', 'latitude')
      data['checkedOutLongitude'] = data.dig('checkedOutDetails', 'longitude')
    end

    data.delete('checkedOutDetails')

    data['relatedTo'] = data['relatedTo'].map { |entity| entity['name'] }.join(', ') if data['relatedTo'].present?
    data['participants'] = data['participants'].map { |entity| entity['name'] }.join(', ') if data['participants'].present?
    data['organizer'] = data.dig('organizer', 'name')

    [data, user_lookup_fields]
  end

  def set_user_field_values(data)
    data.slice(*MEETING_USER_LOOKUP_FIELDS).inject({}) { |hash, (key, val)| hash.merge(key => val.try(:[], 'id')) }
  end
end
