# frozen_string_literal: true

class EmailDeleteEventPublisher
  prepend SimpleCommand

  def initialize(deleted_email_data, action)
    @action = action
    @deleted_email_data = deleted_email_data
  end

  def call
    Rails.logger.info "Email delete event publisher called"
    event = Event::EmailAction.new(delete_payload, @action)
    PublishEvent.call(event)
  end

  private

  def delete_payload
    {
      "entity" => nil,
      "oldEntity" => @deleted_email_data,
      "metadata" => {
        "tenantId" => @deleted_email_data['tenantId'],
        "userId" => @deleted_email_data['deletedBy']['id'],
        "entityId" => @deleted_email_data['id'],
        "entityType" => 'Email',
        "entityAction" => 'DELETED'
      }
    }
  end
end

