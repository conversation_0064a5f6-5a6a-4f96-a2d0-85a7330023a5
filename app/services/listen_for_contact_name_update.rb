class ListenForContactNameUpdate
  prepend SimpleCommand
  include ActiveModel::Validations

  def initialize
  end

  def call
    subscribe_contact_name_updated_event
  end

  private
    def subscribe_contact_name_updated_event
      RabbitmqConnection.subscribe(CONTACT_EXCHANGE, CONTACT_NAME_UPDATED_EVENT, CONTACT_NAME_UPDATED_QUEUE) do |payload|
        Rails.logger.info "Received message #{payload} for #{CONTACT_NAME_UPDATED_EVENT}"
        payload = JSON(payload)
        id = payload["contactId"]
        tenant_id = payload["tenantId"]
        name = "#{payload['firstName']} #{payload['lastName']}".strip
        look_ups = LookUp.where(entity: "#{LOOKUP_CONTACT}_#{id}", tenant_id:tenant_id)
        if look_ups.update_all(name: name) > 0
          PublishEvent.call(
            Event::LookUpUpdated.new({
              entity_id: id,
              entity_type: LOOKUP_CONTACT,
              name: name,
              owner_id: look_ups.first.owner_id,
              tenant_id: tenant_id
            })
          )
        end
      end
    end
end
