require 'google/apis/gmail_v1'
class CreateAttachment
  prepend SimpleCommand
  include ParamsProcessor

  def initialize(email, files, email_to_forward = nil, skip_auth = false)
    @email_record = email
    @files = files
    @email_to_forward = email_to_forward
    @skip_auth = skip_auth
    @email_with_details = nil
  end

  def call
    if @skip_auth
      @email_with_details = Email.where(tenant_id: @email_record.tenant_id, id: @email_to_forward)
    else
      command = GetSecurityContext.call
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid) unless command.success?

      auth_data = command.result
      @email_with_details = Email.where(tenant_id: auth_data.tenant_id, id: @email_to_forward)
    end

    is_tenant_email = @email_to_forward && @email_with_details.exists?
    @files.each do |file|
      file_name_with_extn = get_file_name(file)
      file_name = file_name_with_extn&.split('.')&.first
      file_ext = file_name_with_extn.split('.')&.last
      file_name = "tenant_#{@email_record.tenant_id}/user_#{@email_record.owner_id}/#{@email_record.id}_#{file_name}_#{DateTime.now.to_i}.#{file_ext}"
      file_size = file['data'].try(:size)
      if file['oldAttachment'].present? && file['oldAttachment'].is_a?(Attachment)
        CopyAttachmentsOnS3.call(file['oldAttachment'].file_name, file_name) if is_tenant_email
        File.delete(file['oldAttachment'].extract_file_name)
      elsif file['oldAttachment'].present? && file['oldAttachment'].is_a?(TemplateAttachment)
        file_path = file['data'].path
        UploadFileToS3.call(file_path, file_name, S3_EMAIL_BUCKET)
        #CopyAttachmentsOnS3.call(file['oldAttachment'].file_name, file_name, email_template: true)
      elsif file['data'].present?
        file_path = file['data'].path
        UploadFileToS3.call(file_path, file_name, S3_EMAIL_BUCKET)
      end

      @email_record.attachments.create(file_name: file_name, file_size: file_size.to_i, inline: file['type'].eql?('inline'), content_id: file['content_id'])
    end
  end
end
