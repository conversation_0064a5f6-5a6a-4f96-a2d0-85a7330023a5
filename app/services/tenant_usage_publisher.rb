require 'rest-client'

class TenantUsagePublisher
  prepend SimpleCommand

  def initialize(tenant_id = nil, publish_attachment_usage = false)
    @tenant_id = tenant_id
    @publish_attachment_usage = publish_attachment_usage
  end

  def call
    Rails.logger.info "Tenant Usage publisher called"
    email_data = Email.usage_per_tenant @tenant_id
    email_template_data = EmailTemplate.usage_per_tenant @tenant_id
    attachment_data = []
    if @publish_attachment_usage
      attachment_data = TemplateAttachment.usage_per_tenant(@tenant_id)
      attachment_data = attachment_data + Attachment.usage_per_tenant(@tenant_id) if @publish_attachment_usage
      attachment_data = calculate_attachment_storage_for_tenant(attachment_data)
    end
      
    data = email_data + email_template_data + attachment_data
    event = Event::TenantUsage.new(data)
    PublishEvent.call(event)
    Rails.logger.info "Event::TenantUsagePublisher data #{event.to_json}"
  end

  private

  def calculate_attachment_storage_for_tenant(data)
    result = data.group_by{|h| h[:tenantId]}
      .transform_values do |tenant_data|
        tenant_data.reduce({}) do |sums, usage|
          sums.merge(usage) do |key, a, b|
            key.to_s == 'count' ? a + b : a
          end
        end
      end
    result&.values
  end
end
