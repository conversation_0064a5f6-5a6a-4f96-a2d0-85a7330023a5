require 'rest-client'

class GetDealWithPicklistValues < GetEntityWithPicklistValues

  def initialize(deal_id)
    super(deal_id, LOOKUP_DEAL)
  end

  private

  def process_variables(data)
    data.merge!(data.delete('customFieldValues')) if data['customFieldValues'].present?
    user_lookup_fields = set_user_field_values(data)

    data = set_picklist_values(data)

    @entity_fields.select { |field| field['type'] == 'MULTI_PICKLIST' }.each do |field|
      data[field['internalName']] = data[field['internalName']]&.map { |value_hash| value_hash['name'] }&.join(', ')
    end

    @entity_fields.select { |field| ['LOOK_UP', 'PICK_LIST'].include?(field['type']) }.each do |field|
      if data[field['internalName']].present?
        data[field['internalName']] = data[field['internalName']].is_a?(Hash) ? data[field['internalName']]['name'] : data[field['internalName']].map { |f| f['name'] }.join(', ')
      end
    end

    @entity_fields.select { |field| ['DATETIME_PICKER', 'DATE_PICKER'].include?(field['type']) }.each do |field|
      internal_name = field['internalName']
      if data[internal_name].present?
        data[internal_name] = field['type'] == 'DATETIME_PICKER' ? parse_date_time(data[internal_name]) : parse_date(data[internal_name])
      end
    end

    data['pipelineStage'] = data.dig('pipeline', 'stage', 'name')
    data['pipeline'] = data.dig('pipeline', 'name')

    DEAL_EXCLUDED_FIELDS.each { |field| data.delete(field) }

    [data, user_lookup_fields]
  end

  def set_user_field_values(data)
    data.slice(*DEAL_USER_LOOKUP_FIELDS).inject({}) { |hash, (key, val)| hash.merge(key => val.try(:[], 'id')) }
  end
end
