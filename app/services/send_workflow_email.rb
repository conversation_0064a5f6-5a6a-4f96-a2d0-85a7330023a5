require 'google/apis/gmail_v1'
class SendWorkflowEmail
  prepend SimpleCommand

  attr_accessor :user_id, :tenant_id, :data, :sender_id, :email_template, :connected_account

  def initialize(user_id, tenant_id, sender_id, data)
    @user_id = user_id
    @tenant_id = tenant_id
    @sender_id = sender_id
    @data = data
  end

  def call
    fetch_template
    return unless @email_template
    @connected_account = fetch_connected_account
    return unless @connected_account

    add_token_in_current_thread
    template_data_command = GetEmailTemplateWithEntityDetails.call(@email_template.id, @data['relatedTo']['id'])
    return unless template_data_command.success?

    template_data = template_data_command.result
    template_body = template_data[:body]
    template_body.gsub!('<figure', '<div')
    template_body.gsub!('</figure', '</div')
    @data['body'] = template_body
    @data['subject'] = template_data[:subject]
    @data['emailTemplateAttachments'] = @email_template.template_attachments.map{|attachment| { id: attachment.id, fileName: attachment.extract_file_name(false)} }
  
    begin
      CreateEmail.call(@data.with_indifferent_access, validate_entities: false)
    rescue ExceptionHandler::InvalidDataError => e
      Rails.logger.error("Failed to create email: #{e.message}")
    end
  end

  private

  def add_token_in_current_thread
    thread = Thread.current
    user = @connected_account.user
    thread[:token] = GenerateToken.call(user.id, user.tenant_id, user.profile_id).result
    thread[:auth] = ParseToken.call(thread[:token]).result
    if user.profile_id.blank?
      profile_id = UserSettingsService.new.fetch[:profile_id]
      user.update!(profile_id: profile_id)
      thread[:token] = GenerateToken.call(user.id, user.tenant_id, user.reload.profile_id).result
      thread[:auth] = ParseToken.call(thread[:token]).result
    end
  end

  def fetch_template
    @email_template = EmailTemplate.find_by(id: @data['emailTemplateId'], tenant_id: @tenant_id, active: true)
  end

  def fetch_connected_account
    @connected_account = ConnectedAccount.find_by(active: true, user_id: sender_id)
  end
end
