# frozen_string_literal: true
require 'rest-client'

class GetTenant
  prepend SimpleCommand

  def initialize(_entity_id)
  end

  def call
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result

    begin
      response = RestClient.get(
        SERVICE_IAM + "/v1/tenants",
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?

      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "Get Tenant - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "Get Tenant - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "Get Tenant - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue StandardError
      Rails.logger.error "Get Tenant - invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
