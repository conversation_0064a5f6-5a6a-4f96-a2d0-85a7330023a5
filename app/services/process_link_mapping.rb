class ProcessLinkMapping
  prepend SimpleCommand

  def initialize id
    @id = id
  end

  def call
    link = load_link
    return if link.blank? || link.email.blank?

    old_serialized_email_data = EmailDetailsSerializer.call(link.email, false, nil, true, add_owner_id: true).result

    link.email_link_logs.create(email: link.email, tenant_id: link.tenant_id)

    email_with_link_logs = Email.includes( link_mappings: :email_link_logs ).find_by(id: link.email.id)

    if link.email_link_logs.count.eql? 1
      LinkClickedEventPublisher.call(link, link.email)
      PublishEvent.call(Event::EmailClicked.new(email_with_link_logs, old_serialized_email_data))
      Rails.logger.info "Email Clicked publisher called for Link #{link.url} Tenant ID #{link.email.tenant_id} Email ID: #{link.email.id}"
    end

    PublishEvent.call(Event::EmailUpdatedV2.new(email_with_link_logs, old_serialized_email_data))
    Rails.logger.info "Email Updated V2 publisher called (Email Link Clicked) for Link #{link.url} Tenant ID #{link.email.tenant_id} Email ID: #{link.email.id}"
  end

  private

  def load_link
    LinkMapping.includes(email: { link_mappings: [:email_link_logs] }).find_by(id: @id)
  end
end
