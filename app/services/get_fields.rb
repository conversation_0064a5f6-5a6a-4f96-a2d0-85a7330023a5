# frozen_string_literal: true

require 'rest-client'

class GetFields
  prepend SimpleCommand

  def call
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result

    begin
      response = RestClient.get(url, { Authorization: "Bearer #{token}" })
      body = JSON.parse(response.body)
      fields = body.inject([]) do |arr, f|
        arr << {
          'id' => f["id"],
          'displayName' => f['displayName'],
          'internalName' => f["name"],
          'standard' => f['standard'],
          'type' => f['type'],
          'entity' => entity
        }
      end

      return fields
    rescue RestClient::NotFound
      Rails.logger.error "Get #{entity.camelize} Fields - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "Get #{entity.camelize} Fields - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "Get #{entity.camelize} Fields - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue StandardError => e
      Rails.logger.error "Get #{entity.camelize} Fields - invalid response - #{e}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end

  class Lead < self

    private

    def url
      SERVICE_CONFIG + "/v1/entities/lead/fields?entityType=lead&custom-only=false&sort=createdAt,asc&page=0&size=100"
    end

    def entity
      LOOKUP_LEAD
    end
  end

  class Contact < self

    private

    def url
      SERVICE_CONFIG + "/v1/entities/contact/fields?entityType=contact&custom-only=false&sort=createdAt,asc&page=0&size=100"
    end

    def entity
      LOOKUP_CONTACT
    end
  end

  class Deal < self

    private

    def url
      SERVICE_DEAL + "/v1/deals/fields?custom-only=false&sort=createdAt,asc&page=0&size=100"
    end

    def entity
      LOOKUP_DEAL
    end
  end

  class CallLog < self

    private

    def url
      SERVICE_CALL + "/v1/call-logs/fields"
    end

    def entity
      LOOKUP_CALL
    end
  end

  class Meeting < self

    private

    def url
      SERVICE_MEETING + "/v1/meetings/fields"
    end

    def entity
      LOOKUP_MEETING
    end
  end

  class Task < self

    private

    def url
      "#{SERVICE_CONFIG}/v1/entities/task/fields?entityType=task&custom-only=false&sort=createdAt,asc&page=0&size=100"
    end

    def entity
      LOOKUP_TASK
    end
  end
end
