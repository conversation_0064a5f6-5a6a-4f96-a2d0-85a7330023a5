class CopyAttachmentsOnS3
  prepend SimpleCommand

  def initialize(file_to_copy, file_path, email_template: false)
    @file_path = file_path
    @file_to_copy = file_to_copy
    Aws.config.update(
      endpoint: ENV['AWS_ENDPOINT'],
      region: ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY_ID'], ENV['AWS_SECRET_ACCESS_KEY'])
    )
    s3 = Aws::S3::Resource.new
    bucket_name = email_template ? S3_EMAIL_TEMPLATE_BUCKET : S3_EMAIL_BUCKET
    @bucket = s3.bucket(bucket_name)
  end

  def call
    begin
      obj = @bucket.object(@file_to_copy)
      obj.copy_to(bucket: S3_EMAIL_BUCKET ,key: @file_path)
    rescue StandardError => e
      Rails.logger.error "Email Service | Copy file to s3: #{e.message} | #{@file_to_copy}"
    end
  end
end

