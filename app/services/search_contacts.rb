require 'rest-client'
class SearchContacts
  prepend SimpleCommand

  def initialize(email_ids, tenant_id)
    @email_ids = email_ids
    @tenant_id = tenant_id
  end

  def call
    return { matched: [], unmatched: [] } if @email_ids.blank?
    response = search_contacts
    if response['content'].present?
      response['content'] = response['content'].select { |contact| contact.dig('recordActions', 'email') }
    end
    command = EntitySearchResultParser.call(@email_ids, response, LOOKUP_CONTACT, @tenant_id, include_associated_deals_on_contact: true)
    command.result
  end

  private

  def search_contacts
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    payload = { fields: ["id", "firstName", "lastName", "emails", "associatedDeals", "ownerId"] }

    rules = [
      {
        "operator": "in",
        "id": "emails",
        "field": "emails",
        "type": "string",
        "value": @email_ids.join(',')
      }
    ]

    payload[:jsonRule] = { rules: rules, "condition": "OR", "valid": true }

    begin
      response = RestClient.post(
        SERVICE_SEARCH + "/v1/search/contact?sort=updatedAt,desc&page=0&size=100",
        payload.to_json,
        {
          :Authorization => "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "SearchContacts invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "SearchContacts 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "SearchContacts 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "SearchContacts 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
