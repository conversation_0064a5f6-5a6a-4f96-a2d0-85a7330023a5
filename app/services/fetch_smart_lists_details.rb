# frozen_string_literal: true

class FetchSmartListsDetails
  prepend SimpleCommand

  def initialize(smart_list_ids)
    @smart_list_ids = smart_list_ids
  end

  def call
    command = GetSecurityContext.call('token')

    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?

    token = command.result
    url = "#{SERVICE_SEARCH}/v1/search-lists/email?#{@smart_list_ids.map { |id| "id=#{id}" }.join('&')}"

    begin
      response = RestClient.get(url, { Authorization: "Bearer #{token}" })

      JSON.parse(response.body)
    rescue RestClient::NotFound
      Rails.logger.error "#{self.class} - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "#{self.class} - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "#{self.class} - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue StandardError => e
      Rails.logger.error "#{self.class} - Error response - #{e}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
