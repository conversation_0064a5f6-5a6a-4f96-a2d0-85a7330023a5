require 'rest-client'
class SearchLeads
  prepend SimpleCommand

  def initialize(email_ids, tenant_id)
    @email_ids = email_ids
    @tenant_id = tenant_id
  end

  def call
    return [] if @email_ids.blank?
    response = search_leads
    if response['content'].present?
      response['content'] = response['content'].select { |lead| lead.dig('recordActions', 'email') }
    end
    command = EntitySearchResultParser.call(@email_ids, response, LOOKUP_LEAD, @tenant_id)
    command.result
  end

  private

  def search_leads
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    payload = { fields: ["id", "firstName", "lastName", "emails", "ownerId"] }
    rules = [
      {
        "operator": "in",
        "id": "emails",
        "field": "emails",
        "type": "string",
        "value": @email_ids.join(',')
      }
    ]

    payload[:jsonRule] = { rules: rules, "condition": "OR", "valid": true }

    begin
      response = RestClient.post(
        SERVICE_SEARCH + "/v1/search/lead?sort=updatedAt,desc&page=0&size=100",
        payload.to_json,
        {
          :Authorization => "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "SearchLeads invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "SearchLeads 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "SearchLeads 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "SearchLeads 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
