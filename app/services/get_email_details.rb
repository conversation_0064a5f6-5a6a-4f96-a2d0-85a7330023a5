# frozen_string_literal: true

class GetEmailDetails
  prepend SimpleCommand

  def initialize(email_id)
    @email_id = email_id
  end

  def call
    command = GetSecurityContext.call

    unless command.success?
      Rails.logger.error "Unauthorised: Missing user context in GetEmailDetails"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end

    auth_data = command.result
    email = Email.includes( link_mappings: :email_link_logs ).find_by(id: @email_id, tenant_id: auth_data.tenant_id)

    unless email.present?
      Rails.logger.error "Email not found"
      raise(ExceptionHandler::NotFound, ErrorCode.not_found)
    end

    EmailDetailsSerializer.call(email, false, nil, true, send_body: true).result
  end
end
