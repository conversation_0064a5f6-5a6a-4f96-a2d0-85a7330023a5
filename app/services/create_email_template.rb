class CreateEmailTemplate
  prepend SimpleCommand
  include VariablesProcessor

  def initialize params
    @params = params
    @auth_data = nil
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      tenant_id = @auth_data.tenant_id
      user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result

      unless has_create_permission_on_email_template
        Rails.logger.error 'Insufficient permission: Create email template'
        raise(ExceptionHandler::CreateEmailTemplateNotAllowedError, ErrorCode.create_email_template_not_allowed)
      end

      validate_and_replace_variables(@params[:category])

      email_template = EmailTemplate.new(@params.except(:attachments))
      email_template.tenant_id = tenant_id
      begin
        ActiveRecord::Base.transaction do
          email_template.created_by = email_template.updated_by = user
          email_template.save!
          CreateTemplateAttachment.call(email_template, @params[:attachments])
        end
        email_template
      rescue ActiveRecord::RecordInvalid => e
        if email_template.errors[:name].first == "has already been taken"
          Rails.logger.error "Duplicate template name: #{@params[:name]}"
          raise(ExceptionHandler::DuplicateTemplateNameError, ErrorCode.duplicate_template)
        end

        Rails.logger.error "Create email template: #{e.message}"
        raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      end
    else
      Rails.logger.error 'Unauthorised: User context missing in CreateEmailTemplate'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end

  private

  def has_create_permission_on_email_template
    email_template_permission = @auth_data.permissions.find{ |p| p.name == 'email_template' }
    email_template_permission&.action&.write
  end
end
