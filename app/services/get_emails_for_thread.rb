class GetEmailsForThread
  prepend SimpleCommand
  include RecordActionsProcessor

  def initialize(params)
    @thread_id = params[:id]
    @page = params[:page]
    @size = params[:size]
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, user_id: @auth_data.user_id, active: true)

      thread = EmailThread.find_by(tenant_id: @auth_data.tenant_id, id: @thread_id)
      unless thread
        Rails.logger.info "No thread found with the id: #{@thread_id}"
        raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      end

      unless user_can_read_thread?(@thread_id)
        Rails.logger.error "Unauthorised: User does not have read on thread. User id #{@auth_data.user_id} Thread id #{@thread_id}"
        raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
      end

      emails = thread.emails.includes(:email_track_logs, link_mappings: :email_link_logs).order(created_at: :desc)
      emails = paginate(emails)

      EmbedInlineImagesInBody.call(emails).result
    else
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end

  private

  def paginate(emails)
    page = @page || 1
    size = @size || 10
    emails = emails.page(page.to_i).per_page(size.to_i)
  end
end
