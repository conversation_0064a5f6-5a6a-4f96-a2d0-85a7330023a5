require 'rest-client'
class SmtpMailSender
  attr_accessor :source_thread_id, :reply_to_email_id, :tenant_id
  prepend SimpleCommand
  include ParamsProcessor

  def initialize(params)
    @email_record = params[:email]
    @tenant_id = @email_record.tenant_id
    @connected_account = @email_record.connected_account
    @attachments = params[:attachments] || []
    @reply_to_email_id = params[:reply_to_email_id]
  end

  def call
    message = build_message
    Rails.logger.info "payload: #{message}"
    send_message(message, false)
  end

  private

  def send_message(message, retried = false)
    begin
      response = RestClient.post(
        SERVICE_EMAIL_ENGINE + "/v1/account/#{@connected_account.access_token}/submit",
        message.to_json,
        {
          content_type: :json,
          accept: :json
        }
      )
      res = JSON.parse(response.body)
      return OpenStruct.new({ id: res["messageId"], global_message_id: res["messageId"] }) unless response.nil?
      Rails.logger.error "EmailEngine: Send invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::Unauthorized
      Rails.logger.error "EmailEngine: send 401 : #{response.try(:body)}"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.smtp_unauthorized)
    rescue RestClient::NotFound => e
      Rails.logger.error "EmailEngine: send 404 : #{response.try(:body)} | #{@reply_to_email_id} | #{e.to_s}"
      send_message(message, true) unless retried
      raise(ExceptionHandler::EmailThreadNotFoundError, ErrorCode.email_thread_not_found)
    rescue RestClient::UnsupportedMediaType
      Rails.logger.error "EmailEngine: send 415 : #{response.try(:body)}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.unsupported_media_type)
    rescue RestClient::BadRequest => e
      Rails.logger.error "EmailEngine: send 400: #{response.try(:body)} | #{e.to_s}"
      send_message(message, true) unless retried
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::TooManyRequests
      Rails.logger.error "EmailEngine: send 429"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.rate_limit_error)
    rescue RestClient::InternalServerError => e
      Rails.logger.error "EmailEngine: send 500 | #{e.to_s}"
      send_message(message, true) unless retried
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::NotImplemented
      Rails.logger.error "EmailEngine: send 501"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.not_implemented)
    rescue RestClient::BadGateway
      Rails.logger.error "EmailEngine: send 502"
      send_message(message, true) unless retried
      raise(ExceptionHandler::InvalidDataError, ErrorCode.bad_gateway)
    rescue RestClient::ServiceUnavailable
      Rails.logger.error "EmailEngine: send 503"
      send_message(message, true) unless retried
      raise(ExceptionHandler::InvalidDataError, ErrorCode.service_unavailable)
    rescue RestClient::GatewayTimeout
      Rails.logger.error "EmailEngine: send 504"
      send_message(message, true) unless retried
      raise(ExceptionHandler::InvalidDataError, ErrorCode.gateway_timeout)
    end
  end

  def build_message
    message = {}
    message["from"] = { "address" => @email_record.from }
    message["to"] = @email_record.to.map{ |to| {"address" => to } }
    message['cc'] = @email_record.cc.map{ |cc| {"address" => cc } }
    message['bcc'] = @email_record.bcc.map{ |bcc| {"address" => bcc } }
    message['subject'] = @email_record.subject.to_s

    build_body message
  end

  def build_body message
    message["html"] = @email_record.body
    message["attachments"] = []
    if @reply_to_email_id
      message["reference"] = {}
      message["reference"]["message"] = @reply_to_email_id
    end
    @attachments.each do |a|
      attachment = {}
      content_type = a['data'].respond_to?(:content_type) ? a['data'].content_type : MIME::Types.type_for(a['fileName'])[0].content_type rescue 'multipart/form-data'

      attachment['contentType'] = content_type
      attachment["contentDisposition"] = a['type'] || 'attachment'
      attachment["encoding"]= "base64"
      attachment["content"] = Base64.encode64(File.read(a['data'].path)).gsub("\n","")
      attachment["filename"] = get_file_name(a)
      attachment['cid'] = a['content_id'] || "#{a['fileName']}_#{DateTime.now.to_i}"
      message["attachments"] << attachment
    end
    message
  end
end
