class RecipientsBuilder
  prepend SimpleCommand

  def initialize params
    @params = params
    @user = params[:user]
  end

  def call
    recipients = build_recipients
  end

  private

  def build_recipients
    recipients_data = @params[:recipients] || []
    recipients = recipients_data.collect do |pdata|
      pdata[:tenant_id] = @user.tenant_id
      look_up = GetLookUp.call(pdata).result
      look_up.name = pdata[:name] if pdata[:name]
      if look_up.owner_id.blank?
        look_up.owner_id = fetch_owner_from_details(look_up.entity_type, look_up.entity_id)
      end
      look_up.save! unless look_up.new_record?
      look_up
    end
    Rails.logger.info "build_recipients return #{recipients.to_json}"
    remove_duplicates(recipients)
  end

  def remove_duplicates recipients
    recipients.uniq { |participant| [participant.entity, participant.email] }
  end

  def fetch_owner_from_details(entity_type, entity_id)
    case entity_type
    when LOOKUP_LEAD, LOOKUP_CONTACT
      "Get#{entity_type.classify}".constantize.call(entity_id).result['ownerId']
    when LOOKUP_DEAL
      GetDeal.call(entity_id).result.dig('ownedBy', 'id')
    else
      nil
    end
  end
end
