class DeactivateConnectedAccount
  prepend SimpleCommand
  include ActiveModel::Validations

  attr_accessor :tenant_id, :user_id, :provider, :disconnected_by, :account_type, :user_email, :tenant_email

  def initialize(tenant_id, user_id, provider, disconnected_by, account_type, user_email, tenant_email)
    @tenant_id = tenant_id
    @user_id = user_id
    @provider = provider
    @disconnected_by = disconnected_by
    @account_type = account_type
    @user_email = user_email
    @tenant_email = tenant_email
  end

  def call
    connected_email = ConnectedAccount.find_by(user_id: @user_id, provider_name: provider, active: true).try(:email)
    deactivated = ConnectedAccount.where(user_id: @user_id, provider_name: provider, active: true).update_all(active: false)
    if deactivated.positive? && [AUTO_DISCONNECTED, UNAUTHORIZATION_DISCONNECT, LIMIT_EXCEEDED_DISCONNECT].include?(disconnected_by)
      ConnectedAccountDeactivatedPublisher.call(tenant_id, user_id, disconnected_by, account_type, user_email, tenant_email, connected_email)
    end
  end
end
