class UpdateEmailForEntityDeleteEvent
  prepend SimpleCommand

  def initialize(entity_id, tenant_id, user_id, entity_type, publish_usage = true)
    @tenant_id   = tenant_id
    @entity_id   = entity_id
    @entity_type = entity_type
    @user_id = user_id
    @publish_usage = publish_usage
  end

  def call
    Rails.logger.info "UpdateEmailForEntityDeleteEvent Tenant id #{@tenant_id} Entity Type #{@entity_type} #{@entity_id}"
    look_ups = LookUp.where(tenant_id: @tenant_id, entity:"#{@entity_type}_#{@entity_id}")
    Rails.logger.info "UpdateEmailForEntityDeleteEvent Lookups count #{look_ups.size} | Entity type: #{@entity_type} | Entity ID: #{@entity_id}"
    return unless look_ups.present?

    look_ups.each do |lookup|
      email_look_ups = lookup.email_look_ups
      Rails.logger.info "UpdateEmailForEntityDeleteEvent EmailLookups count #{email_look_ups.size} | Entity type: #{@entity_type} | Entity ID: #{@entity_id}"
      if email_look_ups.size.eql?(0)
        # EmailLookUp.unscoped.where(tenant_id: lookup.tenant_id, look_up_id: lookup.id).update_all(deleted: true)
        lookup.update_column(:deleted, true)
      end

      email_look_ups.includes(:email).find_in_batches do |email_lookup_batch|
        email_lookup_batch.each do |email_lookup|
          next if !email_lookup.persisted? || email_lookup.email.blank?
          delete_email_if_only_lookup_is_associated(email_lookup) unless lookup.entity_type.eql?(LOOKUP_DEAL)
        end
      end
      email_look_ups = lookup.email_look_ups.reload
      if email_look_ups.present?
        lookup.update(entity: LOOKUP_CUSTOM_EMAIL, entity_id: nil, entity_type: LOOKUP_CUSTOM_EMAIL)
      else
        # EmailLookUp.unscoped.where(tenant_id: lookup.tenant_id, look_up_id: lookup.id).update_all(deleted: true)
        lookup.update_column(:deleted, true)
      end
    end
    PublishUsageJob.perform_later(@tenant_id) if @publish_usage
  end

  private

  def delete_email_if_only_lookup_is_associated(email_lookup)
    return if email_lookup.deleted?
    email = email_lookup.email
    return if email.blank? || email.deleted?
    return unless email.persisted?
    look_ups = EmailLookUp.joins(:look_up).where.not(look_up_id: email_lookup.look_up_id).
      where(email_id: email_lookup.email.id).where.not("entity_type=? or entity_type = ?", LOOKUP_CUSTOM_EMAIL, LOOKUP_USER)

    if look_ups.count.eql? 0
      deleted_serialized_email = EmailDetailsSerializer.call(email, false, FetchUser.call(@user_id, @tenant_id).result, false, true, add_owner_id: true).result

      email.attachments.update_all(deleted: true) if email.attachments.present?
      EmailLookUp.unscoped.where(tenant_id: email.tenant_id, email_id: email.id).update_all(deleted: true)
      email.update_column(:deleted, true)

      PublishEvent.call(Event::EmailDeletedWorkflowV2.new(deleted_serialized_email))
      Rails.logger.info "Email Deleted Workflow V2 publisher called Email ID #{deleted_serialized_email['id']} Tenant ID #{deleted_serialized_email['tenantId']}"
      deleted_serialized_email.delete('emailsMatchingGlobalMessageId')
      EmailDeleteEventPublisher.call(deleted_serialized_email, DELETE_ACTION)
    end
  end
end
