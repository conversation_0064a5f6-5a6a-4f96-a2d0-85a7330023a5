class GenerateToken
  prepend SimpleCommand

  def initialize(user_id, tenant_id, profile_id = nil)
    @user_id = user_id
    @tenant_id = tenant_id
    @profile_id = profile_id
  end

  def call
    payload = {
      data:
      {
        accessToken: "1c77c32f-5f5b-4342-91b2-961e38045498",
        expiresIn: 3600,
        expiry: (DateTime.now  + 1.hour).to_i,
        userId: @user_id,
        tenantId: @tenant_id,
        tokenType: 'Bearer',
        permissions: [
          {
            name: "lead",
            "description": "has access to lead resource",
            "limits": -1,
            "units": "count",
            "action": {
              "read": true,
              "readAll": true,
              "task": true,
              "email": true
            }
          },
          {
            name: "deal",
            "description": "has access to deal resource",
            "limits": -1,
            "units": "count",
            "action": {
              "read": true,
              "readAll": true,
              "task": true,
              "email": true
            }
          },
          {
            name: "user",
            "description": "has access to user resource",
            "limits": -1,
            "units": "count",
            "action": {
              "read": true,
              "readAll": true,
            }
          },
          {
            name: "contact",
            "description": "has access to user resource",
            "limits": -1,
            "units": "count",
            "action": {
              "read": true,
              "readAll": true,
              "task": true,
              "email": true
            }
          },
          {
            name: "company",
            "description": "has access to company resource",
            "limits": -1,
            "units": "count",
            "action": {
              "read": true,
              "readAll": true,
              "task": true
            }
          },
          {
            "name": "email_template",
            "description": "has access to user resource",
            "limits": -1,
            "units": "count",
            "action": {
              "read": true,
              "readAll": true,
            }
          },
          {
            name: "call",
            "description": "has access to call resource",
            "limits": -1,
            "units": "count",
            "action": {
              "read": true,
              "readAll": true,
            }
          },
          {
            name: "meeting",
            "description": "has access to meeting resource",
            "limits": -1,
            "units": "count",
            "action": {
              "read": true,
              "readAll": true,
            }
          }
        ]
      }
    }

    if @profile_id.present?
      payload[:data]["meta"] = {}
      payload[:data]["meta"]["pid"] = @profile_id
    end

    token = JWT.encode payload, JWT_KEY, algorithm='HS256', header_fields={}
    token
  end
end
