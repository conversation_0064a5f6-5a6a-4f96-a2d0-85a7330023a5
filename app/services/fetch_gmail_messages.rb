require 'google/apis/gmail_v1'
class FetchGmailMessages
  prepend SimpleCommand
  include MessageProcessor

  def initialize(message_ids, connected_account, history_id)
    @message_ids = message_ids
    @connected_account = connected_account
    @history_id = history_id
    @gmail = Google::Apis::GmailV1::GmailService.new
    @attachments = []
  end

  def call
    add_token_in_current_thread
    command = GetConnectedAccountAccessToken.call(@connected_account, skip_auth: true)
    if command.success?
      begin
        @gmail.authorization = Signet::OAuth2::Client.new(access_token: command.result)
        @message_ids.each do |id|
          @attachments = []
          next if Email.where(source_id: id).exists?
          begin
            message = @gmail.get_user_message('me', id)
            save_message message
          rescue => e
            Rails.logger.error "Error: #{e.message}"
            next
          end
        end
        @connected_account.update(last_history_id: @history_id)
      end
    end
  end

  private

  def save_message message
    return unless message
    return if Email.where(source_id: message.id).exists?
    return if(message.label_ids.include?('DRAFT') || message.label_ids.include?('CHAT'))

    begin
      BounceProcessor.call(message, @connected_account)
    rescue => e
      Rails.logger.info "Bounce Detection Error #{e.message}\n#{e.backtrace.join("\n")}"
    end

    payload = message.payload
    email = Email.new
    headers = payload.headers
    from = headers.select{|a| a.name.downcase.eql? 'from' }&.as_json&.last&.[]("value")
    from = from.include?('<') ? from[/.*<([^>]*)/,1] : from
    email.from = from
    email.cc = parse_recepient(headers, 'cc')
    email.bcc = parse_recepient(headers, 'bcc')
    email.to = parse_recepient(headers, 'to')
    email.status = Email.statuses['sent'] if @connected_account.email == email.from

    emails = (email.cc.to_a + email.bcc.to_a + email.to.to_a + [email.from])
    entities = associated_entities(emails, message, from_email_address: from)

    if((entities[:email_thread].present?) || (entities[:related_look_ups].present? && entities[:related_look_ups][:matched].present?))
      from_lookup = entities[:related_look_ups][:matched].find{|l| l[:email] == email.from}
      #return unless from_lookup || entities[:email_thread].present?

      targeted_entities = entities[:related_look_ups][:matched].find{|l| [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].include? l[:entity]&.split('_')&.first}
      return unless targeted_entities || entities[:email_thread].present?

      unless from_lookup
        from_lookup = entities[:related_look_ups][:unmatched].find{|l| l[:email] == email.from}
      end
      email.subject = headers.select{|a| a.name.eql? 'Subject' }.as_json.last['value']
      email.global_message_id = headers.select{|a| a.name.upcase.eql? 'MESSAGE-ID' }.as_json.last['value']
      email.source_id = message.id
      email.owner = @connected_account.user
      body = parse_email_body(message.payload)
      email.body = body.to_s
      email.connected_account = @connected_account
      lookup = LookUp.find_or_initialize_by(from_lookup)
      lookup.save!
      email.sender = lookup
      email.tenant_id = @connected_account.user.tenant_id
      email_thread = entities[:email_thread] || create_email_thread(message)
      email.email_thread_id = email_thread.id
      if Email.where(source_id: message.id).exists?
        Rails.logger.info "Email save log: Duplicate message with id: #{message.id} "
      else
        begin
          email.body = email.body.force_encoding('UTF-8')
          email.save!
        rescue StandardError => e
          Rails.logger.error "Error while saving email #{e.message} Tenant id #{@connected_account.user.tenant_id} Connected Account id #{@connected_account.id} Email body encoding #{body.encoding}"
          Rails.logger.error "Error while saving email email body #{body.inspect}"
          raise ExceptionHandler::InvalidDataError, ErrorCode.invalid
        end
        email.related_to << lookup
        save_related_entities(email, entities, email_thread.emails.first&.related_to)
        SaveAttachmentsFromGmail.call({gmail: @gmail, email: email, attachments: @attachments})
        email.update_column(:external_attachment_count, Attachment.where(email_id: email.id, inline: false).count)
        already_triggered = Email.where(tenant_id: email.tenant_id, global_message_id: email.global_message_id, is_trigerred: true).exists?
        unless already_triggered && email.global_message_id.present?
          EmailReceivedRelatedToEntityPublisher.call(email, false)
          email.is_trigerred = true
          email.save!
        end
        email_thread=  email.reload.email_thread
        contact_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_CONTACT).pluck(:entity_id) + email_thread.contact_ids.to_a).uniq
        lead_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_LEAD).pluck(:entity_id) + email_thread.lead_ids.to_a).uniq
        user_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_USER).pluck(:entity_id) + email_thread.user_ids.to_a).uniq
        deal_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_DEAL).pluck(:entity_id) + email_thread.deal_ids.to_a).uniq
        email_thread.update(lead_ids: lead_ids, user_ids: user_ids, deal_ids: deal_ids, contact_ids: contact_ids)

        EmailCreateEventPublisher.call(email.id, CREATE_ACTION)
        EmailCreatedWorkflowV2EventPublisher.call(email)
      end
    end
  end

  def parse_email_body(message_part)
    body = ''
    return '' unless message_part

    body = message_part&.body&.data if message_part.mime_type == 'text/html'
    if message_part&.parts&.any?
      message_part.parts.map do |part|
        if part.filename == ''
          body = parse_email_body(part)
        else
          value = part.headers.select{ |header| header.name.downcase == 'Content-Disposition'.downcase }[0]&.value
          content_id = part.headers.select{ |header| header.name.downcase == 'Content-ID'.downcase }[0]&.value
          @attachments << {
            id: part.body.attachment_id,
            name: part.filename,
            type: value ? value.split(';').first : nil,
            content_id: content_id.match(/[\w\.@-]+/).to_a.first
          }
        end
      end
    end

    body
  end

  def create_email_thread message
    EmailThread.create(tenant_id: @connected_account.tenant_id, owner_id: @connected_account.user_id, source_thread_id: message.thread_id)
  end

  def get_email_thread message
    thread = EmailThread.where(source_thread_id: message.thread_id).last
    thread
  end

  def save_related_to email
    return unless EmailLookUp.where(email_id: email.id).exists?

    email.related_to << email.email_thread.emails.order(:created_at).first.related_to if email.email_thread.emails.exists?
  end

  def parse_recepient(headers, param)
    headers.select{|a| a.name.downcase.eql? param }&.as_json&.last&.[]("value")&.split(",")&.map{|a| a.include?('<') ? a[/.*<([^>]*)/,1] : a}
  end
end
