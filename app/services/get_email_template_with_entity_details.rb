class GetEmailTemplateWithEntityDetails
  prepend SimpleCommand

  def initialize(template_id, entity_id)
    @template_id = template_id
    @entity_id = entity_id
    @url_fields = {}
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result

      @email_template = EmailTemplate.find(@template_id)
      unless has_read_permission_on_email_template
        Rails.logger.error 'Insufficient permission: Read email template'
        raise(ExceptionHandler::ReadEmailTemplateNotAllowedError,ErrorCode.read_email_template_not_allowed)
      end

      fields = V2::GetVariables.call(@email_template.category).result
      fields.each do |f|
        (@url_fields[f['entity']] ||= [] ) <<  f['internalName'] if f['type'].eql? 'URL'
      end
      fetch_and_replace_variables
    else
      Rails.logger.error 'Unauthorised: User context missing in GetEmailTemplateWithEntityDetails'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end

  private

  def has_read_permission_on_email_template
    return true if @auth_data.can_access?('email_template', 'read_all') && @email_template.tenant_id.eql?(@auth_data.tenant_id.to_i)
    return true if @auth_data.can_access?('email_template', 'read') && @email_template.created_by_id.eql?(@auth_data.user_id)
    false
  end

  def fetch_and_replace_variables
    @body = @email_template.body
    @subject = @email_template.subject

    if all_variables.present?
      entity_details = {}.with_indifferent_access
      entity_details[@email_template.category], user_field_ids = "Get#{@email_template.category.camelize}WithPicklistValues".constantize.call(@entity_id).result
      if any_variables_present?(all_variables, 'tenant')
        entity_details['tenant'] = GetTenantWithPicklistValues.call(@email_template.category).result
      end
      user_variables = "#{@email_template.category.upcase}_USER_LOOKUP_FIELDS".constantize.select { |field| any_variables_present?(all_variables, field) }
      if user_variables.present?
        entity_details.merge!(GetUsersWithPicklistValues.call(user_field_ids.slice(*user_variables)).result)
      end
      data = replace_variables(entity_details)
    else
      data = { body: @body, subject: @subject }
    end
    data[:email_template] = @email_template
    data
  end

  def replace_variables(entity_details)
    call_recording_var_present = all_variables.uniq.detect { |var| var.include?('callRecording') }
    if @email_template.category.eql?(LOOKUP_CALL) && call_recording_var_present
      if entity_details.dig(LOOKUP_CALL, 'callRecording', 'id').present?
        begin
          call_recording_url_response = GetCallRecordingUrl.call(@entity_id, entity_details.dig(LOOKUP_CALL, 'callRecording', 'id'))
          if call_recording_url_response.success?
            entity_details[LOOKUP_CALL]['callRecording'] = call_recording_url_response.result['url']
          end
        rescue
          entity_details[LOOKUP_CALL]['callRecording'] = I18n.t('call_recording_url_generation_failed')
        end
      else
        entity_details[LOOKUP_CALL]['callRecording'] = I18n.t('call_recording_unavailable')
      end
    end

    variables_in_body.each do |variable|
      @body = validate_and_replace_normal_variable(@body, entity_details, variable)
    end

    conditional_variables_in_body.each do |variable|
      @body = validate_and_replace_conditional_variable(@body, entity_details, variable)
    end

    variables_in_subject.each do |variable|
      @subject = validate_and_replace_normal_variable(@subject, entity_details, variable)
    end

    conditional_variables_in_subject.each do |variable|
      @subject = validate_and_replace_conditional_variable(@subject, entity_details, variable)
    end

    { body: @body, subject: @subject }
  end

  def all_variables
    variables_in_body + conditional_variables_in_body + variables_in_subject + conditional_variables_in_subject
  end

  def variables_in_subject
    @variables_in_subject ||= @subject.scan(/{{[\s|\w|\.]+}}/).map(&:strip).uniq
  end

  def conditional_variables_in_subject
    @conditional_variables_in_subject ||= @subject.scan(/({{[\s|\w|\.]+}, {if missing: [^{}]*[\s|\w]*}})/).flatten.map(&:strip).uniq
  end

  def variables_in_body
    @variables_in_body ||= @body.scan(/{{[\s|\w|\.]+}}/).map(&:strip).uniq
  end

  def conditional_variables_in_body
    @conditional_variables_in_body ||= @body.scan(/({{[\s|\w|\.]+}, {if missing: [^{}]*[\s|\w]*}})/).flatten.map(&:strip).uniq
  end

  def validate_and_replace_normal_variable(text, entity_details, variable_in_text)
    variable = variable_in_text.gsub('{{','').gsub('}}','')
    Rails.logger.info "============ #{variable}"
    parsed_variable = Nokogiri::HTML.parse(variable).text
    entity_type, variable = get_entity_type_and_variable(parsed_variable)
    data = entity_details.dig(entity_type, variable)
    data = get_field_data data, entity_type, variable
    text = text.gsub(variable_in_text, data.to_s)
    text
  end

  def validate_and_replace_conditional_variable(text, entity_details, variable_in_text)
    actual_variable = variable_in_text.scan(/{{[\s|\w|\.]*},/).first.gsub('{{','').gsub('},','')
    Rails.logger.info "==========#{actual_variable}"
    parsed_variable = Nokogiri::HTML.parse(actual_variable).text
    entity_type, variable = get_entity_type_and_variable(parsed_variable)
    data = entity_details.dig(entity_type, variable)
    if data.blank?
      data = variable_in_text.split('}, {if missing: ').last.gsub('}}','')
    end
    data = get_field_data data, entity_type, variable
    text = text.gsub(variable_in_text, data.to_s)
  end

  def any_variables_present?(variables_array, entity)
    variables_array.any? { |variable| variable.scan(/{{[\s|\w|\.]*}/).first.include?("#{entity}.") }
  end

  def get_field_data data, entity_type, variable
    return data unless @url_fields[entity_type]
    return data unless @url_fields[entity_type].include? variable
    "<a href = '#{data}'> #{data} </a>"
  end

  def get_entity_type_and_variable(variable)
    if variable.include?('.')
      variable.split('.')
    else
      [@email_template.category, variable]
    end
  end
end
