class OutlookMailSender
  prepend SimpleCommand
  include GraphApiClient
  include ParamsProcessor

  MAX_CHUNK = 3 * 1024 * 1024 # 3 MB in bytes 3145728

  def initialize(params, token)
    @params = params
    @email_record = params[:email]
    @attachments = params[:attachments] || []
    @connected_account = @email_record.connected_account
    @reply_to_email_id = params[:reply_to_email_id]
    @token = token
  end

  def call
    begin
      res = create_message_draft(message)
      if [200, 201].include?(res.code)
        msg = JSON.parse(res.body)
        create_attachments_for_message(msg['id']) if @attachments.present?
        send_draft(msg['id'])
        return OpenStruct.new({ id: msg['id'], thread_id: msg['conversationId'], global_message_id: msg['internetMessageId'] })
      end
    rescue RestClient::Unauthorized => e
      Rails.logger.error "OutlookMailSender Error: #{e.response.body}"
      raise(ExceptionHandler::OutlookAuthenticationError, ErrorCode.outlook_authentication_failed)
    rescue RestClient::TooManyRequests
      Rails.logger.error "OutlookMailSender Too Many Requests 429"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.rate_limit_error)
    rescue RestClient::ExceptionWithResponse => e
      Rails.logger.error "OutlookMailSender #{e.response.body}"
      raise(ExceptionHandler::ThirdPartyAPIError, ErrorCode.third_party_api)
    end
  end

  private

  def message
    message = {}
    message[:subject] = @email_record.subject.to_s
    message[:body] = { contentType: 'HTML', content: @email_record.body.to_s }
    message[:toRecipients] = get_recipients(@email_record.to)
    message[:ccRecipients] = get_recipients(@email_record.cc)
    message[:bccRecipients] = get_recipients(@email_record.bcc)
    message["from"] = get_from_recipient if @email_record.connected_account.display_name
    message["sender"] = get_from_recipient if @email_record.connected_account.display_name
    message
  end

  def get_from_recipient
    {
      "emailAddress": { 
        "address": @email_record.from,
        "name": @email_record.connected_account.display_name
      }
    }
  end

  def get_recipients(emails)
    recipients = []
    emails.each do |email|
      recipients << { emailAddress: { address: email } }
    end
    recipients
  end

  def create_message_draft(draft)
    if @reply_to_email_id.present?
      res = create_reply_draft(@reply_to_email_id)
      msg = JSON.parse(res.body)
      update_message_draft(msg['id'], draft)
    else
      create_draft(draft)
    end
  end

  def create_attachments_for_message(message_id)
    @attachments.each do |a|
      next unless a['data'].present?

      file_name = get_file_name(a)
      file_size = a['data'].size
      if file_size > MAX_CHUNK
        upload_url = create_attachment_upload_session(message_id, file_name, file_size, content_id: a['content_id'])
        upload_attachment(upload_url, a['data'].path, file_size)
      else
        file_data = Base64.encode64("#{File.read(a['data'].path)}")
        upload_small_attachment(message_id, file_name, file_data, content_id: a['content_id'])
      end
    end
  end

  def upload_attachment(upload_url, path, file_size)
    file = File.open(path)

    chunk_start_pos = 0
    chunk_end_pos = MAX_CHUNK - 1

    until file.eof?
      data = file.read(MAX_CHUNK)
      begin
        upload_chunk(upload_url, data, file_size, chunk_start_pos, chunk_end_pos)
      rescue RestClient::ExceptionWithResponse => e
        Rails.logger.error "Failed to create attachment:: #{e.response.body}"
        raise(ExceptionHandler::ThirdPartyAPIError, ErrorCode.third_party_api)
      end
      chunk_start_pos += MAX_CHUNK
      chunk_end_pos += MAX_CHUNK

      chunk_end_pos = file_size - 1 if chunk_end_pos >= file_size
      break if chunk_start_pos >= file_size
    end
  end
end
