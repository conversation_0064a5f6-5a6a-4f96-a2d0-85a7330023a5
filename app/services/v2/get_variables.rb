# frozen_string_literal: true

class V2::GetVariables
  prepend SimpleCommand

  def initialize category
    @category = category
  end

  def call
    command = GetSecurityContext.call

    if command.success?
      if [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL, LOOKUP_CALL, LO<PERSON>UP_MEETING, LOOKUP_TASK].include?(@category)
        command = "GetFields::#{@category.camelize}".constantize.call
        if command.success?
          @entity_fields = command.result
          return (cross_entity_variables + allowed_entity_variables)
        end
      else
        Rails.logger.error "V2::GetVariables Invalid category #{@category}"
        raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      end
    else
      Rails.logger.error 'Unauthorised: User context missing in V2::GetVariables'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end

  private

  def allowed_entity_variables
    excluded_fields = "#{@category.upcase}_EXCLUDED_FIELDS".constantize
    modify_variables(
      @entity_fields.reject { |field| excluded_fields.include?(field['internalName']) || field['type'] == 'CHECKBOX' },
      EntityLabels.call(@category).result[:displayName]
    )
  end

  def cross_entity_variables
    tenant_variables + user_variables
  end

  def tenant_variables
    JSON.parse(File.read("#{Rails.root}/config/tenant-variables.json"))
  end

  def user_variables
    "#{@category.upcase}_USER_LOOKUP_FIELDS".constantize.inject([]) do |arr, user_field|
      variables = JSON.parse(File.read("#{Rails.root}/config/user-variables.json"))
      entity_label = @entity_fields.find { |f| f['internalName'] == user_field }['displayName']
      arr += modify_variables(variables, entity_label, user_field)
      arr
    end
  end

  def modify_variables(fields, entity_label, entity = nil)
    fields.map do |hash|
      hash['displayName'] = "#{entity_label} - #{hash['displayName']}"
      hash['entity'] = entity if entity.present?
      hash['groupBy'] = entity_label

      hash
    end
  end
end
