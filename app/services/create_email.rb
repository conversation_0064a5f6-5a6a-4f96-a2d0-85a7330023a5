class CreateEmail
  prepend SimpleCommand
  attr_accessor :auth_data, :tenant_id, :user, :params, :validated_look_ups, :reply_to_email, :validate_entities

  def initialize(params, validate_entities: true)
    @params = params
    @validate_entities = validate_entities
  end

  def call
    command = GetSecurityContext.call
    if command.success?
      @auth_data = command.result
      @tenant_id = @auth_data.tenant_id
      @user = GetUserDetails.call(@auth_data.user_id, @auth_data.tenant_id).result
      account = @user.connected_accounts.where(active: true).last
      raise(ExceptionHandler::NotConnectedError, ErrorCode.not_connected) if account.blank?
      user_look_up = GetLookUp.call({ id: @user.id, entity: LOOKUP_USER, email: account.email, tenant_id: @auth_data.tenant_id, name: @user.name }).result

      if @params[:thread_id].present? && @params[:replyTo].present?
        @reply_to_email = Email.where(tenant_id: @tenant_id, email_thread_id: @params[:thread_id], id: @params[:replyTo]).first
        is_reply_allowed = @reply_to_email.present? && @reply_to_email.attributes.values_at('to', 'cc', 'bcc', 'from').flatten.include?(account.email)
        raise(ExceptionHandler::InvalidDataError, ErrorCode.reply_not_allowed) unless is_reply_allowed
      end

      email = Email.new(body: @params[:body], subject: get_subject , sender: user_look_up, tenant_id: @tenant_id)
      begin
        @params[:to] ||= []
        @params[:cc] ||= []
        @params[:bcc] ||= []
        if @validate_entities
          @validated_look_ups = RecipientsValidator.call({ user: @user, recipients: look_ups_to_validate }).result
        else
          @validated_look_ups =  RecipientsBuilder.call({ user: @user, recipients: look_ups_to_validate }).result
        end
        email.to = @params[:to].collect{ |to| to[:email]}
        email.cc = @params[:cc].collect{ |cc| cc[:email]}
        email.bcc = @params[:bcc].collect{ |bcc| bcc[:email]}
        email.owner = @user
        email.connected_account = account
        email.from = account.email
        email.body, inline_attachments = replace_inline_base_64_data_with_attachments(body: email.body)
        attachments = download_existing_attachments + email_template_attachments
        email.external_attachment_count = attachments.count
        attachments += inline_attachments
        email.read_by << user_look_up.entity_id
        email.status = Email.statuses['draft']
        email.metadata = {
          executedWorkflows: @params[:metadata]&.dig('executedWorkflows') || []
        }
        email.campaign_info = @params[:campaignInfo] if @params[:campaignInfo].present?

        ActiveRecord::Base.transaction do
          email.save!
          if(([true, 'true'].include? @params[:trackingEnabled]) && account.tenant.tracking_enabled)
            res = CreateTrackMapping.call(email.body, email.id, email.tenant_id).result
            email.body = res[:body]
            res = CreateLinkMapping.call(email.body, email.id, email.tenant_id).result
            email.body = res
            email.tracking_enabled = @params[:trackingEnabled]
          end
          email.to_recipients = get_recipient_lookups(:to)
          email.cc_recipients = get_recipient_lookups(:cc)
          email.bcc_recipients = get_recipient_lookups(:bcc)

          send_email_params = { email: email, thread_id: @params[:thread_id], reply_to_email_id: @reply_to_email.try(:source_id), attachments: attachments }
          command = SendEmail.call send_email_params
          if command.success?
            thread_id = @params[:thread_id].present? ? @params[:thread_id] : EmailThread.create(tenant_id: @user.tenant_id, owner: @user, source_thread_id: command.result.thread_id).id

            email.assign_attributes(source_id: command.result.id, email_thread_id: thread_id)
            if account.provider_name.eql?(CUSTOM_PROVIDER)
              email.status = Email.statuses['sending']
            else
              email.status = Email.statuses['sent'] if thread_id
            end
            email.global_message_id = command.result.global_message_id unless command.result.global_message_id.blank?
            email.save

            validated_look_ups.each { |v| email.related_to << v }
            if @reply_to_email.present?
              @reply_to_email.related_to.where(entity_type: LOOKUP_DEAL).each do |deal_look_up|
                email.related_to << deal_look_up unless email.related_to.find_by(id: deal_look_up.id)
              end
            end
            CreateAttachment.call email, attachments, @params[:forwardTo] if attachments.present?
            email.reload
            email_thread=  EmailThread.find(thread_id)
            contact_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_CONTACT).pluck(:entity_id) + email_thread.contact_ids.to_a).uniq
            lead_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_LEAD).pluck(:entity_id) + email_thread.lead_ids.to_a).uniq
            user_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_USER).pluck(:entity_id) + email_thread.user_ids.to_a).uniq
            deal_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_DEAL).pluck(:entity_id) + email_thread.deal_ids.to_a).uniq
            email_thread.update(lead_ids: lead_ids, user_ids: user_ids, deal_ids: deal_ids, contact_ids: contact_ids)
          end
        end
        if @params[:relatedTo] && @params[:relatedTo]['entity'] == LOOKUP_DEAL
          EmailRelatedToContactEventPublisher.call(@params, @auth_data, email)
        end

        EmailReceivedRelatedToEntityPublisher.call(email, true)
        EmailCreateEventPublisher.call(email.id, CREATE_ACTION)
        EmailCreatedWorkflowV2EventPublisher.call(email, @params[:metadata])

        email
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error "Send Email: #{e.message}"
        raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
      end
    else
      Rails.logger.error 'Unauthorised: User context missing in CreateEmail'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end
  end

  private

  def look_ups_to_validate
    look_ups = @params[:to]
      .union(@params[:cc])
      .union(@params[:bcc])
    #  .uniq{|el| "#{el[:entity]}_#{el[:id]}"}
    look_ups << @params[:relatedTo] if @params[:relatedTo]
    look_ups
  end

  def get_recipient_lookups(type)
    recipient_lookups = []
    @params[type].each do |entity|
      @validated_look_ups.map do |validated_lookup|
        if validated_lookup.email.eql?(entity[:email])
          validated_lookup.metadata = @params[:metadata]
          recipient_lookups << validated_lookup 
        end
      end
    end

    recipient_lookups
  end

  def download_existing_attachments
    return [] unless @params[:attachments]
    all_attachments = @params[:attachments].reject{|c| c['id'].present?}
    attachments_to_download = @params[:attachments].select{|c| c['id'].present?}
    attachments_to_download.each do |attachment_data|

      attachment = Attachment.where(email_id: @params[:forwardTo], id: attachment_data['id']).first
      next unless attachment
      downloaded_file = DownloadAttachmentFromS3.call(attachment).result
      if downloaded_file.is_a?(File)
        all_attachments << HashWithIndifferentAccess.new({
          data: downloaded_file,
          oldAttachment: attachment,
          fileName: attachment_data['fileName']
        })
      end
    end
    all_attachments
  end

  def email_template_attachments
    return [] unless @params[:emailTemplateAttachments]
    all_attachments = []
    attachments_to_copy = @params[:emailTemplateAttachments].select{|c| c['id'].present?}
    attachments_to_copy.each do |attachment_data|
      attachment = TemplateAttachment.where(id: attachment_data['id']).first
      next unless attachment
      downloaded_file = DownloadAttachmentFromS3.call(attachment).result
      if downloaded_file.is_a?(File)
        all_attachments << HashWithIndifferentAccess.new({
          data: downloaded_file,
          oldAttachment: attachment,
          fileName: attachment_data['fileName']
        })
      end
    end
    all_attachments
  end

  def get_subject
    @params[:subject].presence == 'null' ? 'No Subject' : @params[:subject]
  end

  def replace_inline_base_64_data_with_attachments(body:)
    inline_attachments = []

    return '', inline_attachments unless body.present?

    matched_base64_src_atrributes = body.scan(BASE64_SRC_REGEX)

    matched_base64_src_atrributes.each_with_index do |attribute, index|
      attachment_file = File.new("#{Rails.root}/tmp/#{DateTime.now.to_i}#{index}_#{@tenant_id}_#{@user.id}_#{rand(10000)}", 'wb')
      attachment_file.write(Base64.decode64(attribute[2]))
      attachment_file.pos = 0
      content_id = SecureRandom.uuid

      attachment = HashWithIndifferentAccess.new({
        data: attachment_file,
        fileName: "#{content_id}.#{Mime::Type.lookup(attribute[1]).symbol.to_s}",
        type: 'inline',
        content_id: content_id
      })

      attachment_file.define_singleton_method(:content_type) { attribute[1] }

      inline_attachments << attachment
      body = body.sub(attribute[0], "src=\"cid:#{content_id}\" alt=\"#{attachment['fileName']}\"")
    end

    return body, inline_attachments

  rescue StandardError => e
    Rails.logger.error("Email Service | Failed to create inline attachment: #{e.message} | Email: #{@params}")
    raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
  end
end
