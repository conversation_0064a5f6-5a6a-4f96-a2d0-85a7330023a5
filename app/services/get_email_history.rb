# frozen_string_literal: true
include RecordActionsProcessor

class GetEmailHistory
  prepend SimpleCommand

  def initialize(params)
    @email_id = params[:id]
    @page = params[:page]
    @size = params[:size]
    @view = params[:view]
  end

  def call
    command = GetSecurityContext.call

    unless command.success?
      Rails.logger.error "Unauthorised: Missing user context in GetEmailHistory"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end

    @auth_data = command.result

    email = Email.find_by(tenant_id: @auth_data.tenant_id, id: @email_id)

    unless email
      Rails.logger.info "No email found with the id: #{@email_id}"
      raise(ExceptionHandler::NotFound, ErrorCode.not_found)
    end

    thread = email.email_thread

    unless user_can_read_thread?(thread.id)
      Rails.logger.error "Unauthorised: User does not have read on thread. User id #{@auth_data.user_id} Thread id #{email.email_thread_id}"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    end

    emails = thread.emails.where("id <= ?", @email_id).order(created_at: :desc)
    paginate(emails)

  end

  private

  def paginate(emails)
    page = @page || 1
    size = @size || 10
    emails = emails.page(page.to_i).per_page(size.to_i).
      select(:to, :cc, :bcc, :from, :subject, :status, :tracking_enabled,
             :body,  :id, :created_at, :direction, :body)
  end
end
