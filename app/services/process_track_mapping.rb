class ProcessTrackMapping
  prepend SimpleCommand

  def initialize id
    @id = id
  end

  def call
    email = load_email
    return if email.blank?

    old_serialized_email_data = EmailDetailsSerializer.call(email, false, nil, true, add_owner_id: true).result

    email.email_track_logs.create(tenant_id: email.tenant_id)
    email_with_link_logs = nil

    unless email.opened?
      email.update(status: Email.statuses['opened'])
      EmailOpenedEventPublisher.call(email)
      email_with_link_logs = load_email_with_link_logs(email)
      PublishEvent.call(Event::EmailOpened.new(email_with_link_logs, old_serialized_email_data))
      Rails.logger.info "Email Opened publisher called Tenant ID #{email.tenant_id} Email ID: #{email.id}"
    end

    email_with_link_logs = load_email_with_link_logs(email) unless email_with_link_logs.present?

    PublishEvent.call(Event::EmailUpdatedV2.new(email_with_link_logs, old_serialized_email_data))
    Rails.logger.info "Email Updated V2 publisher called (Email Opened) for Tenant ID #{email.tenant_id} Email ID: #{email.id}"
  end

  private

  def load_email
    TrackMapping.includes(email: { link_mappings: [:email_link_logs] }).find_by(id: @id)&.email
  end

  def load_email_with_link_logs email
    Email.includes( link_mappings: :email_link_logs ).find_by(id: email.id)
  end
end
