require 'rest-client'
class ValidateLeads
  prepend SimpleCommand

  def initialize(leads = [])
    @leads = leads
  end

  def call
    return [] if @leads.nil? || @leads.empty?
    command = GetSecurityContext.call
    if command.success?
      verified_leads = lead_search_response
      verified_leads = verified_leads.select { |lead| lead.dig('recordActions', 'email') }
      Rails.logger.info "ValidateLeads: verified_leads: #{verified_leads}"
      @leads.each do |lead|
        Rails.logger.info "ValidateLeads: lead: #{lead}"
        v_lead = verified_leads.find { |v_l| lead.entity_id == v_l["id"] }
        Rails.logger.info "ValidateLeads: lead: #{v_lead}"
        if v_lead.present?
          lead.name = "#{v_lead["firstName"]} #{v_lead["lastName"]}".strip
          lead.owner_id = v_lead['ownerId']
          if v_lead["emails"].present? && lead.email
            emails = v_lead["emails"].map{|x| x['value']}
            if emails && emails.exclude?(lead.email)
              raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
            end
          end
        else
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      return @leads
    else
      raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
    end
  end

  private

  def lead_search_response
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    lead_ids_params = @leads.collect{|lead| lead.entity_id }.uniq.join(",")
    begin
      response = RestClient.post(
        SERVICE_SEARCH + "/v1/search/lead?page=0&size=1000&sort=updatedAt,desc",
        {
          fields: %w[id firstName lastName emails ownerId],
          jsonRule: {
            condition: 'AND',
            rules: [
              {
                operator: 'in',
                id: 'id',
                field: 'id',
                type: 'double',
                value: lead_ids_params
              }
            ],
            valid: true
          }
        }.to_json,
        {
          Authorization: "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )
      return JSON(response.body)['content'] unless response.nil?
      Rails.logger.error "ValidateLead.lead_search_response invalid response | lead_ids: #{lead_ids_params}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "ValidateLead.lead_search_response sales 404 | lead_ids: #{lead_ids_params}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "ValidateLead.lead_search_response sales 500 | lead_ids: #{lead_ids_params}"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "ValidateLead.lead_search_response sales 400 | lead_ids: #{lead_ids_params}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
