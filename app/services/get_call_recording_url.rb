# frozen_string_literal: true

require 'rest-client'

class GetCallRecordingUrl
  prepend SimpleCommand

  def initialize(call_log_id, call_recording_id)
    @call_log_id = call_log_id
    @call_recording_id = call_recording_id
  end

  def call
    command = GetSecurityContext.call('token')
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?

    token = command.result
    begin
      response = RestClient.get(
        "#{SERVICE_CALL}/v1/call-logs/#{@call_log_id}/call-recordings/#{@call_recording_id}?longLivedUrl=true",
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?

    rescue RestClient::NotFound
      Rails.logger.error 'Get CallRecordingUrl - 404'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error 'Get CallRecordingUrl - 500'
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error 'Get CallRecordingUrl - 400'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue StandardError => e
      Rails.logger.error "Get CallRecordingUrl - invalid response - #{e}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
