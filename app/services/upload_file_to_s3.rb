class UploadFileToS3
  prepend SimpleCommand

  def initialize(file_path, name, bucket)
    @file_path = file_path
    @name = name
    Aws.config.update(
      endpoint: ENV['AWS_ENDPOINT'],
      region: ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY_ID'], ENV['AWS_SECRET_ACCESS_KEY'])
    )
    s3 = Aws::S3::Resource.new
    @bucket = s3.bucket(bucket)
  end

  def call
    begin
      obj = @bucket.object(@name)
      obj.upload_file(@file_path)
      File.delete(@file_path)
    rescue StandardError => e
      Rails.logger.error "Email Service | Upload file to s3: #{e.message} | #{@name}"
    end
  end
end
