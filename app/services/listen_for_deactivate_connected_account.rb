class ListenForDeactivateConnectedAccount
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(USER_EXCHANGE, DEACTIVATE_CONNECTED_ACCOUNT_EVENT, DEACTIVATE_CONNECTED_ACCOUNT_QUEUE) do |payload|
      Rails.logger.debug "Received message #{payload} for #{DEACTIVATE_CONNECTED_ACCOUNT_EVENT}"
      payload = JSON(payload)
      tenant_id = payload['tenantId']
      user_id = payload['userId']
      provider = payload['provider']
      disconnected_by = payload['disconnectedBy']
      user_email = payload['userEmail']
      tenant_email = payload['tenantEmail']
      DeactivateConnectedAccount.call(tenant_id, user_id, provider, disconnected_by, EMAIL_OAUTH, user_email, tenant_email)
    end
  end
end
