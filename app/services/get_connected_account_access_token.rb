class GetConnectedAccountAccessToken
  prepend SimpleCommand

  def initialize(account, skip_auth: false)
    @connected_account = account
    @skip_auth = skip_auth
  end

  def call
    update_token unless valid_access_token?
    decrypt_token
  end

  private

  def decrypt_token
    return @connected_account.reload.access_token unless [GOOGLE_PROVIDER, MICROSOFT_PROVIDER].include? @connected_account.provider_name
    decipher = OpenSSL::Cipher::AES.new(128, :CBC)
    decipher.decrypt
    decipher.padding=1
    decipher.key = ENV['EMAIL_CREDENTIAL_ENCRYPTION_SECRET']
    decipher.iv = ENV['EMAIL_CREDENTIAL_ENCRYPTION_IV']
    decipher.update(Base64.decode64(@connected_account.reload.access_token)) + decipher.final
  end

  def valid_access_token?
    @connected_account.expires_at > Time.current.to_i
  end

  def update_token
    unless @skip_auth
      command = GetSecurityContext.call
      raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized) unless command.success?
    end
    response = get_access_token
    @connected_account.update(access_token: response['accessToken'], expires_at: Time.parse(response['expiresAt']).to_i)
  end

  def get_access_token
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    begin
      response = RestClient.get(
        SERVICE_IAM + "/v1/oauth/email-access-token",
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "GetAccessToken: invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "GetAccessToken 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "GetAccessToken 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "GetAccessToken 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
