# frozen_string_literal: true

class ListenForDealReassign
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(DEAL_EXCHANGE, DEAL_OWNER_UPDATED_EVENT, DEAL_OWNER_UPDATED_QUEUE) do |payload|
      Rails.logger.info "Received message #{payload} for #{DEAL_OWNER_UPDATED_EVENT}"
      payload = JSON(payload)
      data = {}
      data[:entity_id] = payload['id']
      data[:new_owner_id] = payload.dig('deal', 'ownerId')
      data[:tenant_id] = payload.dig('deal', 'tenantId')

      UpdateOwnerForEntity.call(LOOKUP_DEAL, data).result
    end
  end
end
