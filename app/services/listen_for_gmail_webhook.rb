class ListenForGmailWebhook
  prepend SimpleCommand
  include ActiveModel::Valida<PERSON>

  def call
    RabbitmqConnection.subscribe(EMAIL_EXCHANGE, GMAIL_WEBHOOK_EVENT, GMAIL_WEBHOOK_QUEUE) do |payload|
      payload = JSO<PERSON>(payload)
      Rails.logger.info "Message Received: #{EMAIL_EXCHANGE} | #{GMAIL_WEBHOOK_EVENT} | #{payload}"
      command = FetchGmailHistory.call(payload["params"])
      return unless command.success?
      result = command.result
      FetchGmailMessages.call(result[0], result[1], result[2]) if result.present?
    end
  end
end
