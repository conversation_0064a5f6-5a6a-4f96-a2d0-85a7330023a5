class EmailMetadataPublisher
  prepend SimpleCommand
  def initialize(email, look_up)
    @email = email
    @look_up = look_up
  end

  def call
    if @look_up.is_a_deal?
      event = Event::DealMetadata.new(@email, @look_up)
    elsif @look_up.is_a_lead?
      event = Event::LeadMetadata.new(@email, @look_up)
    end
    PublishEvent.call(event)
    Rails.logger.info "Event::EmailMetadata event: #{event.to_json}"
  end
end
