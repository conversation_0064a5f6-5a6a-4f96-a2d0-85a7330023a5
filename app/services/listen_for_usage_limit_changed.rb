class ListenForUsageLimitChanged
  prepend SimpleCommand
  include ActiveModel::Validations

  def call
    RabbitmqConnection.subscribe(USER_EXCHANGE, USAGE_LIMIT_CHANGED_EVENT, USAGE_LIMIT_CHANGED_QUEUE) do |payload|
      Rails.logger.debug "Received message #{payload} for #{USAGE_LIMIT_CHANGED_EVENT}"
      payload = JSON(payload)
      id = payload["userId"]
      tenant_id = payload["tenantId"]
      usage_entity_limits = payload['usageEntityLimits']

      UpdateEmailTrackingSetting.call(tenant_id, payload)
      if usage_entity_limits.present?
        email_templates_limit = usage_entity_limits.find{ |data| data['usageEntity'] == 'EMAIL_TEMPLATES' }
        offset = email_templates_limit['limit']
        EmailTemplate.where(active: true, tenant_id: tenant_id).order(created_at: :desc).offset(offset).update_all(active: false)
      end
    end
  end
end
