require 'rest-client'
class ValidateContacts
  prepend SimpleCommand

  def initialize(contacts = [])
    @contacts = contacts
  end

  def call
    return [] if @contacts.nil? || @contacts.empty?
    command = GetSecurityContext.call
    if command.success?
      verified_contacts = contact_search_response
      verified_contacts = verified_contacts.select { |contact| contact.dig('recordActions', 'email') }
      @contacts.each do |contact|
        v_contact = verified_contacts.find { |v_c| contact.entity_id == v_c["id"] }
        if v_contact.present?
          contact.name = "#{v_contact["firstName"]} #{v_contact["lastName"]}".strip
          contact.owner_id = v_contact['ownerId']
          if v_contact["emails"].present? && contact.email
            emails = v_contact["emails"].map{|x| x['value']}
            if emails && emails.exclude?(contact.email)
              raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
            end
          end
        else
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      return @contacts
    else
      raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
    end
  end

  private

  def contact_search_response
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?
    token = command.result
    contact_ids_params = @contacts.collect{|contact| contact.entity_id }.uniq.join(",")
    begin
      response = RestClient.post(
        SERVICE_SEARCH + "/v1/search/contact?page=0&size=1000&sort=updatedAt,desc",
        {
          fields: %w[id firstName lastName emails ownerId],
          jsonRule: {
            condition: 'AND',
            rules: [
              {
                operator: 'in',
                id: 'id',
                field: 'id',
                type: 'double',
                value: contact_ids_params
              }
            ],
            valid: true
          }
        }.to_json,
        {
          Authorization: "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )
      return JSON(response.body)['content'] unless response.nil?
      Rails.logger.error "ValidateContact.contact_search_response invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "ValidateContact.contact_search_response sales 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "ValidateContact.contact_search_response sales 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "ValidateContact.contact_search_response sales 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
