class SubscriptionRenewalJob < ApplicationJob

  queue_as :default
  sidekiq_options retry: 3

  def perform
    ConnectedAccount.where(active: true).each do |acct|
      begin
        add_token_in_current_thread(acct)
        reregister_webhook(acct)
        FetchDisplayName.call(acct)
      rescue => e
        Rails.logger.info "Account webhook reregister error: #{e.message}"
        next
      ensure
        ActiveRecord::Base.clear_active_connections!
      end
    end
  end

  def add_token_in_current_thread acct
    thread = Thread.current
    thread[:token] = GenerateToken.call(acct.user.id, acct.user.tenant_id).result
  end

  private

  def reregister_webhook(acct)
    Rails.logger.info "Renew account:: #{acct.email} ==> #{acct.provider_name.inspect}--#{acct.inspect}"
    case acct.provider_name
    when GOOGLE_PROVIDER
      RegisterGmailWebhook.call(acct)
    when MICROSOFT_PROVIDER
      RenewOutlookWebhook.call(acct)
    else
      raise(ExceptionHandler::InvalidMailProviderNameError, ErrorCode.invalid_mail_provider_name)
    end
  end
end
