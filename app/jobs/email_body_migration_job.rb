class EmailBodyMigrationJob < ApplicationJob

  queue_as :default
  sidekiq_options retry: 3

  def perform(tenant_id: nil)
    query = { body_summary: nil }
    query[:tenant_id] = tenant_id unless tenant_id.blank?
    Email.where(query).where.not(body: [nil, ""]).order(:id).find_in_batches(batch_size: 100) do |emails|
      emails.each do |email|
        begin
          body =  ActionView::Base.full_sanitizer.sanitize(email.body)
          body_summary = Nokogiri::HTML(body).text.slice(0..100).squish
          email.update(body_summary: body_summary)
          Rails.logger.info "EmailBodyMigration: #{email.id}"
        rescue Exception => e
          Rails.logger.error "EmailBodyMigration: #{email.id} | #{e.to_s}"
        end
      end
    end
  end
end
