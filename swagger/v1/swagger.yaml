---
openapi: 3.0.1
info:
  title: API V1
  version: v1
paths:
  "/v1/emails":
    post:
      summary: Creates an Email
      tags:
      - Emails
      security:
      - bearerAuth: []
      parameters:
      - in: header
        name: Authorization
        required: true
        description: Client token
        schema:
          type: string
      responses:
        '201':
          description: email created
          content: {}
        '401':
          description: authentication failed
          content: {}
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - to
              - related_to
              properties:
                body:
                  type: string
                subject:
                  type: string
                to:
                  type: array
                  items:
                    type: object
                    properties:
                      entity:
                        type: string
                      id:
                        type: string
                      name:
                        type: string
                      email:
                        type: string
                    required:
                    - entity
                    - id
                    - name
                    - email
                cc:
                  type: array
                  items:
                    type: object
                    properties:
                      entity:
                        type: string
                      id:
                        type: string
                      name:
                        type: string
                      email:
                        type: string
                    required:
                    - entity
                    - id
                    - name
                    - email
                bcc:
                  type: array
                  items:
                    type: object
                    properties:
                      entity:
                        type: string
                      id:
                        type: string
                      name:
                        type: string
                      email:
                        type: string
                    required:
                    - entity
                    - id
                    - name
                    - email
                related_to:
                  type: array
                  items:
                    type: object
                    properties:
                      entity:
                        type: string
                      id:
                        type: string
                      name:
                        type: string
                      email:
                        type: string
                    required:
                    - entity
                    - id
                    - name
                    - email
servers:
- url: https://{defaultHost}
  variables:
    defaultHost:
      default: www.example.com
