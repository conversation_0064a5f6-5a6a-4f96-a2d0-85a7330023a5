{"openapi": "3.0.1", "info": {"title": "API V1", "version": "v1"}, "paths": {"/v90f80607226e15c2/emails/health": {"get": {"summary": "Email from database", "tags": ["Emails"], "responses": {"200": {"description": "Database is up"}, "404": {"description": "Entity not present"}, "503": {"description": "Database is down"}}}}, "/v1/email_mappings/{id}": {"get": {"summary": "gets data", "tags": ["Email trakcing"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Get url"}}}}, "/v1/emails": {"post": {"summary": "Creates an Email", "tags": ["Emails"], "security": [{"bearerAuth": []}], "parameters": [{"name": "body"}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "email created"}, "401": {"description": "authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["to", "relatedTo"], "properties": {"body": {"type": "string"}, "subject": {"type": "string"}, "to": {"type": "array", "items": {"type": "object", "properties": {"entity": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["entity", "id", "name", "email"]}}, "cc": {"type": "array", "items": {"type": "object", "properties": {"entity": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["entity", "id", "name", "email"]}}, "bcc": {"type": "array", "items": {"type": "object", "properties": {"entity": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["entity", "id", "name", "email"]}}, "relatedTo": {"type": "object"}}}}}}}}, "/v1/emails/{id}/mark_as_read": {"post": {"summary": "Mark email as read", "tags": ["Emails"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Email marked as read"}, "401": {"description": "Authentication failed"}, "404": {"description": "Email Not Found"}, "422": {"description": "Invalid Request"}}}}, "/v1/emails/{id}/mark_as_unread": {"post": {"summary": "Mark email as un-read", "tags": ["Emails"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Email mark as unread"}, "401": {"description": "Authentication failed"}, "404": {"description": "Email Not Found"}, "422": {"description": "Invalid Request"}}}}, "/v1/emails/{id}/forward": {"post": {"summary": "Mark email as read", "tags": ["Emails"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "body"}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "email created"}, "401": {"description": "authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["to", "relatedTo"], "properties": {"body": {"type": "string"}, "subject": {"type": "string"}, "to": {"type": "array", "items": {"type": "object", "properties": {"entity": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["entity", "id", "name", "email"]}}, "cc": {"type": "array", "items": {"type": "object", "properties": {"entity": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["entity", "id", "name", "email"]}}, "bcc": {"type": "array", "items": {"type": "object", "properties": {"entity": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["entity", "id", "name", "email"]}}, "relatedTo": {"type": "object"}}}}}}}}, "/v1/emails/{id}/attachments/{attachment_id}": {"get": {"summary": "Get attachment url", "tags": ["Emails"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "attachment_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Attachment"}, "401": {"description": "authentication failed"}}}}, "/v1/emails/{id}": {"delete": {"summary": "Delete Email", "tags": ["Emails"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Email Deleted"}, "401": {"description": "Unauthorized"}, "404": {"description": "Email Not Found"}, "422": {"description": "Invalid Request"}}}}, "/v1/email-threads/{id}/emails": {"get": {"summary": "Get attachment url", "tags": ["Emails"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entity_id", "in": "query", "schema": {"type": "string"}}, {"name": "entity_type", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Attachment"}, "401": {"description": "authentication failed"}}}}, "/v1/emails/search/smart-list/count": {"get": {"summary": "Get records count of smart-list", "tags": ["Emails"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Smart List Records Count"}, "401": {"description": "authentication failed"}}}}, "/v1/email-templates": {"post": {"summary": "Creates an Email template", "tags": ["<PERSON>ail Te<PERSON>late"], "security": [{"bearerAuth": []}], "parameters": [{"name": "body"}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "email template created"}, "401": {"description": "authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["name", "category"], "properties": {"name": {"type": "String"}, "category": {"type": "String"}, "subject": {"type": "String"}, "body": {"type": "String"}}}}}}, "x-access-policy": {"action": "create", "policy": "EMAIL_TEMPLATES", "resource": "emailTemplates"}}}, "/v1/email-templates/{id}": {"put": {"summary": "Updates an Email template", "tags": ["<PERSON>ail Te<PERSON>late"], "security": [{"bearerAuth": []}], "parameters": [{"name": "body"}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "email template updated"}, "401": {"description": "authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["name", "category"], "properties": {"name": {"type": "String"}, "category": {"type": "String"}, "subject": {"type": "String"}, "body": {"type": "String"}}}}}}}, "get": {"summary": "Get Email template ", "tags": ["<PERSON>ail Te<PERSON>late"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "email template"}, "401": {"description": "authentication failed"}}}}, "/v1/email-templates/search": {"post": {"summary": "Search an Email template", "tags": ["<PERSON>ail Te<PERSON>late"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "email template Search"}, "401": {"description": "authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"jsonRule": {"condition": {"type": "string"}, "rules": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "/v1/email-templates/variables": {"get": {"summary": "Get Email template variables", "tags": ["<PERSON>ail Te<PERSON>late"], "security": [{"bearerAuth": []}], "parameters": [{"name": "category", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "email template variables for category"}, "401": {"description": "authentication failed"}}}}, "/v1/email-templates/{id}/{entity_type}/{entity_id}": {"get": {"summary": "Get details with entity", "tags": ["<PERSON>ail Te<PERSON>late"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entity_type", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "email template with entity"}, "401": {"description": "authentication failed"}}}}, "/v1/email-templates/{id}/activate": {"post": {"summary": "Activate Email template ", "tags": ["<PERSON>ail Te<PERSON>late"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "email template activated"}, "401": {"description": "authentication failed"}}, "x-access-policy": {"action": "activate", "policy": "EMAIL_TEMPLATES", "resource": "emailTemplates"}}}, "/v1/email-templates/{id}/deactivate": {"post": {"summary": "Deactivate Email template ", "tags": ["<PERSON>ail Te<PERSON>late"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "email template deactivated"}, "401": {"description": "authentication failed"}}}}, "/v1/email-templates/:id/attachments/:attachment_id": {"get": {"summary": "Download Email template attachment", "tags": ["<PERSON>ail Te<PERSON>late"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "attachment_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "email template attachment url"}, "401": {"description": "authentication failed"}}}}, "/v2/email-templates/variables": {"get": {"summary": "Get Email template variables", "tags": ["<PERSON>ail Te<PERSON>late"], "security": [{"bearerAuth": []}], "parameters": [{"name": "category", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "email template variables for category"}, "401": {"description": "authentication failed"}}}}, "/v1/email-templates/lookup": {"get": {"summary": "Email Template Lookup", "tags": ["<PERSON>ail Te<PERSON>late"], "security": [{"bearerAuth": []}], "parameters": [{"name": "category", "in": "query", "schema": {"type": "string"}}, {"name": "q", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "User token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Email Templates fetched successfully"}, "401": {"description": "Authentication failed"}}}}, "/v1/email-threads": {"post": {"summary": "Common email-threads url for gateway", "tags": ["Email-threads"], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Emails Thread controller"}}}}, "/v1/email-threads/search": {"post": {"summary": "Searches emails", "tags": ["Emails"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Emails Searched"}, "401": {"description": "authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"jsonRule": {"condition": {"type": "string"}, "rules": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "/v1/email-threads/{id}/mark_as_read": {"post": {"summary": "Marks all email in the thread as read", "tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"204": {"description": "Emails Thread marked as read"}, "401": {"description": "authentication failed"}}}}, "/v1/email-threads/{id}/mark_as_unread": {"post": {"summary": "Marks all email in the thread as unread", "tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"204": {"description": "Emails Thread marked as unread"}, "401": {"description": "authentication failed"}}}}, "/v1/email-threads/{id}/reply": {"post": {"summary": "Reply on thread", "tags": ["Reply on email Thread"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Reply on email thread"}, "401": {"description": "authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["to", "relatedTo"], "properties": {"body": {"type": "string"}, "subject": {"type": "string"}, "to": {"type": "array", "items": {"type": "object", "properties": {"entity": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["entity", "id", "name", "email"]}}, "cc": {"type": "array", "items": {"type": "object", "properties": {"entity": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["entity", "id", "name", "email"]}}, "bcc": {"type": "array", "items": {"type": "object", "properties": {"entity": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["entity", "id", "name", "email"]}}, "relatedTo": {"type": "object"}, "replyTo": {"type": "integer"}}}}}}}}, "/v1/email-threads/{id}": {"delete": {"summary": "Delete an Email thread", "tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Email thread delete success"}, "401": {"description": "Authentication failed"}}}}, "/v1/email-threads/{id}/associate-with-deals": {"post": {"summary": "Associate email with thread", "tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Email thread association with deal"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "array"}}}}}}, "/v1/emails/fields": {"get": {"summary": "List fields", "tags": ["Email Fields"], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Email Fields List"}, "401": {"description": "Authentication failed"}}}}, "/v1/gmail_webhooks": {"post": {"summary": "creates webhook", "tags": ["Gmail webhook"], "parameters": [], "responses": {"200": {"description": "webhook creation"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "json"}}}}}}, "/v1/emails/layout/list": {"get": {"summary": "Layout List", "tags": ["Layouts"], "security": [{"bearerAuth": []}], "parameters": [{"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Layout List API"}, "401": {"description": "Authentication Failed"}}}}, "/v1/link_mappings/{id}": {"get": {"summary": "gets data", "tags": ["Email trakcing"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Get url"}}}}, "/v1/outlook_webhooks": {"post": {"summary": "creates webhook", "tags": ["Outlook webhook"], "parameters": [], "responses": {"200": {"description": "webhook creation"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "json"}}}}}}, "/v2/email-threads/search": {"post": {"summary": "Searches emails", "tags": ["Emails"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Emails Searched"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"jsonRule": {"condition": {"type": "string"}, "rules": {"type": "array", "items": {"type": "string"}}}}}}}}}}}, "servers": [{"url": "http://{defaultHost}", "variables": {"defaultHost": {"default": "localhost:3000"}}}, {"url": "https://{defaultHost}", "variables": {"defaultHost": {"default": "127.0.0.1:3000"}}}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}