# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_08_21_013616) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "pgcrypto"
  enable_extension "plpgsql"

  create_table "attachments", force: :cascade do |t|
    t.bigint "email_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "file_name"
    t.bigint "file_size"
    t.boolean "inline", default: false
    t.string "content_id"
    t.boolean "deleted", default: false
    t.index ["email_id"], name: "index_attachments_on_email_id"
  end

  create_table "connected_accounts", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "user_id", null: false
    t.string "access_token"
    t.string "refresh_token"
    t.bigint "expires_at"
    t.string "email"
    t.text "scopes", default: [], array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "provider_name"
    t.boolean "active"
    t.string "last_history_id"
    t.string "subscription_id"
    t.bigint "subscription_expiry"
    t.datetime "last_synced_at"
    t.string "display_name"
  end

  create_table "email_link_logs", force: :cascade do |t|
    t.bigint "email_id", null: false
    t.uuid "link_mapping_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "tenant_id"
    t.index ["created_at"], name: "index_email_link_logs_on_created_at"
    t.index ["email_id"], name: "index_email_link_logs_on_email_id"
    t.index ["link_mapping_id"], name: "index_email_link_logs_on_link_mapping_id"
    t.index ["tenant_id"], name: "index_email_link_logs_on_tenant_id"
  end

  create_table "email_look_ups", force: :cascade do |t|
    t.bigint "look_up_id", null: false
    t.boolean "related"
    t.bigint "email_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "recipient_type"
    t.bigint "tenant_id"
    t.boolean "deleted", default: false
    t.index ["email_id", "tenant_id"], name: "emaillookups_emailidtenantid"
    t.index ["email_id"], name: "index_email_look_ups_on_email_id"
    t.index ["look_up_id"], name: "index_email_look_ups_on_look_up_id"
    t.index ["tenant_id"], name: "index_email_look_ups_on_tenant_id"
  end

  create_table "email_templates", force: :cascade do |t|
    t.string "name", null: false
    t.string "category", null: false
    t.string "subject"
    t.text "body"
    t.boolean "active", default: true, null: false
    t.bigint "tenant_id", null: false
    t.bigint "created_by_id", null: false
    t.bigint "updated_by_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["tenant_id", "name"], name: "index_email_templates_on_tenant_id_and_name", unique: true
  end

  create_table "email_threads", force: :cascade do |t|
    t.bigint "tenant_id"
    t.bigint "owner_id", null: false
    t.string "source_thread_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "deleted", default: false
    t.integer "contact_ids", array: true
    t.integer "lead_ids", default: [], array: true
    t.integer "deal_ids", default: [], array: true
    t.integer "user_ids", default: [], array: true
    t.index ["contact_ids"], name: "index_email_threads_on_contact_ids", using: :gin
    t.index ["deal_ids"], name: "index_email_threads_on_deal_ids", using: :gin
    t.index ["lead_ids"], name: "index_email_threads_on_lead_ids", using: :gin
    t.index ["owner_id"], name: "index_email_thread_on_owner_id"
    t.index ["source_thread_id"], name: "emailThreads_sourceThreadId"
    t.index ["tenant_id"], name: "index_email_threads_on_tenant_id"
    t.index ["user_ids"], name: "index_email_threads_on_user_ids", using: :gin
  end

  create_table "email_track_logs", force: :cascade do |t|
    t.bigint "email_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "tenant_id"
    t.index ["created_at"], name: "index_email_track_logs_on_created_at"
    t.index ["email_id"], name: "index_email_track_logs_on_email_id"
    t.index ["tenant_id"], name: "index_email_track_logs_on_tenant_id"
  end

  create_table "emails", force: :cascade do |t|
    t.string "to", default: [], array: true
    t.string "cc", default: [], array: true
    t.string "from"
    t.string "bcc", default: [], array: true
    t.text "subject"
    t.string "source_id", limit: 256
    t.text "body"
    t.bigint "owner_id", null: false
    t.bigint "sender_id"
    t.bigint "connected_account_id", null: false
    t.string "reply_to"
    t.bigint "tenant_id", null: false
    t.integer "status"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "email_thread_id"
    t.bigint "read_by", default: [], array: true
    t.string "provider_id"
    t.string "global_message_id"
    t.boolean "is_trigerred"
    t.boolean "tracking_enabled", default: false
    t.boolean "deleted", default: false
    t.string "body_summary"
    t.integer "direction"
    t.integer "external_attachment_count", default: 0
    t.jsonb "metadata", default: {}
    t.integer "bounce_type"
    t.text "failed_reason"
    t.jsonb "campaign_info", default: {}
    t.index ["connected_account_id", "tenant_id"], name: "emails_connected_account_tenant_id"
    t.index ["created_at"], name: "index_email_on_created_at"
    t.index ["email_thread_id"], name: "index_emails_on_email_thread_id"
    t.index ["global_message_id"], name: "index_emails_on_global_message_id"
    t.index ["owner_id"], name: "index_email_on_owner_id"
    t.index ["source_id"], name: "index_emails_on_source_id"
    t.index ["tenant_id"], name: "emails_tenant_id"
  end

  create_table "emails_to_be_deleteds", force: :cascade do |t|
    t.bigint "email_id", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["email_id"], name: "index_emails_to_be_deleteds_on_email_id"
    t.index ["tenant_id"], name: "index_emails_to_be_deleteds_on_tenant_id"
  end

  create_table "global_messages", force: :cascade do |t|
    t.string "message_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["message_id"], name: "index_global_messages_on_message_id", unique: true
  end

  create_table "link_mappings", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "email_id", null: false
    t.text "url"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "tenant_id"
    t.index ["email_id"], name: "index_link_mappings_on_email_id"
    t.index ["tenant_id"], name: "index_link_mappings_on_tenant_id"
  end

  create_table "look_ups", force: :cascade do |t|
    t.string "entity"
    t.string "name"
    t.string "email"
    t.bigint "tenant_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "owner_id"
    t.bigint "entity_id"
    t.string "entity_type", limit: 15
    t.boolean "deleted", default: false
    t.index ["owner_id"], name: "index_look_ups_on_owner_id"
    t.index ["tenant_id", "email"], name: "lookUps_tenantId_email"
  end

  create_table "template_attachments", force: :cascade do |t|
    t.bigint "email_template_id", null: false
    t.text "url"
    t.string "file_name"
    t.bigint "file_size"
    t.boolean "inline", default: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["email_template_id"], name: "index_template_attachments_on_email_template_id"
  end

  create_table "tenants", force: :cascade do |t|
    t.boolean "tracking_enabled", default: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "track_mappings", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.bigint "email_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "tenant_id"
    t.index ["email_id"], name: "index_track_mappings_on_email_id"
    t.index ["tenant_id"], name: "index_track_mappings_on_tenant_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "profile_id"
  end

  add_foreign_key "attachments", "emails"
  add_foreign_key "email_link_logs", "emails"
  add_foreign_key "email_link_logs", "link_mappings"
  add_foreign_key "email_look_ups", "emails"
  add_foreign_key "email_look_ups", "look_ups"
  add_foreign_key "email_track_logs", "emails"
  add_foreign_key "emails", "email_threads"
  add_foreign_key "emails_to_be_deleteds", "emails"
  add_foreign_key "emails_to_be_deleteds", "tenants"
  add_foreign_key "link_mappings", "emails"
  add_foreign_key "template_attachments", "email_templates"
  add_foreign_key "track_mappings", "emails"
end
