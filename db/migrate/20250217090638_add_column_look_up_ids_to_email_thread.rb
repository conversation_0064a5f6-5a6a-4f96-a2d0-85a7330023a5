class AddColumnLookUpIdsToEmailThread < ActiveRecord::Migration[6.1]
  def change
    add_column :email_threads, :contact_ids, :integer, array: true, defalt: []
    add_column :email_threads, :lead_ids, :integer, array: true, default: []
    add_column :email_threads, :deal_ids, :integer, array: true, default: []
    add_column :email_threads, :user_ids, :integer, array: true, default: []

    add_index :email_threads, :contact_ids, using: :gin
    add_index :email_threads, :lead_ids, using: :gin
    add_index :email_threads, :deal_ids, using: :gin
    add_index :email_threads, :user_ids, using: :gin
  end
end
