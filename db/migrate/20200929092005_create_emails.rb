class CreateEmails < ActiveRecord::Migration[6.0]
  def change
    create_table :emails do |t|
      t.string :to, array: true, default: []
      t.string :cc, array: true, default: []
      t.string :from
      t.string :bcc, array: true, default: []
      t.text :subject
      t.string :source_id
      t.text :body
      t.bigint :owner_id, null: false, foreign_key: true
      t.bigint :sender_id, null: true, foreign_key: true
      t.bigint :connected_account_id, null: false, foreign_key: true
      t.string :reply_to
      t.bigint :tenant_id
      t.integer :status

      t.timestamps
    end
  end
end
