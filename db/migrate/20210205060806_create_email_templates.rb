class CreateEmailTemplates < ActiveRecord::Migration[6.0]
  def change
    create_table :email_templates do |t|
      t.string :name, null: false
      t.string :category, null: false
      t.string :subject
      t.text :body
      t.boolean :active, null: false, default: true
      t.bigint :tenant_id, null: false
      t.bigint :created_by_id, null: false, foreign_key: true
      t.bigint :updated_by_id, null: false, foreign_key: true

      t.timestamps
    end
  end
end
