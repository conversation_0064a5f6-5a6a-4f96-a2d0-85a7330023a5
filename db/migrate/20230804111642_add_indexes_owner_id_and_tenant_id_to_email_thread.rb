class AddIndexesOwnerIdAndTenantIdToEmailThread < ActiveRecord::Migration[6.0]
  def change
    add_index :email_threads, :owner_id, name: 'index_email_thread_on_owner_id' unless index_name_exists?(:email_threads, 'index_email_thread_on_owner_id')
    add_index :email_threads, :tenant_id, name: 'index_email_threads_on_tenant_id' unless index_name_exists?(:email_threads, 'index_email_threads_on_tenant_id')
  end
end
