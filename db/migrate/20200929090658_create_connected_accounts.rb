class CreateConnectedAccounts < ActiveRecord::Migration[6.0]
  def change
    create_table :connected_accounts do |t|
      t.bigint :provider_id, null: false, foreign_key: true
      t.bigint :tenant_id
      t.bigint :user_id, null: false, foreign_key: true
      t.string :access_token
      t.string :refresh_token
      t.bigint :expires_at
      t.string :email
      t.string :scopes, array: true, default: []

      t.timestamps
    end
  end
end
