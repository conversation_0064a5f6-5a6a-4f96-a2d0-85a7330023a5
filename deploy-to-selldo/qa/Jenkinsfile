podTemplate(
    containers: [
        containerTemplate(name: 'helm', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/lachlanevenson/k8s-helm:v3.4.2', command: 'cat',
            ttyEnabled: true),
        containerTemplate(name: 'curl', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/sling/jenkins/curl', command: 'cat', ttyEnabled: true)
    ],
    imagePullSecrets: ['registry-credentials']) {
  properties([parameters(
      [string(name: 'dockerImageTag', description: 'Docker image tag to deploy'),
       string(name: 'branchName', defaultValue: 'dev', description: 'Branch being deployed'),
       string(name: 'targetBranch', defaultValue: 'dev', description: 'Target branch against which if a PR is being raised')])])

  currentBuild.description = "branch ${params.branchName}"
  node(POD_LABEL) {
    container('helm') {
      withCredentials([[$class       : 'FileBinding',
                        credentialsId: 'selldo-test-kubeconfig',
                        variable     : 'KUBECONFIG'],
                       [$class       : 'StringBinding',
                        credentialsId: 'sd-charts-github-api-token',
                        variable     : 'API_TOKEN']]) {
        stage('Add Helm repository') {
          sh script: "helm repo add stable 'https://charts.helm.sh/stable'",
              label: 'Add stable helm repo'
          sh script: "helm repo add sd-charts 'https://${API_TOKEN}@raw.githubusercontent.com/amuratech/sd-charts/master/'",
              label: 'Add helm repo'
          sh script: 'helm repo list', label: 'List available helm repos'
        }
        withCredentials([[$class       : 'StringBinding',
                          credentialsId: 'selldo-test-env-postgres-password',
                          variable     : 'POSTGRES_PASSWORD'],
                          [$class       : 'StringBinding',
                          credentialsId: 'selldo-test-env-rabbitmq-password',
                          variable     : 'RABBITMQ_PASSWORD'],
                          [$class       : 'StringBinding',
                          credentialsId: 'sd-emails-rails-master-key',
                          variable     : 'RAILS_MASTER_KEY'],
                          [$class       : 'StringBinding',
                          credentialsId: 'selldo-app-qa-host',
                          variable     : 'APP_QA_HOST'],
                          [$class       : 'StringBinding',
                          credentialsId: 'selldo-stage-emails-bucket-name',
                          variable     : 'EMAIL_BUCKET_NAME'],
                          [$class       : 'StringBinding',
                          credentialsId: 'selldo-stage-email-templates-bucket-name',
                          variable     : 'EMAIL_TEMPLATES_BUCKET_NAME'],
                          [$class       : 'StringBinding',
                          credentialsId: 'selldo-qa-spaces-key',
                          variable     : 'SELL_DO_QA_SPACES_KEY'],
                          [$class       : 'StringBinding',
                          credentialsId: 'selldo-qa-spaces-secret',
                          variable     : 'SELL_DO_QA_SPACES_SECRET'],
                          [$class       : 'StringBinding',
                          credentialsId: 'selldo-qa-env-postgres-username',
                          variable     : 'POSTGRES_USERNAME'],
                          [$class       : 'StringBinding',
                          credentialsId: 'selldo-qa-env-rabbitmq-username',
                          variable     : 'RABBITMQ_USERNAME'],
                          [$class       : 'StringBinding',
                          credentialsId: 'selldo-qa-gmails-subscription-topics',
                          variable     : 'GMAIL_SUBSCRIPTION_TOPIC']
                         ]) {
          stage('Deploy') {
            echo "Deploying docker release -> nexus.sling-dev.com/8023/sling/sd-emails:${params.dockerImageTag}"
            sh script: "helm upgrade --install sd-emails sd-charts/sd-emails " +
                "--set " +
                "image.tag=${params.dockerImageTag}," +
                "appConfig.postgres.username=${POSTGRES_USERNAME}," +
                "appConfig.postgres.password=${POSTGRES_PASSWORD}," +
                "appConfig.rabbitmq.username=${RABBITMQ_USERNAME}," +
                "appConfig.rabbitmq.password=${RABBITMQ_PASSWORD}," +
                "appConfig.credentials.masterKey=${RAILS_MASTER_KEY}," +
                "appConfig.google.subscriptionTopic='${GMAIL_SUBSCRIPTION_TOPIC}'," +
                "appConfig.s3.emailBucket=${EMAIL_BUCKET_NAME}," +
                "appConfig.s3.emailTemplateBucket=${EMAIL_TEMPLATES_BUCKET_NAME}," +
                "appConfig.s3.key=${SELL_DO_QA_SPACES_KEY}," +
                "appConfig.s3.secret=${SELL_DO_QA_SPACES_SECRET}," +
                "namespace=selldo," +
                "appConfig.apiHost=https://${APP_QA_HOST}," +
                "deployment.annotations.buildNumber=${currentBuild.number} " +
                "--wait",
                label: 'Install helm release'
          }
        }
      }
    }
    container('curl') {
      withCredentials([[$class      : 'StringBinding',
                       credentialsId: 'selldo-qa-api-host',
                       variable     : 'SELL_DO_QA_API_HOST']]) {
        stage('Refresh Gateway routes') {
          sh script: "curl -X " +
                      "POST http://${SELL_DO_QA_API_HOST}/actuator/gateway/refresh " +
                      "-H 'Accept: application/json' " +
                      "-H 'Host: ${SELL_DO_QA_API_HOST}' " +
                      "-H 'cache-control: no-cache'",
             label: 'Force refresh routes cache'
        }
      }
    }
  }
}
