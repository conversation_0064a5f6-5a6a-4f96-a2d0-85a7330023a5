require 'sidekiq/web'

# Configure Sidekiq-specific session middleware
Sidekiq::Web.use ActionDispatch::Cookies
Sidekiq::Web.use ActionDispatch::Session::CookieStore, key: "_interslice_session"

Rails.application.routes.draw do
  # For details on the DSL available within this file, see https://guides.rubyonrails.org/routing.html
  mount Rswag::Api::Engine => '/v2'
  mount Sidekiq::Web, at: "/sidekiq"

  get 'health', to: 'emails#health'
  get "/v90f80607226e15c2/emails/health", to: "health#status"

  namespace :v1 do
    resources :email_threads, only: [:destroy], path: 'email-threads' do
      post :search, on: :collection
      member do
        post :mark_as_read
        post :mark_as_unread
        post :reply
        post 'associate-with-deals', to: "associate_with_deals"
      end
    end

    get '/email-threads/:id/emails' => 'emails#index'

    resources :emails, only: [:create, :destroy, :show] do
      collection do
        post 'send-email', to: 'emails#send_email'
        get 'search/smart-list/count', to: 'emails#smart_list_records_count'

        resources :layout, only: [] do
          collection do
            get :list
          end
        end

        resources :fields, only: [:index]
      end

      member do
        post :mark_as_read
        post :mark_as_unread
        post :forward
        get :history
        get "attachments/:attachment_id", to: "emails#download_attachment"
      end
    end

    resources :gmail_webhooks, only: [:create]
    resources :imap_webhooks, only: [:create]
    resources :outlook_webhooks, only: [:create]
    resources :email_mappings, only: [:show]
    resources :link_mappings, only: [:show]

    resources :email_templates, only: [:create, :show, :update], path: 'email-templates' do
      member do
        post :activate
        post :deactivate
        get "attachments/:attachment_id", to: "email_templates#download_attachment"
        get ':entity_type/:entity_id', to: 'email_templates#details_with_entity'
      end

      collection do
        post :search
        get :variables
        get :lookup
      end
    end
  end

  namespace :v2 do
    resources :email_threads, only: [], path: 'email-threads' do
      post :search, on: :collection
    end
    get '/email-templates/variables', to: "email_templates#variables"
  end
end
