LOOKUP_LEAD = 'lead'.freeze
LOOKUP_DEAL = 'deal'.freeze
LOOKUP_CONTACT = 'contact'.freeze
LOOKUP_COMPANY = 'company'.freeze
LOOKUP_USER = 'user'.freeze
LOOKUP_CUSTOM_EMAIL = 'customemail'.freeze
LOOKUP_TIMEZONE = 'timezone'.freeze
LOOKUP_CALL = 'call_log'.freeze
LOOKUP_MEETING = 'meeting'.freeze
LOOKUP_TASK = 'task'.freeze
LOOKUP_TYPES = [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT, LOOKUP_COMPANY, LO<PERSON><PERSON>_USER, <PERSON>O<PERSON><PERSON>_TIMEZONE, LOOKUP_CUSTOM_EMAIL]
TENANT = 'tenant'.freeze
USER_EXCHANGE = "ex.iam"
EMAIL_EXCHANGE = "ex.email"
SCHEDULER_EXCHANGE = "ex.scheduler"

DAILY_3AM_SCHEDULER_EVENT = 'scheduler.3am'
DAILY_3AM_SCHEDULER_QUEUE = 'q.scheduler.3am.email'

WORKFLOW_EXCHANGE = 'ex.workflow'
LEAD_EXCHANGE = 'ex.sales'.freeze
CONTACT_EXCHANGE = 'ex.sales'.freeze
DEAL_EXCHANGE = 'ex.deal'.freeze
AUTO_DISCONNECTED = 'AUTO'.freeze
UNAUTHORIZATION_DISCONNECT = 'UNAUTHORIZATION'.freeze
LIMIT_EXCEEDED_DISCONNECT = 'LIMIT_EXCEEDED'.freeze
EMAIL_OAUTH = 'emailOauth'.freeze
EMAIL_CLIENT = 'emailClient'.freeze

LEAD_OWNER_UPDATED_EVENT = 'sales.lead.owner_updated'.freeze
LEAD_OWNER_UPDATED_QUEUE = 'q.sales.lead.owner_updated.emails'.freeze
CONTACT_OWNER_UPDATED_EVENT = 'sales.contact.owner_updated'.freeze
CONTACT_OWNER_UPDATED_QUEUE = 'q.sales.contact.owner_updated.emails'.freeze
DEAL_OWNER_UPDATED_EVENT = 'deal.reassigned'.freeze
DEAL_OWNER_UPDATED_QUEUE = 'q.deal.reassigned.emails'.freeze

LEAD_NAME_UPDATED_EVENT = 'sales.lead.name.updated'.freeze
LEAD_NAME_UPDATED_QUEUE = 'q.sales.lead.name.updated.emails'.freeze
CONTACT_NAME_UPDATED_EVENT = 'contact.name.updated'.freeze
CONTACT_NAME_UPDATED_QUEUE = 'q.contact.name.updated.emails'.freeze

CONNECTED_ACCOUNT_SYNC_EVENT = "iam.emailOauth.connected"
CUSTOM_CONNECTED_ACCOUNT_SYNC_EVENT = "iam.emailClient.connected"
DEACTIVATE_CONNECTED_ACCOUNT_EVENT = "iam.emailOauth.disconnected"
DEACTIVATE_CUSTOM_CONNECTED_ACCOUNT_EVENT = "iam.emailClient.disconnected"
GMAIL_WEBHOOK_EVENT = "email.gmail.webhook"
SMTP_ACCOUNT_DISCONNECT_EVENT = "email.disconnect.emailClient"
IMAP_WEBHOOK_EVENT = "email.imap.webhook"
IMAP_SENT_WEBHOOK_EVENT = "email.imap.sent.webhook"
OUTLOOK_WEBHOOK_EVENT = "email.outlook.webhook"
GMAIL_WEBHOOK_QUEUE = "q.email.gmail.webhook"
OUTLOOK_WEBHOOK_QUEUE = "q.email.outlook.webhook"
IMAP_WEBHOOK_QUEUE = "q.email.imap.webhook"
IMAP_SENT_WEBHOOK_QUEUE = "q.email.imap.sent.webhook"
EMAIL_OAUTH_DISCONNECTED = 'email.oauth.disconnected'.freeze
EMAIL_CLIENT_DISCONNECTED = 'email.client.disconnected'.freeze
EMAIL_LOOKUP_UPDATED = 'email.lookup.updated'.freeze

WORKFLOW_SEND_EMAIL_EVENT = 'workflow.email.send'
GMAIL_WEBHOOK_SCHEDULER_REFRESH_EVENT = "scheduler.email.webhook.refresh"
GMAIL_WEBHOOK_SCHEDULER_REFRESH_QUEUE = "q.scheduler.email.webhook.refresh"
LEAD_DELETED_QUEUE = 'q.emails.sales.lead.deleted'
CONTACT_DELETED_QUEUE = 'q.emails.sales.contact.deleted'
DEAL_DELETED_QUEUE = 'q.emails.deal.deleted'.freeze

WORKFLOW_SEND_EMAIL_QUEUE = 'q.workflow.email.send'
USAGE_SCHEDULER_EVENT = "scheduler.collect.usage"
USAGE_SCHEDULER_QUEUE = "q.email.scheduler.collect.usage"
USAGE_PUBLISHER_EVENT = "tenant.usage.collected"
USAGE_PUBLISHER_QUEUE = "q.email.tenant.usage.collected"
USAGE_LIMIT_CHANGED_EVENT = "usage.limit.changed"
USAGE_LIMIT_CHANGED_QUEUE = "q.email.usage.limit.changed"

USER_NAME_UPDATED_EVENT = "user.name.updated"
USER_UPDATED_V2_EVENT = 'user.updated.v2'

CONNECTED_ACCOUNT_SYNC_QUEUE = "q.iam.emailOauth.connected"
CUSTOM_CONNECTED_ACCOUNT_SYNC_QUEUE = "q.iam.emailClient.connected"
DEACTIVATE_CONNECTED_ACCOUNT_QUEUE = "q.iam.emailOauth.disconnected"
DEACTIVATE_CUSTOM_CONNECTED_ACCOUNT_QUEUE = "q.iam.emailClient.disconnected"
USER_NAME_UPDATED_QUEUE = "q.iam.user.name.updated"
USER_UPDATED_V2_QUEUE = "q.email.user.updated.v2"

GOOGLE_PROVIDER = 'GOOGLE'
MICROSOFT_PROVIDER = 'MICROSOFT'
CUSTOM_PROVIDER = 'CUSTOM'
JWT_KEY = 'test'
LEAD_DELETED_EVENT = 'sales.lead.deleted'
CONTACT_DELETED_EVENT = 'sales.contact.deleted'.freeze
DEAL_DELETED_EVENT = 'deal.deleted'.freeze

EMAIL_RECEIVED_RELATED_TO_ENTITY_EVENT = 'email.received.relatedTo.entity'

LEAD_EXCLUDED_FIELDS = ['forecastingType', 'isNew', 'recordActions', 'metaData', 'dnd', 'photoUrls', 'convertedAt', 'timezone', 'requirementCurrency']
CONTACT_EXCLUDED_FIELDS =  ['forecastingType', 'isNew', 'recordActions', 'metaData', 'dnd', 'timezone', 'stakeholder']
DEAL_EXCLUDED_FIELDS = ['forecastingType', 'isNew', 'recordActions', 'metaData', 'price', 'quantity', 'discount', 'units']
CALL_LOG_EXCLUDED_FIELDS = %w[startTime notes entityType recordActions tenantId].freeze
TENANT_EXCLUDED_FIELDS = %w[createdAt updatedAt createdBy updatedBy recordActions metaData dateFormat logo active confirmed confirmedAt planName hasLogo].freeze
MEETING_EXCLUDED_FIELDS = %w[allDay].freeze
TASK_EXCLUDED_FIELDS = %w[createdAt updatedAt createdViaId createdViaName createdViaType updatedViaId updatedViaName updatedViaType metaData recordActions tenantId].freeze

USER_ALLOWED_FIELDS = %w[salutation firstName lastName phoneNumbers email designation currency profileId signature timezone id].freeze
LEAD_USER_LOOKUP_FIELDS = %w[createdBy updatedBy convertedBy importedBy ownerId].freeze
CONTACT_USER_LOOKUP_FIELDS = %w[createdBy updatedBy importedBy ownerId].freeze
DEAL_USER_LOOKUP_FIELDS = %w[createdBy updatedBy ownedBy].freeze
CALL_LOG_USER_LOOKUP_FIELDS = %w[createdBy updatedBy owner].freeze
MEETING_USER_LOOKUP_FIELDS = %w[createdBy updatedBy owner cancelledBy conductedBy].freeze
TASK_USER_LOOKUP_FIELDS = %w[assignedTo ownerId createdBy updatedBy].freeze

GRAPH_HOST = 'https://graph.microsoft.com'.freeze

CREATE_ACTION = 'create'
EMAIL_CREATED = "email.created"
DELETE_ACTION = 'delete'
EMAIL_DELETED = 'email.deleted'
EMAIL_OPENED_NOTIFY_USER = 'email.opened.notify.user'
LINK_CLICKED = 'email.link.clicked.notify.user'
OPEN_ACTION = 'opened'
LINK_CLICKED_ACTION = 'link_clicked'
EMAIL_UPDATED_V2 = 'email.updated.v2'
EMAIL_DELETED_WORKFLOW_V2 = 'email.deleted.workflow.v2'
EMAIL_CREATED_WORKFLOW_V2 = 'email.created.workflow.v2'
EMAIL_OPENED = 'email.opened'
EMAIL_CLICKED = 'email.clicked'

BASE64_SRC_REGEX = /(src="data:([-\w]+\/[-\w\+\.]+)?;base64,([^"]+)")/i

DIRECTION = 'direction'
SENT = 'sent'
RECEIVED = 'received'

if Rails.env.development? || Rails.env.test?
  SERVICE_IAM = "http://localhost:8081"
  SERVICE_SEARCH = "http://localhost:8083"
  SERVICE_DEAL = "http://localhost:8090"
  SERVICE_SALES = "http://localhost:8082"
  SERVICE_CONFIG = "http://localhost:8089"
  SERVICE_MEETING = 'http://localhost:8093'
  SERVICE_EMAIL_ENGINE = 'http://localhost:3000'
  S3_EMAIL_BUCKET = "qa-emails"
  S3_EMAIL_TEMPLATE_BUCKET = "qa-email-templates"
  APP_KYLAS_HOST = "http://localhost"
  GMAIL_SUBSCRIPTION_TOPIC = "projects/kylas-dev/topics/receive"
  API_HOST = "http://localhost:3000"
  SERVICE_CALL = 'http://localhost:3000'
  SERVICE_PRODUCTIVITY = 'http://localhost:8087'
elsif Rails.env.staging?
  SERVICE_IAM = "http://sd-iam"
  SERVICE_SEARCH = "http://sd-search"
  SERVICE_DEAL = "http://sd-deal"
  SERVICE_SALES = "http://sd-sales"
  SERVICE_CONFIG = "http://sd-config"
  SERVICE_MEETING = "http://sd-meetings"
  APP_KYLAS_HOST = "https://app-qa.sling-dev.com"
  API_HOST = ENV['API_HOST']
  SERVICE_EMAIL_ENGINE = 'http://sd-email-engine'
  S3_EMAIL_BUCKET = ENV['S3_EMAIL_BUCKET']
  S3_EMAIL_TEMPLATE_BUCKET = ENV['S3_EMAIL_TEMPLATE_BUCKET']
  SERVICE_CALL = 'http://sd-call'
  SERVICE_PRODUCTIVITY = 'http://sd-productivity'
elsif Rails.env.production?
  SERVICE_IAM = "http://sd-iam"
  SERVICE_SEARCH = "http://sd-search"
  SERVICE_DEAL = "http://sd-deal"
  SERVICE_SALES = "http://sd-sales"
  SERVICE_CONFIG = "http://sd-config"
  SERVICE_MEETING = "http://sd-meetings"
  APP_KYLAS_HOST = "https://app.kylas.io"
  API_HOST = ENV['API_HOST']
  S3_EMAIL_BUCKET = ENV['S3_EMAIL_BUCKET']
  S3_EMAIL_TEMPLATE_BUCKET = ENV['S3_EMAIL_TEMPLATE_BUCKET']
  SERVICE_CALL = 'http://sd-call'
  SERVICE_PRODUCTIVITY = 'http://sd-productivity'
end

RELATIVE_FILTERS_LIST = [
  'today', 'yesterday', 'tomorrow',
  'last_seven_days', 'next_seven_days',
  'last_fifteen_days', 'next_fifteen_days',
  'last_thirty_days', 'next_thirty_days',
  'current_week', 'last_week', 'next_week',
  'current_month', 'last_month', 'next_month',
  'current_quarter', 'last_quarter', 'next_quarter',
  'current_year', 'last_year', 'next_year',
  'week_to_date', 'month_to_date', 'quarter_to_date', 'year_to_date',
  'before_current_date_and_time', 'after_current_date_and_time'
].freeze

DEFAULT_IST_TIMEZONE = 'Asia/Calcutta'.freeze
USER_FIELDS_RULE_IDS = %w[userFields sentByFields receivedByFields].freeze
