require 'connection_pool'
class RabbitmqConnection
  @@channel = nil
  @@gmail_webhook_channel = nil

  def self.get_channel routing_key
    if routing_key.eql? GMAIL_WEBHOOK_EVENT
      return @@gmail_webhook_channel if @@gmail_webhook_channel.present?
      rabbitmq_connection = Bunny.new(
        host: <PERSON><PERSON><PERSON>['RABBITMQ_HOST'],
        user: <PERSON><PERSON><PERSON>['RABBITMQ_USER'],
        pass: <PERSON>NV['RABBITMQ_PASSWORD'],
        vhost: ENV['RABBITMQ_VIRTUAL_HOST']).start
      @@gmail_webhook_channel = rabbitmq_connection.create_channel(nil, 5)
      @@gmail_webhook_channel.prefetch(1)
      @@gmail_webhook_channel
    else
      routing_key = routing_key.gsub('.','_')
      if RabbitmqConnection.class_variable_defined?("@@#{routing_key}")
        return  self.class_variable_get("@@#{routing_key}")
      else
        rabbitmq_connection = Bunny.new(
          host: <PERSON><PERSON><PERSON>['RABBITMQ_HOST'],
          user: <PERSON><PERSON><PERSON>['RABBITMQ_USER'],
          pass: <PERSON><PERSON><PERSON>['RABBITMQ_PASSWORD'],
          vhost: <PERSON><PERSON><PERSON>['RABBITMQ_VIRTUAL_HOST']).start
        channel = rabbitmq_connection.create_channel(nil, 1)
        channel.prefetch(1)
        self.class_variable_set("@@#{routing_key}", channel)
        self.class_variable_get("@@#{routing_key}")
      end
    end
  end

  def self.get_exchange exchange_name, routing_key = 'email'
    get_channel(routing_key).topic(exchange_name, durable: true)
  end

  def self.subscribe exchange_name, routing_key, queue
    exchange = get_exchange(exchange_name, routing_key)
    channel = get_channel(routing_key)
    channel.basic_qos(1)
    args = {
      Bunny::Queue::XArgs::QUEUE_TYPE => "quorum"
    }

    channel.queue(queue, auto_delete: false, durable: true, arguments: args).bind(exchange, routing_key: routing_key).subscribe(:manual_ack => true) do |delivery_info, metadata, payload|
      begin
        yield payload
      rescue Exception => e
        Rails.logger.info "RabbitMQ exception: routing_key: #{routing_key} | message: #{e.to_s} | #{e.backtrace}"
        if e.to_s.downcase.include?('activerecord') || e.to_s.downcase.include?('connection')
          ActiveRecord::Base.connection.close if ActiveRecord::Base.connection
          ActiveRecord::Base.connection_pool.with_connection do
            yield payload
          end
        end
      ensure
        ActiveRecord::Base.clear_active_connections!
        channel.acknowledge(delivery_info.delivery_tag, false)
      end
    end
  end
end
