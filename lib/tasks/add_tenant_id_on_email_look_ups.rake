namespace :emails do
  desc 'Add tenant id on existing email look ups'
  task :add_tenant_id_on_email_look_ups, [:from_tenant_id, :to_tenant_id] => [:environment] do |_task, args|
    from_tenant_id = args.from_tenant_id.to_i
    to_tenant_id = args.to_tenant_id.to_i

    if from_tenant_id.positive? && to_tenant_id.positive? && from_tenant_id <= to_tenant_id
      (from_tenant_id..to_tenant_id).to_a.each do |tenant_id|
        begin
          Email.where(tenant_id: tenant_id).select(:id)
                .find_in_batches do |email_batch|
                  email_ids = email_batch.pluck(:id)

                  EmailLookUp.where(email_id: email_ids, tenant_id: nil).update_all(tenant_id: tenant_id)
                end
          puts "Email Look Ups updated for tenant #{tenant_id}"
        rescue => e
          puts "Error when updating email look ups for tenant id #{tenant_id}. Message: #{e.message}"
        end
      end
    else
      puts "Invalid tenant ids From Tenant Id #{from_tenant_id} To Tenant Id #{to_tenant_id}"
    end
  end
end

# RAILS_ENV=<Environment> bundle exec rake emails:add_tenant_id_on_email_look_ups[<from_tenant_id>,<to_tenant_id>]
