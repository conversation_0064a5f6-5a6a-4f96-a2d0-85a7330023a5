namespace :emails do
  desc 'Set status on existing emails'

  task :set_status_on_existing_received_emails, %i[from_tenant_id to_tenant_id] => [:environment] do |_task, args|
    from_tenant_id = args.from_tenant_id.to_i
    to_tenant_id = args.to_tenant_id.to_i

    if from_tenant_id.positive? && to_tenant_id.positive? && from_tenant_id <= to_tenant_id
      (from_tenant_id..to_tenant_id).to_a.each do |tenant_id|
        Email.where(tenant_id: tenant_id, status: nil, direction: :received).update_all(status: :received)
        puts "Emails updated for tenant #{tenant_id}"
      rescue StandardError => e
        puts "Error when updating emails for tenant id #{tenant_id}. Message: #{e.message}"
      end
    else
      puts "Invalid tenant ids From Tenant Id #{from_tenant_id} To Tenant Id #{to_tenant_id}"
    end
  end

  task :set_status_on_existing_sent_emails, %i[from_tenant_id to_tenant_id] => [:environment] do |_task, args|
    from_tenant_id = args.from_tenant_id.to_i
    to_tenant_id = args.to_tenant_id.to_i

    if from_tenant_id.positive? && to_tenant_id.positive? && from_tenant_id <= to_tenant_id
      (from_tenant_id..to_tenant_id).to_a.each do |tenant_id|
        Email.where(tenant_id: tenant_id, status: nil, direction: :sent).update_all(status: :sent)
        puts "Emails updated for tenant #{tenant_id}"
      rescue StandardError => e
        puts "Error when updating emails for tenant id #{tenant_id}. Message: #{e.message}"
      end
    else
      puts "Invalid tenant ids From Tenant Id #{from_tenant_id} To Tenant Id #{to_tenant_id}"
    end
  end
end

# RAILS_ENV=<Environment> bundle exec rake emails:set_status_on_existing_received_emails[<from_tenant_id>,<to_tenant_id>]
# RAILS_ENV=<Environment> bundle exec rake emails:set_status_on_existing_sent_emails[<from_tenant_id>,<to_tenant_id>]
