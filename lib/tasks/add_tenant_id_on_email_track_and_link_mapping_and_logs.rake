namespace :emails do
  desc 'Add tenant id on track mapping, link mapping, email track logs and email link logs'
  task :add_tenant_id_on_email_track_and_link_mapping_and_logs, [:from_tenant_id, :to_tenant_id] => [:environment] do |_task, args|
    from_tenant_id = args.from_tenant_id.to_i
    to_tenant_id = args.to_tenant_id.to_i

    if from_tenant_id.positive? && to_tenant_id.positive? && from_tenant_id <= to_tenant_id
      (from_tenant_id..to_tenant_id).to_a.each do |tenant_id|
        begin
          tenant_emails_ids = Email.where(tenant_id: tenant_id).pluck(:id)
          offset = 0
          limit = 25_000
          loop do
            email_ids_batch = tenant_emails_ids.slice(offset, limit)
            break if email_ids_batch.blank?
            TrackMapping.where(email_id: email_ids_batch, tenant_id: nil).update_all(tenant_id: tenant_id)
            LinkMapping.where(email_id: email_ids_batch, tenant_id: nil).update_all(tenant_id: tenant_id)
            EmailTrackLog.where(email_id: email_ids_batch, tenant_id: nil).update_all(tenant_id: tenant_id)
            EmailLinkLog.where(email_id: email_ids_batch, tenant_id: nil).update_all(tenant_id: tenant_id)
            puts "Tenant ID #{tenant_id} Offset #{(offset)} Limit #{limit} : Batch Completed"
            offset += limit
          end
          puts "Tenant ID updated on email tracking tables for tenant id #{tenant_id}"
        rescue => e
          puts "Error when updating tenant id on email tracking tables for tenant id #{tenant_id}. Message: #{e.message}"
        end
      end
    else
      puts "Invalid tenant ids From Tenant Id #{from_tenant_id} To Tenant Id #{to_tenant_id}"
    end
  end
end

# RAILS_ENV=<Environment> bundle exec rake emails:add_tenant_id_on_email_track_and_link_mapping_and_logs[<from_tenant_id>,<to_tenant_id>]
