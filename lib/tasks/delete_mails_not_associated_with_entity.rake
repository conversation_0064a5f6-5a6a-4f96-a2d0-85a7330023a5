task :delete_mails_not_associated_with_entity, [:from_id, :to_id, :number_of_days] => :environment do |task, args|
  from_id = args['from_id'].to_i
  to_id = args['to_id'].to_i
  number_of_days = args['number_of_days'].to_i
  number_of_days = 5 if number_of_days.zero?
  (from_id..to_id).each do |id|
    emails_by_tenant_id =  Email.where("created_at > ?", Date.today - 5.days).where(tenant_id: id).count
    p "Tenant id: #{id} | total: #{emails_by_tenant_id}"
    Email.where("created_at > ?", Date.today - 5.days).where(tenant_id: id).find_in_batches.each do |emails|
      emails.each do |email|
        p email.id
        original_recipients = email.to.to_a + email.cc.to_a + [email.from] + email.bcc.to_a
        look_ups = EmailLookUp.joins(:look_up).where(email_id: email.id).where.not("entity like 'user_%' or entity = 'customemail'").select("look_ups.email as e").collect(&:e)
        common_recipients = original_recipients.intersection look_ups
        EmailsToBeDeleted.create(email_id: email.id, tenant_id: id) if common_recipients.blank?
      end
    end
    p "Tenant id: #{id} | total: #{emails_by_tenant_id} | Emails To be deleted: #{EmailsToBeDeleted.where(tenant_id: id).count}"
  end
end
