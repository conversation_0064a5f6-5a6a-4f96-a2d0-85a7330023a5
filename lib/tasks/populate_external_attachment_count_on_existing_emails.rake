namespace :emails do
  desc 'Add external attachment count on emails'
  task :populate_external_attachment_count, [:from_tenant_id, :to_tenant_id] => [:environment] do |_task, args|
    from_tenant_id = args.from_tenant_id.to_i
    to_tenant_id = args.to_tenant_id.to_i

    if from_tenant_id.positive? && to_tenant_id.positive? && from_tenant_id <= to_tenant_id
      (from_tenant_id..to_tenant_id).to_a.each do |tenant_id|
        begin
          Email.select(:id).where(tenant_id: tenant_id, external_attachment_count: 0).find_in_batches.with_index(1) do |email_ids_batch, index|
            email_thread_attachments_count = Attachment.select(:id).where(inline: false).joins(:email).where(emails: { tenant_id: tenant_id, id: email_ids_batch.pluck(:id) }).group('attachments.email_id').count
            if email_thread_attachments_count.present?
              Email.where(tenant_id: tenant_id, id: email_thread_attachments_count.keys).where("temp_table.eid = emails.id").update_all("external_attachment_count = temp_table.eac FROM (VALUES #{email_thread_attachments_count.map { |em_id, em_ac| "(#{em_id}, #{em_ac})" }.join(',')}) AS temp_table(eid, eac)")
            end
            puts "Email external attachment count updated for tenant #{tenant_id} Batch #{index}"
          end
        rescue => e
          puts "Error when updating external attachment count on email for tenant id #{tenant_id}. Message: #{e.message}"
        end
      end
    else
      puts "Invalid tenant ids From Tenant Id #{from_tenant_id} To Tenant Id #{to_tenant_id}"
    end
  end
end

# RAILS_ENV=<Environment> bundle exec rake emails:populate_external_attachment_count[<from_tenant_id>,<to_tenant_id>]
