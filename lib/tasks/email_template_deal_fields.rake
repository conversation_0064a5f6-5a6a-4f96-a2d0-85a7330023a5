task :email_template_deal_fields => :environment do |task, args|
  vars = { 'associatedCompany' => 'company', 'owner' => 'ownedBy'}
  vars.each do |old_var, new_var|
    puts "processing variable: #{old_var}"
    EmailTemplate.where(category: 'deal').where("body like '%#{old_var}%'").each do |template|
      body = template.body.gsub(/{{#{old_var}/, "{{#{new_var}")
      Rails.logger.info "-------- updating body of #{template.id} -------------"
      Rails.logger.info "OLD BODY: #{template.body}"
      Rails.logger.info "NEW BODY: #{template.body}"
      template.update(body: body)
    end

    EmailTemplate.where(category: 'deal').where("subject like '%#{old_var}%'").each do |template|
      subject = template.subject.gsub(/{{#{old_var}/, "{{#{new_var}")
      Rails.logger.info "-------- updating subject of #{template.id} -------------"
      Rails.logger.info "OLD SUBJECT: #{template.subject}"
      Rails.logger.info "NEW SUBJECT: #{template.subject}"
      template.update(subject: subject)
    end
  end
end
