task :delete_orphans, [:entity, :ids] => :environment do |task, args|
  entity = args[:entity]
  ids = args[:ids].split(' ').map(&:to_i)
  p entity
  entity_ids =  LookUp.where("entity like ?", "#{entity}_%").
    where("created_at < ?", DateTime.now - 30.minutes).
    collect(&:entity).
    map{|a| a.split('_').last.to_i}.uniq

  deleted_ids = entity_ids - ids
  p "Lead ids in DB: #{entity_ids.count}"
  p "ids to be deleted #{ids.count}"
  p "deleted_ids : #{deleted_ids}"
  deleted_ids.each do |id|
    lookup = LookUp.where("entity like ?", "#{entity}_#{id}").last
    data = {entity_id: id, tenant_id: lookup.tenant_id, user_id: 'dummy'}
    case entity
    when 'lead'
      UpdateEmailForEntityDeleteEvent.call(id, lookup.tenant_id, "dummy", LOOKUP_LEAD)
    when 'contact'
      UpdateEmailForEntityDeleteEvent.call(id, lookup.tenant_id, "dummy", LOOKUP_CONTACT)
    when 'deal'
      UpdateEmailForEntityDeleteEvent.call(id, lookup.tenant_id, "dummy", LOOKUP_DEAL)
    else
      p "invalid entity"
    end
  end
  p "task executed"
end
